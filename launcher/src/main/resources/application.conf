hawtio {
  enable = true
  version = 2.16.3
  auth {
    enable = false
    loginConfigFile = "launcher/src/main/resources/hawtio-login.config"
  }
}

config {
  service.type = ["RD", "RS", "MD", "MS"]
  sdNotify {
    enabled = true
  }
}

rd {
  kafka {
    consumer {
      fip {
        kafka-clients.bootstrap.servers = "localhost:9092"

        topics: [{
          topic: "ppb.stream.pp.instructions.tennis"
          topic: ${?FIP_TENNIS_TOPIC}
        }, {
          topic: "ppb.stream.pp.instructions.football"
          topic: ${?FIP_FOOTBALL_TOPIC}
        }, {
          topic: "ppb.stream.pp.instructions.basketball"
          topic: ${?FIP_BASKETBALL_TOPIC}
        }, {
          topic: "ppb.stream.pp.instructions.racing"
          topic: ${?FIP_RACING_TOPIC}
        }, {
          topic: "ppb.stream.pp.instructions.others"
          topic: ${?FIP_OTHERS_TOPIC}
        }]
      }
      pbc {
        kafka-clients.bootstrap.servers = "localhost:9092"

        topics: [{
          topic: "stream.type.hierachy.bootstrap.demux"
          topic: ${?GPL_BOOTSTRAP_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.tennis.demux"
          topic: ${?PBC_TENNIS_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.football.demux"
          topic: ${?PBC_FOOTBALL_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.basketball.demux"
          topic: ${?PBC_BASKETBALL_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.racing.demux"
          topic: ${?PBC_RACING_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.others.demux"
          topic: ${?PBC_OTHERS_TOPIC}
        }]
      }
      ipma {
        topics: [{
          topic: "powers.stream.pp.ipma.overrides.tennis"
          topic: ${?IPMA_TENNIS_TOPIC}
        }, {
          topic: "powers.stream.pp.ipma.overrides.football"
          topic: ${?IPMA_FOOTBALL_TOPIC}
        }, {
          topic: "powers.stream.pp.ipma.overrides.basketball"
          topic: ${?IPMA_BASKETBALL_TOPIC}
        }, {
          topic: "powers.stream.pp.ipma.overrides.racing"
          topic: ${?IPMA_RACING_TOPIC}
        }, {
          topic: "powers.stream.pp.ipma.overrides.others"
          topic: ${?IPMA_OTHERS_TOPIC}
        }]
        kafka-clients.bootstrap.servers = "localhost:9092"
      }
      gma {
        topics: [{
          topic: "powers.stream.pp.gma.overrides.tennis"
          topic: ${?GMA_TENNIS_TOPIC}
        }, {
          topic: "powers.stream.pp.gma.overrides.football"
          topic: ${?GMA_FOOTBALL_TOPIC}
        }, {
          topic: "powers.stream.pp.gma.overrides.basketball"
          topic: ${?GMA_BASKETBALL_TOPIC}
        }, {
          topic: "powers.stream.pp.gma.overrides.racing"
          topic: ${?GMA_RACING_TOPIC}
        }]

        kafka-clients.bootstrap.servers = "localhost:9092"
      }
      gth {
        topics: [{
          topic: "type.hierarchy.paddy_power"
          topic: ${?GTH_TOPIC}
        }]
        kafka-clients.bootstrap.servers = "localhost:9092"
      }
    }

    demux-pbc-service {
      producer.kafka-clients.bootstrap.servers = "localhost:9092"
      consumer {
        kafka-clients.bootstrap.servers = "localhost:9092"
        topics: [{
          topic: "stream.type.hierachy.bootstrap"
          topic: ${?DEMUX_PBC_GPL_BOOTSTRAP_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.tennis"
          topic: ${?DEMUX_PBC_TENNIS_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.football"
          topic: ${?DEMUX_PBC_FOOTBALL_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.basketball"
          topic: ${?DEMUX_PBC_BASKETBALL_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.racing"
          topic: ${?DEMUX_PBC_RACING_TOPIC}
        }, {
          topic: "powers.stream.pp.competition.others"
          topic: ${?DEMUX_PBC_OTHERS_TOPIC}
        }]
      }
    }

    producer.kafka-clients.bootstrap.servers = "localhost:9092"
    md-by-rd-producer.kafka-clients.bootstrap.servers = "localhost:9092"
    md-by-rd-producer.enableOnBootstrap = false
    md-by-rd-producer.topics = []
    sep-producer.kafka-clients.bootstrap.servers = "localhost:9092"
    overrides-producer.kafka-clients.bootstrap.servers = "localhost:9092"

    error-topic.kafka-clients.bootstrap.servers = "localhost:9092"

    subscription {
      request.common-kafka-clients.bootstrap.servers = "localhost:9092"
      notification.common-kafka-clients.bootstrap.servers = "localhost:9092"
      broadcast.common-kafka-clients.bootstrap.servers = "localhost:9092"
    }


  }
}
include required("cross-dcs.conf")
include required("demux-gpl.conf")

md {
  kafka {
    consumer {
      gth {
        kafka-clients.bootstrap.servers = "localhost:9092"

        topics: [{
          topic: "type.hierarchy.local"
          topic: ${?GTH_INBOUND_TOPIC}
        }]
      }
      fip {
        kafka-clients.bootstrap.servers = "localhost:9092"

        topics: [{
          topic: "ppb.stream.pp.instructions.tennis"
          topic: ${?FIP_TENNIS_TOPIC}
        }, {
          topic: "ppb.stream.pp.instructions.football"
          topic: ${?FIP_FOOTBALL_TOPIC}
        }, {
          topic: "ppb.stream.pp.instructions.basketball"
          topic: ${?FIP_BASKETBALL_TOPIC}
        }, {
          topic: "ppb.stream.pp.instructions.racing"
          topic: ${?FIP_RACING_TOPIC}
        }, {
          topic: "ppb.stream.pp.instructions.others"
          topic: ${?FIP_OTHERS_TOPIC}
     }]
      }
    }

    producer {
      kafka-clients.bootstrap.servers = "localhost:9092"
    }

    error-topic {
      kafka-clients.bootstrap.servers = "localhost:9092"
    }

    subscription {
      request {
        common-kafka-clients.bootstrap.servers = "localhost:9092"
      }
      notification {
        common-kafka-clients.bootstrap.servers = "localhost:9092"
      }
      broadcast {
        common-kafka-clients.bootstrap.servers = "localhost:9092"
      }
    }
  }
}

rs {
  publishingEnabled = true
  kafka {
    consumer {
      rd {
        topics = [
          {
            topic: "catalog.risk.domain.pp.others.delta"
            topic: ${?RD_OTHER_TOPIC}
          },
          {
            topic: "catalog.risk.domain.pp.football.delta"
            topic: ${?RD_SOCCER_TOPIC}
          },
          {
            topic: "catalog.risk.domain.pp.tennis.delta"
            topic: ${?RD_TENNIS_TOPIC}
          },
          {
            topic: "catalog.risk.domain.pp.racing.delta"
            topic: ${?RD_HORSE_RACING_TOPIC}
          },
          {
            topic: "catalog.risk.domain.pp.basketball.delta"
            topic: ${?RD_BASKETBALL_TOPIC}
          }
        ]
        kafka-clients.bootstrap.servers = "localhost:9092"
      }
    }

    producer.kafka-clients.bootstrap.servers = "localhost:9092"

    error-topic.kafka-clients.bootstrap.servers = "localhost:9092"
  }
  jmx {
    enable = true
    operations {
      hazelcast.enable = true
      actorState.enable = true
    }
  }
  http {
    askTimeout = 3000
    cacheEnabled = true
    cacheClusterEnabled = false
    cacheClusterName = "localhost"
    cacheClusterAddress = "localhost"
    cacheTTLDurationSec = 180
    cacheClusterNodeList = "node1;node2;node3"
    cacheMapName = "default"
  }
}
ms {
  publishingEnabled = true
  kafka {
    consumer {
      md {
        topics = [
          {
            topic: "catalog.market.domain.pp.others.delta"
            topic: ${?MD_OTHER_TOPIC}
          },
          {
            topic: "catalog.market.domain.pp.football.delta"
            topic: ${?MD_SOCCER_TOPIC}
          },
          {
            topic: "catalog.market.domain.pp.tennis.delta"
            topic: ${?MD_TENNIS_TOPIC}
          },
          {
            topic: "catalog.market.domain.pp.racing.delta"
            topic: ${?MD_HORSE_RACING_TOPIC}
          },
          {
            topic: "catalog.market.domain.pp.basketball.delta"
            topic: ${?MD_BASKETBALL_TOPIC}
          }
        ]
        kafka-clients.bootstrap.servers = "localhost:9092"
      }
    }

    producer {
      kafka-clients.bootstrap.servers = "localhost:9092"
    }

    error-topic {
      kafka-clients.bootstrap.servers = "localhost:9092"
    }
  }
}

rd {
  jmx {
    enable = true
    operations {
      actorState.enable = true
    }
  }
}

md {
  jmx {
    enable = true
    operations {
      actorState.enable = true
    }
  }
}

pekko.cluster.serialize-messages = on
pekko.cluster.jmx.enabled = on
pekko.cluster.seed-nodes = ["pekko://PCSASystem@127.0.0.1:17355"]
pekko.remote.artery.canonical.hostname = "127.0.0.1"

include required("inmemory-persistence.conf")
