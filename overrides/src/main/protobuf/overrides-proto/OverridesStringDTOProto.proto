syntax = "proto3";

package com.flutter.pcsa.common.overrides.contract.proto;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  flat_package: true
};

message OverridesStringDTOProto {
  map<string, OverrideStringDTOProto> overrides = 1;
}

message OverrideStringDTOProto {
  google.protobuf.StringValue instanceOverride = 1;
  optional string flag = 2;
}