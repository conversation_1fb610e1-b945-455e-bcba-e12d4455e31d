@RDStreamDeltas @Ignore
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: Feeds flow - RD processes fields for MD temporarily

  This feature intends to temporarily verify that the messages coming from GTH/FIP are correctly processed
  by all upper hierarchy entities and RD publishes some MD fields to Risk Delta stream. Fields are:
  - name for all entities
  - status and start time for the event


  Scenario: GTH flow of messages are correctly processed and the fields are published to the delta stream

    * random Int value is stored with index "superclassId"
    * random Int value is stored with index "subclassId"
    * random Int value is stored with index "eventTypeId"
    * random Int value is stored with index "eventId"
    * random Int value is stored with index "marketTypeId"
    * random Int value is stored with index "marketTypeLinkId"
    * value "CREATE" is stored with index "action"

    * value "Tennis" is stored with index "superclassName"

    # Create random Superclass
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Superclass.json"
    When Kafka message with key "superclassId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TEMP" topic
    And "Kafka" response matches
      | $.action                              | CREATE                            |
      | $.superclass.id                       | urn:sbk:pc:spc:gpd:superclassId |
      | $.superclass.name.instances.ALL.value | superclassName                  |

    * value "|Tennis|" is stored with index "subclassName"

    # Create random Subclass
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Subclass.json"
    When Kafka message with key "subclassId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And "Kafka" response matches
      | $.subscribeSuperclass.superclassId | urn:sbk:pc:spc:gpd:superclassId |
      | $.subscribeSuperclass.subclassId   | urn:sbk:pc:sbc:gpd:subclassId   |
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And "Kafka" response matches
      | $.subscribers[0] | urn:sbk:pc:sbc:gpd:subclassId |
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_TEMP" topic

    And "Kafka" response matches
      | $.action                            | CREATE                            |
      | $.subclass.id                       | urn:sbk:pc:sbc:gpd:subclassId   |
      | $.subclass.superclassId             | urn:sbk:pc:spc:gpd:superclassId |
      | $.subclass.name.instances.ALL.value | subclassName                    |

    * value "|ATP Auckland|" is stored with index "eventTypeName"

    # Create random Event Type
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Event_Type.json"
    When Kafka message with key "eventTypeId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And "Kafka" response matches
      | $.subscribeSubclass.subclassId  | urn:sbk:pc:sbc:gpd:subclassId |
      | $.subscribeSubclass.eventTypeId | urn:sbk:pc:et:gpd:eventTypeId |
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And "Kafka" response matches
      | $.subscribers[0] | urn:sbk:pc:et:gpd:eventTypeId |
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_TEMP" topic
    And "Kafka" response matches
      | $.action                             | CREATE                          |
      | $.eventType.id                       | urn:sbk:pc:et:gpd:eventTypeId |
      | $.eventType.subclassId               | urn:sbk:pc:sbc:gpd:subclassId |
      | $.eventType.name.instances.ALL.value | eventTypeName                 |

    * value "|1st Set Total Games|" is stored with index "marketTypeName"

    #Send MarketType
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_MarketType.json"
    When Kafka message with key "marketTypeId" is published to "GTH_INPUT" topic

    #Send MarketTypeLink
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_MarketTypeLink.json"
    When Kafka message with key "eventTypeId_marketTypeId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TEMP" topic
    And "Kafka" response matches
      | $.action                                  | CREATE                                          |
      | $.marketTypeLink.id                       | urn:sbk:pc:mtl:gpd:eventTypeId_marketTypeId |
      | $.marketTypeLink.name.instances.ALL.value | marketTypeName                                |
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic


  Scenario: FIP flow of messages are correctly processed and the fields are published to the delta stream

    * random Int value is stored with index "marketId"
    * random Int value is stored with index "eventTypeId"
    * random Int value is stored with index "eventId"
    * random Int value is stored with index "selectionId1"
    * random Int value is stored with index "selectionId2"
    * "TENNIS" global type hierarchy from GTH is created
    * value "CREATE" is stored with index "action"

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_Create_Event_Without_sort.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    # then event applies MATCH as default value to sort
    Then consume "3" Kafka messages from "RD_OUTPUT_DELTA_TEMP" topic
    And "Kafka" response matches
      | $.action                     | CREATE                                                               |
      | $.event.id                   | urn:sbk:pc:e:gpd:eventId                                           |
      | $.event.eventTypeId          | urn:sbk:pc:et:gpd:eventTypeId                                      |
      | $.event.sort.instances       | Key Not Found                                                        |
      | $.event.sort.base            | Key Not Found                                                        |
      # TODO: Check the path and values for the Event name, stauts and start time
      | $.event.name.instances.ALL   | {"value":"Evan Bynoe v Alejandro Hayen","flag":"NONE"}               |
      | $.event.status.instances.ALL | Key Not Found                                                        |
      | $.event.startTime            | {"instances":{"ALL":{"value":"2024-07-17T18:00:00Z","flag":"NONE"}}} |
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic

    * value "UPDATE" is stored with index "action"

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_Create_Market_Without_Selections.json"
    When Kafka message with key "marketId" is published to "FIP_TENNIS" topic without headers
#    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
#    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_TEMP" topic
    And "Kafka" response matches
      | $.action                    | UPDATE                                 |
      | $.market.id                 | urn:sbk:pc:m:gpd:marketId            |
      | $.market.name.instances.ALL | {"value":"Head to Head","flag":"NONE"} |
      | $.market.selections         | Key Not Found                          |

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_Update_Market_Add_Selections.json"
    When Kafka message with key "marketId" is published to "FIP_TENNIS" topic without headers
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TEMP" topic
    And "Kafka" response matches
      | $.action                                  | UPDATE                                |
      | $.market.id                               | urn:sbk:pc:m:gpd:marketId           |
      | $.market.selections[0].id                 | urn:sbk:pc:s:gpd:selectionId1       |
      | $.market.selections[0].name.instances.ALL | {"value":"\|Gonzaga\|","flag":"NONE"} |
      | $.market.selections[1].id                 | urn:sbk:pc:s:gpd:selectionId2       |
      | $.market.selections[1].name.instances.ALL | {"value":"\|Indiana\|","flag":"NONE"} |