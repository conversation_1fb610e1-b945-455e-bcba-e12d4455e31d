@RDStreamDeltas @FIPFlow
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: FIP Flow - Event LayToLose

  This feature intends to verify that the field 'LayToLose' under Event from FIP flow is correctly processed
  and published to Risk Deltas stream
  # specific logic applied to this field shall be described here

  Background:
    * "TENNIS" global type hierarchy from GTH is created
    * unique long id is stored with index "eventId"

  Scenario: Multiple messages received from FIP for Event (CREATE, UPDATE, REFRESH and UPDATE without any changes) with layToLose value
  On CREATE, the value is persisted and published to client
  On UPDATE, the new value is persisted and published to client
  On REFRESH containing a different value, the new value is persisted and published to client
  On UPDATE with same value that is persisted, no message is produced

    # create is received with layToLose value = 50
    * value "50" is stored with index "layToLose"
    * value "CREATE" is stored with index "action"
    * value "REGULAR" is stored with index "updateType"

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_ESL_Event_Create_With_Event_Sort.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                    | CREATE                               |
      | $.event.id                  | urn:sbk:pc:e:gpd:eventId           |
      | $.event.subclassId          | urn:sbk:pc:sbc:gpd:13                |
      | $.event.eventTypeId         | urn:sbk:pc:et:gpd:eventTypeId      |
      | $.event.layToLose.instances | {"ALL":{"value":50.0,"flag":"NONE"}} |

    # update is received with different layToLose value
    * value "83" is stored with index "layToLose"

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_ESL_Event_Update_Risk_Management.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                    | UPDATE                               |
      | $.event.id                  | urn:sbk:pc:e:gpd:eventId           |
      | $.event.subclassId          | urn:sbk:pc:sbc:gpd:13                |
      | $.event.layToLose.instances | {"ALL":{"value":83.0,"flag":"NONE"}} |

    # REFRESH is received with different layToLose value = 77
    * value "77" is stored with index "layToLose"
    * value "REFRESH" is stored with index "action"
    * value "REFRESH" is stored with index "updateType"

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_ESL_Event_Create_With_Event_Sort.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                    | REFRESH                              |
      | $.event.id                  | urn:sbk:pc:e:gpd:eventId           |
      | $.event.subclassId          | urn:sbk:pc:sbc:gpd:13                |
      | $.event.layToLose.instances | {"ALL":{"value":77.0,"flag":"NONE"}} |

    #update is received without any change
    * value "UPDATE" is stored with index "action"

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_ESL_Event_Create_With_Event_Sort.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then "RD_OUTPUT_DELTA_TENNIS" should have no new messages

  Scenario: REFRESH message received from FIP for Event when CREATE was not received (no state)
  On REFRESH without state, the new value is persisted and published to client

    # refresh is received with layToLose value = 60
    * value "60" is stored with index "layToLose"
    * value "REFRESH" is stored with index "action"
    * value "REFRESH" is stored with index "updateType"

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_ESL_Event_Create_With_Event_Sort.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                    | CREATE                               |
      | $.event.id                  | urn:sbk:pc:e:gpd:eventId           |
      | $.event.subclassId          | urn:sbk:pc:sbc:gpd:13                |
      | $.event.layToLose.instances | {"ALL":{"value":60.0,"flag":"NONE"}} |

