@RDStreamDeltas @GMAFlow
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: GMA Flow - Market Override fields

  This feature intends to verify Market Overrides

  Background:
    * "FOOTBALL" market with selections is created along with all hierarchy

  Scenario: Multi State Message received from GMA to Market Type OVERRIDE with laytolose and gmtl values
  On OVERRIDE message is published


    Given "Kafka" payload with headers is set to "/domain/gma/football/market/GMA_Global_Override_Market.json"
    When Kafka message with key "marketId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action         | UPDATE                      |
      | $.market.id      | urn:sbk:pc:m:gpd:marketId |
      | $.market.eventId | urn:sbk:pc:e:gpd:eventId  |

    Then consume "3" Kafka messages from "SEP_FOOTBALL" topic
    And Kafka message with value "US-TX" for path "$.locale" is picked
    And "Kafka" response matches
      | $.payload.market.id.id                                    | marketId      |
      | $.payload.market.eventId.id                               | eventId       |
      | $.action                                                  | OVERRIDE_FIELDS |
      | $.payload.market.layToLose.winLP                          | 30.0            |
      | $.payload.market.layToLose.guaranteedMinWin               | Key Not Found   |
      | $.payload.market.fieldAction.layToLoseActions.winLPAction | LOCK            |
      | $.locale                                                  | US-TX              |
    And Kafka message with value "US-NJ" for path "$.locale" is picked
    And "Kafka" response matches
      | $.payload.market.id.id                                               | marketId      |
      | $.payload.market.eventId.id                                          | eventId       |
      | $.action                                                             | OVERRIDE_FIELDS |
      | $.payload.market.layToLose.winLP                                     | 12.0            |
      | $.payload.market.layToLose.guaranteedMinWin                          | 8.0             |
      | $.payload.market.fieldAction.layToLoseActions.winLPAction            | LOCK            |
      | $.payload.market.fieldAction.layToLoseActions.guaranteedMinWinAction | REMOVE_OVERRIDE |
      | $.locale                                                             | US-NJ              |
    And Kafka message with value "US-VA" for path "$.locale" is picked
    And "Kafka" response matches
      | $.payload.market.id.id                                    | marketId      |
      | $.payload.market.eventId.id                               | eventId       |
      | $.action                                                  | OVERRIDE_FIELDS |
      | $.payload.market.layToLose.winLP                          | 5.0             |
      | $.payload.market.layToLose.guaranteedMinWin               | Key Not Found   |
      | $.payload.market.fieldAction.layToLoseActions.winLPAction | REMOVE_OVERRIDE |
      | $.locale                                                  | US-VA              |

    Given "Kafka" payload with headers is set to "/domain/gma/football/market/GMA_Global_Override_Market_RESET.json"
    When Kafka message with key "marketId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    Then consume "2" Kafka messages from "SEP_FOOTBALL" topic
    And Kafka message with value "US-TX" for path "$.locale" is picked
    And "Kafka" response matches
      | $.payload.market.id.id                                    | marketId      |
      | $.payload.market.eventId.id                               | eventId       |
      | $.action                                                  | OVERRIDE_FIELDS |
      | $.payload.market.layToLose.winLP                          | Key Not Found   |
      | $.payload.market.layToLose.guaranteedMinWin               | Key Not Found   |
      | $.payload.market.fieldAction.layToLoseActions.winLPAction | RESET           |
      | $.locale                                                  | US-TX              |
    And Kafka message with value "US-NJ" for path "$.locale" is picked
    And "Kafka" response matches
      | $.payload.market.id.id                                               | marketId      |
      | $.payload.market.eventId.id                                          | eventId       |
      | $.action                                                             | OVERRIDE_FIELDS |
      | $.payload.market.layToLose.winLP                                     | Key Not Found   |
      | $.payload.market.layToLose.guaranteedMinWin                          | 8.0             |
      | $.payload.market.fieldAction.layToLoseActions.winLPAction            | Key Not Found   |
      | $.payload.market.fieldAction.layToLoseActions.guaranteedMinWinAction | REMOVE_OVERRIDE |
      | $.locale                                                             | US-NJ              |

    Given "Kafka" payload with headers is set to "/domain/gma/football/market/GMA_Global_Override_Market_RESET_ALL.json"
    When Kafka message with key "marketId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    Then consume "1" Kafka messages from "SEP_FOOTBALL" topic
    And "Kafka" response matches
      | $.payload.market.id.id                                               | marketId      |
      | $.payload.market.eventId.id                                          | eventId       |
      | $.action                                                             | OVERRIDE_FIELDS |
      | $.payload.market.layToLose.winLP                                     | 500.0           |
      | $.payload.market.layToLose.guaranteedMinWin                          | 8.0             |
      | $.payload.market.fieldAction.layToLoseActions.winLPAction            | RESET           |
      | $.payload.market.fieldAction.layToLoseActions.guaranteedMinWinAction | REMOVE_OVERRIDE |
      | $.locale                                                             | Key Not Found   |

