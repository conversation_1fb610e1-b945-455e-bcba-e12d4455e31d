@RDStreamDeltas @GTHFlowOverrides
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: GTH Flow - Market Type Link Overrides for mostMaxBetPerc

  This feature intends to verify that override messages from GMA are processed when the type hierarchy are set from GTH
  and published to Risk Deltas stream

  Scenario: A CREATE message received from GTH to Market Type is received
  On CREATE, the values are persisted and published to downstream
  Then, an override for the LayToLosePerc is received and processed

    * random UUID value is stored with index "correlationId"
    * random Int value is stored with index "eventTypeId"
    * random Int value is stored with index "marketTypeId"
    * random Int value is stored with index "marketTypeLinkId"
    * value "CREATE" is stored with index "action"

    #Send EventType
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Event_Type.json"
    When Kafka message with key "eventTypeId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action       | CREATE                          |
      | $.eventType.id | urn:sbk:pc:et:gpd:eventTypeId |
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic

    #Send MarketType
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_MarketType.json"
    When Kafka message with key "marketTypeId" is published to "GTH_INPUT" topic

    #Send MarketTypeLink
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_MarketTypeLink.json"
    When Kafka message with key "urn:GBP:MarketTypeLink:DM:eventTypeId_marketTypeId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                     | CREATE                          |
      | $.marketTypeLink.eventTypeId | urn:sbk:pc:et:gpd:eventTypeId |
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic

    # Override MarketTypeLink
    # mostMaxBetPerc override is received on market type link

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "ALL" on Market Type Link field "mostMaxBetPerc" with value "111"
    When Kafka message with key "urn:GBP:MarketTypeLink:DM:eventTypeId_marketTypeId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                                  | UPDATE                                          |
      | $.marketTypeLink.id                       | urn:sbk:pc:mtl:gpd:eventTypeId_marketTypeId |
      | $.marketTypeLink.mostMaxBetPerc.instances | {"ALL":{"value":111,"flag":"OVERRIDDEN"}}       |


    Given "TENNIS" GMA "LOCK" instruction was received for instance "ALL" on Market Type Link field "mostMaxBetPerc" with value "222"
    When Kafka message with key "urn:GBP:MarketTypeLink:DM:eventTypeId_marketTypeId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                                  | UPDATE                                          |
      | $.marketTypeLink.id                       | urn:sbk:pc:mtl:gpd:eventTypeId_marketTypeId |
      | $.marketTypeLink.mostMaxBetPerc.instances | {"ALL":{"value":222,"flag":"LOCKED"}}           |


    Given "TENNIS" GMA "UNLOCK" instruction was received for instance "ALL" on Market Type Link field "mostMaxBetPerc" with value "333"
    When Kafka message with key "urn:GBP:MarketTypeLink:DM:eventTypeId_marketTypeId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                                  | UPDATE                                          |
      | $.marketTypeLink.id                       | urn:sbk:pc:mtl:gpd:eventTypeId_marketTypeId |
      | $.marketTypeLink.mostMaxBetPerc.instances | {"ALL":{"value":333,"flag":"OVERRIDDEN"}}       |
