@RDStreamDeltas @GMAFlow @Overrides
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: GMA Flow - Overridable Field using EventType LinkedtypeRestriction

  This feature intends to verify that overrides sent from GMA to fields of type 'OverridableField' are correctly handled
  by PCSA and published to Risk Deltas stream with correct override value and flag

  Background:
    * "FOOTBALL" event type is created along with all upper level hierarchy

  Scenario: One Instance - Message received from GMA to Event Type with all different actions for linkedTypeRestriction field

    #Send OVERRIDE as "["football","football matches"]"
    # expected: ALL = {"value":"["football","football matches"]","flag":"OVERRIDDEN"}
    Given GMA "OVERRIDE" instruction was received for instance "ALL" on Event type field "linkedTypeRestriction" with value "["football","football matches"]"
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                        | UPDATE                                                        |
      | $.eventType.id                                  | urn:sbk:pc:et:gpd:eventTypeId                               |
      | $.eventType.linkedTypeRestriction.instances.ALL | {"value":["football","football matches"],"flag":"OVERRIDDEN"} |

    # Send LOCK as ["football matches","australian"]
    # expected: ALL = {"value":["football matches","australian"],"flag":"LOCKED"}
    Given GMA "LOCK" instruction was received for instance "ALL" on Event type field "linkedTypeRestriction" with value "["football matches","australian"]"
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                        | UPDATE                                                      |
      | $.eventType.id                                  | urn:sbk:pc:et:gpd:eventTypeId                             |
      | $.eventType.linkedTypeRestriction.instances.ALL | {"value":["football matches","australian"],"flag":"LOCKED"} |

    # Send UNLOCK without any value
    # expected: ALL = {"flag":"OVERRIDDEN"} ???
    Given GMA "UNLOCK" instruction was received for instance "ALL" on Event type field "linkedTypeRestriction" without value
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                        | UPDATE                          |
      | $.eventType.id                                  | urn:sbk:pc:et:gpd:eventTypeId |
      | $.eventType.linkedTypeRestriction.instances.ALL | {"flag":"OVERRIDDEN"}           |

    # Send RESET as []
    # expected: ALL = Key Not Found
    Given GMA "RESET" instruction was received for instance "ALL" on Event type field "linkedTypeRestriction" without value
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                        | UPDATE                          |
      | $.eventType.id                                  | urn:sbk:pc:et:gpd:eventTypeId |
      | $.eventType.linkedTypeRestriction.instances.ALL | Key Not Found                   |

    # Send OVERRIDE as "["football","football matches"]" to different instance urn:i:FD:US-NY
    # expected: urn:i:FD:US-NY = {"value":["football","football matches"],"flag":"OVERRIDDEN"}
    Given GMA "OVERRIDE" instruction was received for instance "urn:i:FD:US-NY" on Event type field "linkedTypeRestriction" with value "["football","football matches"]"
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                                   | UPDATE                                                        |
      | $.eventType.id                                             | urn:sbk:pc:et:gpd:eventTypeId                               |
      | $.eventType.linkedTypeRestriction.instances.urn:i:FD:US-NY | {"value":["football","football matches"],"flag":"OVERRIDDEN"} |

    # Send LOCK as "["football matches","brazilian"]" to different instance urn:i:FD:US-NY
    # expected: urn:i:FD:US-NY = ["football matches","brazilian"],"flag":"LOCKED"}
    Given GMA "LOCK" instruction was received for instance "urn:i:FD:US-NY" on Event type field "linkedTypeRestriction" with value "["football matches","brazilian"]"
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                                   | UPDATE                                                     |
      | $.eventType.id                                             | urn:sbk:pc:et:gpd:eventTypeId                            |
      | $.eventType.linkedTypeRestriction.instances.urn:i:FD:US-NY | {"value":["football matches","brazilian"],"flag":"LOCKED"} |

    # Send UNLOCK as "["fifa"]" to different instance urn:i:FD:US-NY
    # expected: urn:i:FD:US-NY = {"value":["fifa"],"flag":"OVERRIDDEN"}
    Given GMA "UNLOCK" instruction was received for instance "urn:i:FD:US-NY" on Event type field "linkedTypeRestriction" with value "["fifa"]"
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                                   | UPDATE                                 |
      | $.eventType.id                                             | urn:sbk:pc:et:gpd:eventTypeId        |
      | $.eventType.linkedTypeRestriction.instances.urn:i:FD:US-NY | {"value":["fifa"],"flag":"OVERRIDDEN"} |

  Scenario: Two Instances - Message received from GMA to Event Type with all different actions for linkedTypeRestriction field

    # Send OVERRIDE as "["football matches","euro 2024"]" for urn:i:FD:US-NY and US-TX instances
    # expected: urn:i:FD:US-NY = {"value":["football matches","euro 2024"],"flag":"OVERRIDDEN"}  # expected: US-TX = {"value":["football matches","euro 2024"],"flag":"OVERRIDDEN"}
    Given GMA message was received on Event type field "linkedTypeRestriction" for instance "urn:i:FD:US-NY" with value "["football matches","euro 2024"]" with "OVERRIDE" instruction, and for instance "US-TX" with value "["football matches","euro 2024"]" with "OVERRIDE" instruction
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                                   | UPDATE                                                         |
      | $.eventType.id                                             | urn:sbk:pc:et:gpd:eventTypeId                                |
      | $.eventType.linkedTypeRestriction.instances.urn:i:FD:US-NY | {"value":["football matches","euro 2024"],"flag":"OVERRIDDEN"} |
      | $.eventType.linkedTypeRestriction.instances.US-TX          | {"value":["football matches","euro 2024"],"flag":"OVERRIDDEN"} |

    # Send LOCK as ["champions league"] for NY instance and UNLOCK as ["world cup"] for TX instance
    # expected: urn:i:FD:US-NY = {"value":["champions league"],"flag":"LOCKED"}  # expected: US-TX = {"value":["world cup"],"flag":"OVERRIDDEN"}
    Given GMA message was received on Event type field "linkedTypeRestriction" for instance "urn:i:FD:US-NY" with value "["champions league"]" with "LOCK" instruction, and for instance "US-TX" with value "["world cup"]" with "UNLOCK" instruction
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                                   | UPDATE                                         |
      | $.eventType.id                                             | urn:sbk:pc:et:gpd:eventTypeId                |
      | $.eventType.linkedTypeRestriction.instances.urn:i:FD:US-NY | {"value":["champions league"],"flag":"LOCKED"} |
      | $.eventType.linkedTypeRestriction.instances.US-TX          | {"value":["world cup"],"flag":"OVERRIDDEN"}    |

    # Send OVERRIDE as ["mirtilo"] for NY instance and LOCK as ["friendlies"] for TX instance
    # expected: urn:i:FD:US-NY = {"value":["fifa ladies"],"flag":"OVERRIDDEN"}  # expected: US-TX = {"value":["friendlies"],"flag":"LOCKED"}
    Given GMA message was received on Event type field "linkedTypeRestriction" for instance "urn:i:FD:US-NY" with value "["fifa ladies"]" with "OVERRIDE" instruction, and for instance "US-TX" with value "["friendlies"]" with "LOCK" instruction
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                                   | UPDATE                                        |
      | $.eventType.id                                             | urn:sbk:pc:et:gpd:eventTypeId               |
      | $.eventType.linkedTypeRestriction.instances.urn:i:FD:US-NY | {"value":["fifa ladies"],"flag":"OVERRIDDEN"} |
      | $.eventType.linkedTypeRestriction.instances.US-TX          | {"value":["friendlies"],"flag":"LOCKED"}      |

    # Send RESET for NY and TX instances
    # expected: urn:i:FD:US-NY = Key Not Found  # expected: US-TX = Key Not Found
    Given GMA message was received on Event type field "linkedTypeRestriction" without value for instance "urn:i:FD:US-NY" with "RESET" instruction, and for instance "US-TX" with "RESET" instruction
    When Kafka message with key "eventTypeId" is published to "GMA_FOOTBALL" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                                   | UPDATE                          |
      | $.eventType.id                                             | urn:sbk:pc:et:gpd:eventTypeId |
      | $.eventType.linkedTypeRestriction.instances.urn:i:FD:US-NY | Key Not Found                   |
      | $.eventType.linkedTypeRestriction.instances.US-TX          | Key Not Found                   |
