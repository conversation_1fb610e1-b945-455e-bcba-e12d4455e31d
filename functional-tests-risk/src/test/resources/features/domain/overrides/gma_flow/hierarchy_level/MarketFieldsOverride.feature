@RDStreamDeltas @GMAFlow @Overrides
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: GMA Flow - Market fields

  This feature intends to verify that overrides sent from GMA to market fields are correctly
  handled by PCSA and published to Risk Deltas stream with correct override value and flag

  Background:
    * unique long id is stored with index "selectionId1"
    * unique long id is stored with index "selectionId2"
    * "TENNIS" market with selections is created along with all hierarchy

  # "winLP" should be renamed to "layToLose"
  Scenario: Message received from GMA with an override action for the layToLose field

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "ALL" on Market field "layToLose" with value "350.0"
    When Kafka message with key "urn:sbk:pc:m:gpd:marketId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                         | UPDATE                              |
      | $.market.id                      | urn:sbk:pc:m:gpd:marketId         |
      | $.market.eventId                 | urn:sbk:pc:e:gpd:eventId          |
      | $.market.layToLose.instances.ALL | {"value":350.0,"flag":"OVERRIDDEN"} |
    Then consume "1" Kafka messages from "SEP_TENNIS" topic
    And "Kafka" response matches
      | $.payload.market.id.id | marketId |

  Scenario: Message received from GMA with an override action for the BIR delay field

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "US-TX" on Market field "inPlayBettingDelay" with value "10"
    When Kafka message with key "urn:sbk:pc:m:gpd:marketId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                                    | UPDATE                           |
      | $.market.id                                 | urn:sbk:pc:m:gpd:marketId      |
      | $.market.eventId                            | urn:sbk:pc:e:gpd:eventId       |
      | $.market.inPlayBettingDelay.instances.US-TX | {"value":10,"flag":"OVERRIDDEN"} |

  Scenario: Message received from GMA with an override action for the leastMaxBet field

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "ALL" on Market field "leastMaxBet" with value "27.75"
    When Kafka message with key "urn:sbk:pc:m:gpd:marketId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                           | UPDATE                              |
      | $.market.id                        | urn:sbk:pc:m:gpd:marketId         |
      | $.market.eventId                   | urn:sbk:pc:e:gpd:eventId          |
      | $.market.leastMaxBet.instances.ALL | {"value":27.75,"flag":"OVERRIDDEN"} |

  Scenario: Message received from GMA with an override action for the mostMaxBet field

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "ALL" on Market field "mostMaxBet" with value "140000.00"
    When Kafka message with key "urn:sbk:pc:m:gpd:marketId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                          | UPDATE                                 |
      | $.market.id                       | urn:sbk:pc:m:gpd:marketId            |
      | $.market.eventId                  | urn:sbk:pc:e:gpd:eventId             |
      | $.market.mostMaxBet.instances.ALL | {"value":140000.0,"flag":"OVERRIDDEN"} |

  Scenario: Message received from GMA with an override action for the winGMLTL field

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "ALL" on Market field "guaranteedMinimumLayToLose" with value "150.0"
    When Kafka message with key "urn:sbk:pc:m:gpd:marketId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                                          | UPDATE                              |
      | $.market.id                                       | urn:sbk:pc:m:gpd:marketId         |
      | $.market.eventId                                  | urn:sbk:pc:e:gpd:eventId          |
      | $.market.guaranteedMinimumLayToLose.instances.ALL | {"value":150.0,"flag":"OVERRIDDEN"} |
    Then consume "1" Kafka messages from "SEP_TENNIS" topic
    And "Kafka" response matches
      | $.payload.market.id.id | marketId |

  Scenario: Message received from GMA with an override action for the ignoreTimeConfig field

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "ALL" on Market field "inPlayIgnoreTimeConfig" with value "true"
    When Kafka message with key "urn:sbk:pc:m:gpd:marketId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                                      | UPDATE                             |
      | $.market.id                                   | urn:sbk:pc:m:gpd:marketId        |
      | $.market.eventId                              | urn:sbk:pc:e:gpd:eventId         |
      | $.market.inPlayIgnoreTimeConfig.instances.ALL | {"value":true,"flag":"OVERRIDDEN"} |

  Scenario: Message received from GMA with an override action for the inPlay field

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "ALL" on Market field "inPlay" with value "true"
    When Kafka message with key "urn:sbk:pc:m:gpd:marketId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                      | UPDATE                             |
      | $.market.id                   | urn:sbk:pc:m:gpd:marketId        |
      | $.market.eventId              | urn:sbk:pc:e:gpd:eventId         |
      | $.market.inPlay.instances.ALL | {"value":true,"flag":"OVERRIDDEN"} |

  Scenario: Message received from GMA with an override action for the minAccumulators field

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "ALL" on Market field "minAccumulator" with value "3"
    When Kafka message with key "urn:sbk:pc:m:gpd:marketId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                              | UPDATE                          |
      | $.market.id                           | urn:sbk:pc:m:gpd:marketId     |
      | $.market.eventId                      | urn:sbk:pc:e:gpd:eventId      |
      | $.market.minAccumulator.instances.ALL | {"value":3,"flag":"OVERRIDDEN"} |

  Scenario: Message received from GMA with an override action for the maxAccumulators field

    Given "TENNIS" GMA "OVERRIDE" instruction was received for instance "ALL" on Market field "maxAccumulator" with value "22"
    When Kafka message with key "urn:sbk:pc:m:gpd:marketId" is published to "GMA_TENNIS" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                              | UPDATE                           |
      | $.market.id                           | urn:sbk:pc:m:gpd:marketId      |
      | $.market.eventId                      | urn:sbk:pc:e:gpd:eventId       |
      | $.market.maxAccumulator.instances.ALL | {"value":22,"flag":"OVERRIDDEN"} |
