@RDStreamDeltas @RDActorStateMBean @RDActorStateValidation
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: This feature intends to validate the event Actor State

  Background:
    * backend host is set with url "hawtioUrl"
    * REST header "Content-Type" is set with value "application/json"
    * value "RDActorState" is stored with index "mbeanName"
    * random Int value is stored with index "superclassId"
    * random Int value is stored with index "subclassId"
    * value "urn:sbk:pc:sbc:gpd:subclassId" is stored with index "argument"


  Scenario: Validate actor information when Subclass doesn't exist at RD actor.
  It must retrieve an error when the actor state is not found.

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                       | 200 OK                          |
      | $.data.request.mbean         | mbeanName:type=Cluster        |
      | $.data.value.entityId        | urn:sbk:pc:sbc:gpd:subclassId |
      | $.data.value.responseMessage | No State to validate            |


  Scenario: Validate actor information when a Subclass is processed and valid by PCSA-RD
  It must retrieve the actor state when the subclass is processed, and all the validation pass.

    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Superclass.json"
    When Kafka message with key "superclassId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    And "Kafka" response matches
      | $.action        | CREATE                            |
      | $.superclass.id | urn:sbk:pc:spc:gpd:superclassId |

    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Subclass.json"
    When Kafka message with key "subclassId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    And "Kafka" response matches
      | $.action      | CREATE                          |
      | $.subclass.id | urn:sbk:pc:sbc:gpd:subclassId |

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                       | 200 OK                          |
      | $.data.request.mbean         | mbeanName:type=Cluster        |
      | $.data.value.entityId        | urn:sbk:pc:sbc:gpd:subclassId |
      | $.data.value.responseMessage | Validation passed               |


  Scenario: Validate actor information when Subclass is already processed but not valid because doesn't have a feed creation by PCSA-RD, and superclass isn't created
  It must retrieve the actor state when the Subclass is processed, and the validation failed due Subclass wasn't created by feeds and doesn't have the complete hierarchy(superclass isn't created)

    * value "UPDATE" is stored with index "action"

    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Superclass.json"
    When Kafka message with key "superclassId" is published to "GTH_INPUT" topic
    Then "RD_OUTPUT_DELTA_DEFAULT" should have no new messages


    * value "UPDATE" is stored with index "action"

    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Subclass.json"
    When Kafka message with key "subclassId" is published to "GTH_INPUT" topic
    Then "RD_OUTPUT_DELTA_DEFAULT" should have no new messages

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                           | 200 OK                                                                    |
      | $.data.request.mbean             | mbeanName:type=Cluster                                                  |
      | $.data.value.entityId            | urn:sbk:pc:sbc:gpd:subclassId                                           |
      | $.data.value.responseMessage     | Validation failed                                                         |
      | $.data.value.validationErrors[0] | This urn:sbk:pc:sbc:gpd:subclassId doesn't have the complete hierarchy. |
      | $.data.value.validationErrors[1] | This urn:sbk:pc:sbc:gpd:subclassId wasn't created by feeds.             |


  Scenario: Validate actor information when Subclass is already processed but not valid because doesn't have a feed creation by PCSA-RD
  It must retrieve the actor state when the Subclass is processed, and the validation failed due Subclass wasn't created by feeds

    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Superclass.json"
    When Kafka message with key "superclassId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    And "Kafka" response matches
      | $.action        | CREATE                            |
      | $.superclass.id | urn:sbk:pc:spc:gpd:superclassId |

    * value "UPDATE" is stored with index "action"

    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Subclass.json"
    When Kafka message with key "subclassId" is published to "GTH_INPUT" topic
    Then "RD_OUTPUT_DELTA_DEFAULT" should have no new messages

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                           | 200 OK                                                                    |
      | $.data.request.mbean             | mbeanName:type=Cluster                                                  |
      | $.data.value.entityId            | urn:sbk:pc:sbc:gpd:subclassId                                           |
      | $.data.value.responseMessage     | Validation failed                                                         |
      | $.data.value.validationErrors[0] | This urn:sbk:pc:sbc:gpd:subclassId doesn't have the complete hierarchy. |
      | $.data.value.validationErrors[1] | This urn:sbk:pc:sbc:gpd:subclassId wasn't created by feeds.             |