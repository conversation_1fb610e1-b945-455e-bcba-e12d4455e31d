@RDStreamDeltas @RDActorStateMBean @RDActorStateValidation
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: This feature intends to validate the event Actor State

  Background:
    * backend host is set with url "hawtioUrl"
    * REST header "Content-Type" is set with value "application/json"
    * random UUID value is stored with index "correlationId"
    * set property "correlationId" as SCENARIO_ID
    * value "RDActorState" is stored with index "mbeanName"
    * "TENNIS" global type hierarchy from GTH is created
    * unique long id is stored with index "eventId"
    * value "urn:sbk:pc:e:gpd:eventId" is stored with index "argument"
    * unique long id is stored with index "marketId"
    * unique long id is stored with index "marketId2"
    * unique long id is stored with index "selectionId1"
    * unique long id is stored with index "selectionId2"

  Scenario: Validate actor information when event doesn't exist at RD actor.
  It must retrieve an error when the actor state is not found.

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                       | 200 OK                     |
      | $.data.request.mbean         | mbeanName:type=Cluster   |
      | $.data.value.entityId        | urn:sbk:pc:e:gpd:eventId |
      | $.data.value.responseMessage | No State to validate       |

  Scenario: Validate actor information when an event is processed and valid by PCSA-RD, as well as the markets associated with that event.
  It must retrieve the actor state when the event is processed, and all the validation pass.

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_Create_Event.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action            | CREATE                          |
      | $.event.id          | urn:sbk:pc:e:gpd:eventId      |
      | $.event.eventTypeId | urn:sbk:pc:et:gpd:eventTypeId |

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                       | 200 OK                     |
      | $.data.request.mbean         | mbeanName:type=Cluster   |
      | $.data.value.entityId        | urn:sbk:pc:e:gpd:eventId |
      | $.data.value.responseMessage | Validation passed          |

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_ESL_Create_Two_Markets.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "2" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "2" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "2" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                       | 200 OK                     |
      | $.data.request.mbean         | mbeanName:type=Cluster   |
      | $.data.value.entityId        | urn:sbk:pc:e:gpd:eventId |
      | $.data.value.responseMessage | Validation passed          |

  Scenario: Validate actor information when event is already processed but not valid because doesn't have a feed creation by PCSA-RD, followed by a market creation.
  It must retrieve the actor state when the event is processed, and the validation failed due event wasn't created by feeds, and the validation of the market also failed due parent(event) are not valid.

    * value "UPDATE" is stored with index "action"

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_Create_Event.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                        | 200 OK                                                       |
      | $.data.request.mbean          | mbeanName:type=Cluster                                     |
      | $.data.value.entityId         | urn:sbk:pc:e:gpd:eventId                                   |
      | $.data.value.responseMessage  | Validation failed                                            |
      | $.data.value.validationErrors | ["This urn:sbk:pc:e:gpd:eventId wasn't created by feeds."] |

    * value "CREATE" is stored with index "action"

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_Create_Market_Without_birDelay.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                           | 200 OK                                                                    |
      | $.data.request.mbean             | mbeanName:type=Cluster                                                  |
      | $.data.value.entityId            | urn:sbk:pc:e:gpd:eventId                                                |
      | $.data.value.responseMessage     | Validation failed                                                         |
      | $.data.value.validationErrors[0] | This urn:sbk:pc:e:gpd:eventId wasn't created by feeds.                  |
      | $.data.value.validationErrors[1] | This urn:sbk:pc:m:gpd:marketId doesn't have the complete hierarchy.     |
      | $.data.value.validationErrors[2] | This urn:sbk:pc:s:gpd:selectionId1 doesn't have the complete hierarchy. |

  Scenario: Validate actor information when a market is resulted.
  It must retrieve an error stating the market is not live.

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_Create_Event.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                       | 200 OK                     |
      | $.data.request.mbean         | mbeanName:type=Cluster   |
      | $.data.value.entityId        | urn:sbk:pc:e:gpd:eventId |
      | $.data.value.responseMessage | Validation passed          |

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_ESL_Create_Two_Markets.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    Then consume "2" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "2" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "2" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                       | 200 OK                     |
      | $.data.request.mbean         | mbeanName:type=Cluster   |
      | $.data.value.entityId        | urn:sbk:pc:e:gpd:eventId |
      | $.data.value.responseMessage | Validation passed          |

    Given "Kafka" payload is set to "domain/fip/tennis/FIP_ESL_Update_Market_Resulted.json"
    When Kafka message with key "eventId" is published to "FIP_TENNIS" topic without headers
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_TENNIS" topic
    Then "Kafka" response matches
      | $.action                  | CLOSE                                           |
      | $.market.id               | urn:sbk:pc:m:gpd:marketId                     |
      | $.market.eventId          | urn:sbk:pc:e:gpd:eventId                      |
      | $.market.marketTypeLinkId | urn:sbk:pc:mtl:gpd:eventTypeId_marketTypeId |
      | $.market.selections[0].id | urn:sbk:pc:s:gpd:selectionId1                 |
      | $.market.selections[1].id | urn:sbk:pc:s:gpd:selectionId2                 |
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET_VALIDATION.json"
    When a "/hawtio/jolokia" POST is requested
    Then Jolokia response matches
      | $.code                       | 200 OK                     |
      | $.data.request.mbean         | mbeanName:type=Cluster   |
      | $.data.value.entityId        | urn:sbk:pc:e:gpd:eventId |
      | $.data.value.responseMessage | Validation failed          |