@RDStreamDeltas @RDActorStateMBean
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: This feature intends to purge the Subclass Actor

  Background:
    * backend host is set with url "hawtioUrl"
    * REST header "Content-Type" is set with value "application/json"
    * value "RDActorState" is stored with index "mbeanName"
    * random Int value is stored with index "superclassId"
    * random Int value is stored with index "subclassId"
    * random UUID value is stored with index "correlationId"

  Scenario: Purge actor with invalid GBPId.
  It should return an error invalid GBP id.

    * value "subclassId" is stored with index "argument"

    Given "REST" payload is set to "jmx/ACTOR_PURGE.json"
    When a "/hawtio/jolokia" POST is requested
    Then "REST" response matches
      | $.code               | 200 OK                                                                                                                                                                                |
      | $.data.request.mbean | mbeanName:type=Cluster                                                                                                                                                              |
      | $.data.value         | Operation Failed. Errors: [Invalid GbpId: GPB identifiers must be a Unified Resource Name following the schema , e.g. urn:<product>:<entity>:<hierarchy_level>:<source>:<source_id>.] |


  Scenario: Purge actor with inexistent but valid GBPId.
  It should create an actor and purge it.

    * value "urn:sbk:pc:sbc:gpd:subclassId" is stored with index "argument"

    Given "REST" payload is set to "jmx/ACTOR_PURGE.json"
    When a "/hawtio/jolokia" POST is requested
    Then "REST" response matches
      | $.code               | 200 OK                                                                  |
      | $.data.request.mbean | mbeanName:type=Cluster                                                |
      | $.data.value         | Purge executed successfully with GBP Id urn:sbk:pc:sbc:gpd:subclassId |

  Scenario: Purge actor when subclass is already processed by PCSA-RD.
  It should purge the actor when specific event has been processed.

    * value "urn:sbk:pc:spc:gpd:superclassId" is stored with index "argument"
    * value "CREATE" is stored with index "action"

    #Create random Superclass
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Superclass.json"
    When Kafka message with key "superclassId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    And "Kafka" response matches
      | $.action        | CREATE                            |
      | $.superclass.id | urn:sbk:pc:spc:gpd:superclassId |

    #Create random Subclass
    Given "Kafka" payload with headers is set to "domain/gth/tennis/GTH_Create_Subclass.json"
    When Kafka message with key "subclassId" is published to "GTH_INPUT" topic
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    And "Kafka" response matches
      | $.action                | CREATE                            |
      | $.subclass.id           | urn:sbk:pc:sbc:gpd:subclassId   |
      | $.subclass.superclassId | urn:sbk:pc:spc:gpd:superclassId |

    * value "urn:sbk:pc:sbc:gpd:subclassId" is stored with index "argument"

    Given "REST" payload is set to "jmx/ACTOR_PURGE.json"
    When a "/hawtio/jolokia" POST is requested
    Then "REST" response matches
      | $.code               | 200 OK                                                                  |
      | $.data.request.mbean | mbeanName:type=Cluster                                                |
      | $.data.value         | Purge executed successfully with GBP Id urn:sbk:pc:sbc:gpd:subclassId |
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_DEFAULT" topic
    And "Kafka" response matches
      | $.action | CREATE |
    # The current Implementation is publishing as a CREATE, don't know how accurate that is

    * wait for "5000" milliseconds

    Given "REST" payload is set to "jmx/ACTOR_STATE_GET.json"
    When a "/hawtio/jolokia" POST is requested
    Then "REST" response matches
      | $.code               | 200 OK                   |
      | $.data.request.mbean | mbeanName:type=Cluster |
      | $.data.value         | Actor state not found    |


