@RiskOverridesE2E
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection

Feature: Risk flow from Market Overrides - E2E

  Verify the Risk flow with market overrides from the ingestion of FIP/PBC and IPMA input sources until the HTTP endpoint responses

  Background:
    * "FOOTBALL" event is created along with all hierarchy
    * unique long id is stored with index "marketId"
    * unique long id is stored with index "selectionId1"
    * unique long id is stored with index "selectionId2"
    * unique long id is stored with index "selectionId3"
    * backend host is set with url "rsBaseUrl"
    * REST header "Content-Type" is set with value "application/json"
    * value "ALL" is stored with index "instance"

      # NOTES:
      # Market Type link initial values:
      #    LTL= 100% ; LMB= 100%; MMB=100%
      # Event initial values:
      #    LTL= 500; LMB= 25; MMB= 30000

  Scenario: Override for guaranteedMinWin and guaranteedMinEnabled is received and presented in the HTTP endpoint
  followed by a reset that should clean the value and reset to the FEEDs value
  This scenario covers the Test Cases #16, #18, #19, #20 of the E2E test plan

    Given "Kafka" payload is set to "domain/fip/football/FIP_ESL_Create_Market_with_GMLTL.json"
    When <PERSON><PERSON><PERSON> message with key "eventId" is published to "FIP_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                          | CREATE                         |
      | $.market.id                                       | urn:sbk:pc:m:gpd:marketId    |
      | $.market.eventId                                  | urn:sbk:pc:e:gpd:eventId     |
      | $.market.guaranteedMinimumLayToLose.instances.ALL | {"value":1000.0,"flag":"NONE"} |
    Then consume "2" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    When Kafka message with key "urn:GBP:Event:DM:eventId" is picked
    Then "Kafka" response matches
      | $.action            | CREATE                     |
      | $.event.id          | urn:sbk:pc:e:gpd:eventId |
      | $.event.mostMaxBet  | 30000.0                    |
      | $.event.leastMaxBet | 25.0                       |
      | $.event.layToLose   | 500.0                      |
    When Kafka message with key "urn:GBP:Market:DM:marketId" is picked
    Then "Kafka" response matches
      | $.action                        | CREATE                      |
      | $.market.id                     | urn:sbk:pc:m:gpd:marketId |
      | $.market.mostMaxBet             | 30000.0                     |
      | $.market.leastMaxBet            | 25.0                        |
      | $.market.layToLose              | 500.0                       |
      | $.market.inPlayIgnoreTimeConfig | false                       |

    * value "30" is stored with index "guaranteedMinWin"
    * value "true" is stored with index "guaranteedMinEnabled"

    Given "Kafka" payload is set to "domain/ipma/IPMA_Override_Market.json"
    When Kafka message with key "eventId" is published to "IPMA_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                      | UPDATE                                     |
      | $.market.id                                   | urn:sbk:pc:m:gpd:marketId                |
      | $.market.eventId                              | urn:sbk:pc:e:gpd:eventId                 |
      | $.market.inPlayIgnoreTimeConfig.instances     | {"ALL":{"value":true,"flag":"LOCKED"}}     |
      | $.market.inPlayIgnoreTimeConfig.base          | {"ALL":{"value":false}}                    |
      | $.market.guaranteedMinimumLayToLose.instances | {"ALL":{"value":30.0,"flag":"LOCKED"}}     |
      | $.market.guaranteedMinimumLayToLose.base      | {"ALL":{"value":1000.0}}                   |
      | $.market.leastMaxBet.instances                | {"ALL":{"value":1.0,"flag":"OVERRIDDEN"}}  |
      | $.market.leastMaxBet.base                     | {"ALL":{"value":25.0}}                     |
      | $.market.mostMaxBet.instances                 | {"ALL":{"value":10.0,"flag":"OVERRIDDEN"}} |
      | $.market.mostMaxBet.base                      | {"ALL":{"value":30000.0}}                  |
      | $.market.layToLose.instances                  | {"ALL":{"value":20.0,"flag":"OVERRIDDEN"}} |
      | $.market.layToLose.base                       | {"ALL":{"value":500.0}}                    |
    Then consume "1" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                            | UPDATE                      |
      | $.market.id                         | urn:sbk:pc:m:gpd:marketId |
      | $.market.mostMaxBet                 | 10.0                        |
      | $.market.leastMaxBet                | 1.0                         |
      | $.market.layToLose                  | 20.0                        |
      | $.market.inPlayIgnoreTimeConfig     | true                        |
      | $.market.guaranteedMinimumLayToLose | 30.0                        |

    Given "REST" payload is set to "stream/http/SelectionRiskRequest.json"
    And wait for "3000" milliseconds
    When a "/risk-stream/api/selections" POST is requested
    Then "REST" response matches
      | $.code                                                  | 200 OK |
      | $.data.selections.size()                                | 1      |
      | $.data.selections.selectionId1.betLimits.mostMaxBet   | 10.0   |
      | $.data.selections.selectionId1.betLimits.leastMaxBet  | 1.0    |
      | $.data.selections.selectionId1.layToLose              | 20.0   |
      | $.data.selections.selectionId1.gmltl.ignoreTimeConfig | true   |
      | $.data.selections.selectionId1.gmltl.inPlay           | false  |
      | $.data.selections.selectionId1.gmltl.win              | 30.0   |

    # We'll always receive the GMLTL info
    * value "0" is stored with index "guaranteedMinWin"

    Given "Kafka" payload is set to "domain/ipma/IPMA_Override_Reset_Market.json"
    When Kafka message with key "eventId" is published to "IPMA_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                      | UPDATE                                |
      | $.market.id                                   | urn:sbk:pc:m:gpd:marketId           |
      | $.market.eventId                              | urn:sbk:pc:e:gpd:eventId            |
      | $.market.inPlayIgnoreTimeConfig.instances     | {"ALL":{"value":false,"flag":"NONE"}} |
      | $.market.inPlayIgnoreTimeConfig.base          | Key Not Found                         |
      | $.market.guaranteedMinimumLayToLose.instances | {"ALL":{"value":0.0,"flag":"LOCKED"}} |
      | $.market.guaranteedMinimumLayToLose.base      | {"ALL":{"value":1000.0}}              |
      | $.market.leastMaxBet.instances                | Key Not Found                         |
      | $.market.mostMaxBet.instances                 | Key Not Found                         |
      | $.market.layToLose.instances                  | Key Not Found                         |
    Then consume "1" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                            | UPDATE                      |
      | $.market.id                         | urn:sbk:pc:m:gpd:marketId |
      | $.market.mostMaxBet                 | 10.0                        |
      | $.market.leastMaxBet                | 1.0                         |
      | $.market.layToLose                  | 20.0                        |
      | $.market.inPlayIgnoreTimeConfig     | false                       |
      | $.market.guaranteedMinimumLayToLose | 0.0                         |

    Given "REST" payload is set to "stream/http/SelectionRiskRequest.json"
    And wait for "3000" milliseconds
    When a "/risk-stream/api/selections" POST is requested
    Then "REST" response matches
      | $.code                                                  | 200 OK |
      | $.data.selections.size()                                | 1      |
      | $.data.selections.selectionId1.betLimits.mostMaxBet   | 10.0   |
      | $.data.selections.selectionId1.betLimits.leastMaxBet  | 1.0    |
      | $.data.selections.selectionId1.layToLose              | 20.0   |
      | $.data.selections.selectionId1.gmltl.ignoreTimeConfig | false  |
      | $.data.selections.selectionId1.gmltl.inPlay           | false  |
      | $.data.selections.selectionId1.gmltl.win              | 0.0    |

  Scenario: Override for layToLose, leastMaxBet and mostMaxBet at market level is received and presented in the HTTP endpoint
  followed by a reset that should NOT clean the values and keep the OVERRIDDEN values
  Then, an update for the Event from FEEDs should recalculate the market value
  This scenario covers the Test Cases #21, #22, #23, #24, #25, #26, #27, #28 and #29 of the E2E test plan

    Given "Kafka" payload is set to "domain/fip/football/FIP_ESL_Create_Market_with_GMLTL.json"
    When Kafka message with key "eventId" is published to "FIP_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                          | CREATE                         |
      | $.market.id                                       | urn:sbk:pc:m:gpd:marketId    |
      | $.market.eventId                                  | urn:sbk:pc:e:gpd:eventId     |
      | $.market.guaranteedMinimumLayToLose.instances.ALL | {"value":1000.0,"flag":"NONE"} |
    Then consume "2" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    When Kafka message with key "urn:GBP:Event:DM:eventId" is picked
    Then "Kafka" response matches
      | $.action            | CREATE                     |
      | $.event.id          | urn:sbk:pc:e:gpd:eventId |
      | $.event.mostMaxBet  | 30000.0                    |
      | $.event.leastMaxBet | 25.0                       |
      | $.event.layToLose   | 500.0                      |
    When Kafka message with key "urn:GBP:Market:DM:marketId" is picked
    Then "Kafka" response matches
      | $.action                        | CREATE                      |
      | $.market.id                     | urn:sbk:pc:m:gpd:marketId |
      | $.market.mostMaxBet             | 30000.0                     |
      | $.market.leastMaxBet            | 25.0                        |
      | $.market.layToLose              | 500.0                       |
      | $.market.inPlayIgnoreTimeConfig | false                       |

    Given "Kafka" payload is set to "domain/ipma/IPMA_Override_Market.json"
    When Kafka message with key "eventId" is published to "IPMA_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                      | UPDATE                                     |
      | $.market.id                                   | urn:sbk:pc:m:gpd:marketId                |
      | $.market.eventId                              | urn:sbk:pc:e:gpd:eventId                 |
      | $.market.inPlayIgnoreTimeConfig.instances     | {"ALL":{"value":true,"flag":"LOCKED"}}     |
      | $.market.inPlayIgnoreTimeConfig.base          | {"ALL":{"value":false}}                    |
      | $.market.guaranteedMinimumLayToLose.instances | {"ALL":{"value":12.0,"flag":"LOCKED"}}     |
      | $.market.guaranteedMinimumLayToLose.base      | {"ALL":{"value":1000.0}}                   |
      | $.market.leastMaxBet.instances                | {"ALL":{"value":1.0,"flag":"OVERRIDDEN"}}  |
      | $.market.leastMaxBet.base                     | {"ALL":{"value":25.0}}                     |
      | $.market.mostMaxBet.instances                 | {"ALL":{"value":10.0,"flag":"OVERRIDDEN"}} |
      | $.market.mostMaxBet.base                      | {"ALL":{"value":30000.0}}                  |
      | $.market.layToLose.instances                  | {"ALL":{"value":20.0,"flag":"OVERRIDDEN"}} |
      | $.market.layToLose.base                       | {"ALL":{"value":500.0}}                    |
    Then consume "1" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                            | UPDATE                      |
      | $.market.id                         | urn:sbk:pc:m:gpd:marketId |
      | $.market.mostMaxBet                 | 10.0                        |
      | $.market.leastMaxBet                | 1.0                         |
      | $.market.layToLose                  | 20.0                        |
      | $.market.inPlayIgnoreTimeConfig     | true                        |
      | $.market.guaranteedMinimumLayToLose | 12.0                        |

    Given "REST" payload is set to "stream/http/SelectionRiskRequest.json"
    And wait for "3000" milliseconds
    When a "/risk-stream/api/selections" POST is requested
    Then "REST" response matches
      | $.code                                                  | 200 OK |
      | $.data.selections.size()                                | 1      |
      | $.data.selections.selectionId1.betLimits.mostMaxBet   | 10.0   |
      | $.data.selections.selectionId1.betLimits.leastMaxBet  | 1.0    |
      | $.data.selections.selectionId1.layToLose              | 20.0   |
      | $.data.selections.selectionId1.gmltl.ignoreTimeConfig | true   |
      | $.data.selections.selectionId1.gmltl.inPlay           | false  |
      | $.data.selections.selectionId1.gmltl.win              | 12.0   |

     # We'll always receive the GMLTL info
    * value "0" is stored with index "guaranteedMinWin"

    Given "Kafka" payload is set to "domain/ipma/IPMA_Override_Reset_Market.json"
    When Kafka message with key "eventId" is published to "IPMA_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                      | UPDATE                                |
      | $.market.id                                   | urn:sbk:pc:m:gpd:marketId           |
      | $.market.eventId                              | urn:sbk:pc:e:gpd:eventId            |
      | $.market.inPlayIgnoreTimeConfig.instances     | {"ALL":{"value":false,"flag":"NONE"}} |
      | $.market.inPlayIgnoreTimeConfig.base          | Key Not Found                         |
      | $.market.guaranteedMinimumLayToLose.instances | {"ALL":{"value":0.0,"flag":"LOCKED"}} |
      | $.market.guaranteedMinimumLayToLose.base      | {"ALL":{"value":1000.0}}              |
      | $.market.leastMaxBet.instances                | Key Not Found                         |
      | $.market.mostMaxBet.instances                 | Key Not Found                         |
      | $.market.layToLose.instances                  | Key Not Found                         |
    Then consume "1" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                            | UPDATE                      |
      | $.market.id                         | urn:sbk:pc:m:gpd:marketId |
      | $.market.mostMaxBet                 | 10.0                        |
      | $.market.leastMaxBet                | 1.0                         |
      | $.market.layToLose                  | 20.0                        |
      | $.market.inPlayIgnoreTimeConfig     | false                       |
      | $.market.guaranteedMinimumLayToLose | 0.0                         |

    Given "REST" payload is set to "stream/http/SelectionRiskRequest.json"
    And wait for "3000" milliseconds
    When a "/risk-stream/api/selections" POST is requested
    Then "REST" response matches
      | $.code                                                  | 200 OK |
      | $.data.selections.size()                                | 1      |
      | $.data.selections.selectionId1.betLimits.mostMaxBet   | 10.0   |
      | $.data.selections.selectionId1.betLimits.leastMaxBet  | 1.0    |
      | $.data.selections.selectionId1.layToLose              | 20.0   |
      | $.data.selections.selectionId1.gmltl.ignoreTimeConfig | false  |
      | $.data.selections.selectionId1.gmltl.inPlay           | false  |
      | $.data.selections.selectionId1.gmltl.win              | 0.0    |

    # Update risk management fields at event level
    * value "50" is stored with index "leastMaxBet"
    * value "50000" is stored with index "mostMaxBet"
    * value "100" is stored with index "layToLose"

    Given "Kafka" payload is set to "domain/fip/football/FIP_ESL_Event_Update_Risk_Management.json"
    When Kafka message with key "eventId" is published to "FIP_FOOTBALL" topic without headers
    Then consume "2" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    When Kafka message with key "urn:GBP:Event:DM:eventId" is picked
    Then "Kafka" response matches
      | $.action                                         | UPDATE                          |
      | $.event.id                                       | urn:sbk:pc:e:gpd:eventId      |
      | $.event.leastMaxBet.instances.ALL                | {"value":50.0,"flag":"NONE"}    |
      | $.event.mostMaxBet.instances.ALL                 | {"value":50000.0,"flag":"NONE"} |
      | $.event.layToLose.instances.ALL                  | {"value":100.0,"flag":"NONE"}   |
      | $.event.guaranteedMinimumLayToLose.instances.ALL | Key Not Found                   |
    When Kafka message with key "urn:GBP:Market:DM:marketId" is picked
    Then "Kafka" response matches
      | $.action                                          | UPDATE                               |
      | $.market.id                                       | urn:sbk:pc:m:gpd:marketId          |
      | $.market.eventId                                  | urn:sbk:pc:e:gpd:eventId           |
      | $.market.leastMaxBet.instances.ALL                | {"value":50.0,"flag":"INHERITED"}    |
      | $.market.mostMaxBet.instances.ALL                 | {"value":50000.0,"flag":"INHERITED"} |
      | $.market.layToLose.instances.ALL                  | {"value":100.0,"flag":"INHERITED"}   |
      | $.market.guaranteedMinimumLayToLose.instances.ALL | Key Not Found                        |
    Then consume "2" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    When Kafka message with key "urn:GBP:Event:DM:eventId" is picked
    Then "Kafka" response matches
      | $.action            | UPDATE                     |
      | $.event.id          | urn:sbk:pc:e:gpd:eventId |
      | $.event.mostMaxBet  | 50000.0                    |
      | $.event.leastMaxBet | 50.0                       |
      | $.event.layToLose   | 100.0                      |
    When Kafka message with key "urn:GBP:Market:DM:marketId" is picked
    Then "Kafka" response matches
      | $.action                        | UPDATE                      |
      | $.market.id                     | urn:sbk:pc:m:gpd:marketId |
      | $.market.mostMaxBet             | 50000.0                     |
      | $.market.leastMaxBet            | 50.0                        |
      | $.market.layToLose              | 100.0                       |
      | $.market.inPlayIgnoreTimeConfig | false                       |

  Scenario: OVERRIDE for leastMaxBet is received for the market with a value which is higher than mostMaxBet
  This scenario covers the Test Case #39 of the E2E test plan

    Given "Kafka" payload is set to "domain/fip/football/FIP_ESL_Create_Market_with_GMLTL.json"
    When Kafka message with key "eventId" is published to "FIP_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                          | CREATE                         |
      | $.market.id                                       | urn:sbk:pc:m:gpd:marketId    |
      | $.market.eventId                                  | urn:sbk:pc:e:gpd:eventId     |
      | $.market.guaranteedMinimumLayToLose.instances.ALL | {"value":1000.0,"flag":"NONE"} |
    Then consume "2" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    When Kafka message with key "urn:GBP:Event:DM:eventId" is picked
    Then "Kafka" response matches
      | $.action            | CREATE                     |
      | $.event.id          | urn:sbk:pc:e:gpd:eventId |
      | $.event.mostMaxBet  | 30000.0                    |
      | $.event.leastMaxBet | 25.0                       |
    When Kafka message with key "urn:GBP:Market:DM:marketId" is picked
    Then "Kafka" response matches
      | $.action             | CREATE                      |
      | $.market.id          | urn:sbk:pc:m:gpd:marketId |
      | $.market.mostMaxBet  | 30000.0                     |
      | $.market.leastMaxBet | 25.0                        |

    #OVERRIDE is not considered due leastMaxBet is higher than mostMaxBet
    * value "31000" is stored with index "leastMaxBet"
    # We'll always receive the GMLTL info
    * value "0" is stored with index "guaranteedMinWin"

    Given "Kafka" payload is set to "domain/ipma/IPMA_Override_Market_Field_LeastMaxBet.json"
    When Kafka message with key "eventId" is published to "IPMA_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action             | UPDATE                      |
      | $.market.id          | urn:sbk:pc:m:gpd:marketId |
      | $.market.eventId     | urn:sbk:pc:e:gpd:eventId  |
      | $.market.mostMaxBet  | Key Not Found               |
      | $.market.leastMaxBet | Key Not Found               |
    Then consume "1" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    And "Kafka" response matches
      | $.action             | UPDATE                      |
      | $.market.id          | urn:sbk:pc:m:gpd:marketId |
      | $.market.eventId     | urn:sbk:pc:e:gpd:eventId  |
      | $.market.mostMaxBet  | 30000.0                     |
      | $.market.leastMaxBet | 25.0                        |

    Given "REST" payload is set to "stream/http/SelectionRiskRequest.json"
    And wait for "3000" milliseconds
    When a "/risk-stream/api/selections" POST is requested
    Then "REST" response matches
      | $.code                                                 | 200 OK  |
      | $.data.selections.size()                               | 1       |
      | $.data.selections.selectionId1.betLimits.mostMaxBet  | 30000.0 |
      | $.data.selections.selectionId1.betLimits.leastMaxBet | 25.0    |

  Scenario: OVERRIDE for mostMaxBet is received for the market with a value which is lower than leastMaxBet
  This scenario covers the Test Case #40 of the E2E test plan

    Given "Kafka" payload is set to "domain/fip/football/FIP_ESL_Create_Market_with_GMLTL.json"
    When Kafka message with key "eventId" is published to "FIP_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTIONS" topic
    And consume "1" Kafka messages from "PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS" topic
    And consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action                                          | CREATE                         |
      | $.market.id                                       | urn:sbk:pc:m:gpd:marketId    |
      | $.market.eventId                                  | urn:sbk:pc:e:gpd:eventId     |
      | $.market.guaranteedMinimumLayToLose.instances.ALL | {"value":1000.0,"flag":"NONE"} |
    Then consume "2" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    When Kafka message with key "urn:GBP:Event:DM:eventId" is picked
    Then "Kafka" response matches
      | $.action            | CREATE                     |
      | $.event.id          | urn:sbk:pc:e:gpd:eventId |
      | $.event.mostMaxBet  | 30000.0                    |
      | $.event.leastMaxBet | 25.0                       |
    When Kafka message with key "urn:GBP:Market:DM:marketId" is picked
    Then "Kafka" response matches
      | $.action             | CREATE                      |
      | $.market.id          | urn:sbk:pc:m:gpd:marketId |
      | $.market.mostMaxBet  | 30000.0                     |
      | $.market.leastMaxBet | 25.0                        |

    #OVERRIDE is not considered due mostMaxBet is lower than leastMaxBet
    * value "24" is stored with index "mostMaxBet"
    Given "Kafka" payload is set to "domain/ipma/IPMA_Override_Market_Field_MostMaxBet.json"
    When Kafka message with key "eventId" is published to "IPMA_FOOTBALL" topic without headers
    Then consume "1" Kafka messages from "RD_OUTPUT_DELTA_FOOTBALL" topic
    And "Kafka" response matches
      | $.action             | UPDATE                      |
      | $.market.id          | urn:sbk:pc:m:gpd:marketId |
      | $.market.eventId     | urn:sbk:pc:e:gpd:eventId  |
      | $.market.mostMaxBet  | Key Not Found               |
      | $.market.leastMaxBet | Key Not Found               |
    Then consume "1" Kafka messages from "RS_OUTPUT_FULL_FOOTBALL" topic
    And "Kafka" response matches
      | $.action             | UPDATE                      |
      | $.market.id          | urn:sbk:pc:m:gpd:marketId |
      | $.market.eventId     | urn:sbk:pc:e:gpd:eventId  |
      | $.market.mostMaxBet  | 30000.0                     |
      | $.market.leastMaxBet | 25.0                        |

    Given "REST" payload is set to "stream/http/SelectionRiskRequest.json"
    And wait for "3000" milliseconds
    When a "/risk-stream/api/selections" POST is requested
    Then "REST" response matches
      | $.code                                                 | 200 OK  |
      | $.data.selections.size()                               | 1       |
      | $.data.selections.selectionId1.betLimits.mostMaxBet  | 30000.0 |
      | $.data.selections.selectionId1.betLimits.leastMaxBet | 25.0    |