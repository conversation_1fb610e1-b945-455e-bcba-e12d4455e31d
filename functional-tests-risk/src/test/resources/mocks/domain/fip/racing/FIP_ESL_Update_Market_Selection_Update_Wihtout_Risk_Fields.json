{
  "headers": {
    "SCENARIO_ID": "SCENARIO_ID",
    "RAMP_SPORT": "23",
    "source-app": "Payload-Viewer",
    "KAFKA_RECORD_TIMESTAMP": "1717599054817",
    "MTF_TIMESTAMP_IN": "1717599054819",
    "uber-trace-id": "fc189d28754258cc:b3355c71a568b774:340b82236879b314:0",
    "TOPOLOGY_PROFILE": "racing",
    "APP_KEY": "zqfYNzmk57o4KuNX",
    "TOPOLOGY_BRAND": "BRAND",
    "MARKET_ID": "marketId",
    "TIMESTAMP_SPOUT": "1717599054828",
    "FHG_TIMESTAMP_IN": "1717599054819",
    "SEQUENCE": "13",
    "TIMESTAMP": "1717599054817",
    "MEDIA_TYPE": "application/json",
    "UPDATE_TYPE": "REGULAR",
    "TIMESTAMP_OUT": "1717599054817",
    "ID": "eventId",
    "CORRELATION_ID": "correlationId",
    "INSTRUCTION_PROCESSOR_TIMESTAMP_OUT": "1717599054854",
    "PROVIDER_TIMESTAMP": "1717599054784",
    "SPORT": "7",
    "FIP_TIMESTAMP_IN": "1717599054828",
    "TIMESTAMP_IN": "1717599054817",
    "DATA": "EVENT_COMMAND",
    "FHG_TIMESTAMP_OUT": "1717599054826",
    "PROVIDER_SEQUENCE": "13",
    "PROVIDER": "POWERS_FEED"
  },
  "iType": "UPDATE",
  "event": {
    "markets": [
      {
        "typeId": marketTypeId:4210,
        "id": {
          "externalId": "marketId"
        },
        "livePriceBettingAvailable": false,
        "selections": [
          {
            "action": "UPDATE",
            "displayed": false,
            "id": {
              "externalId": "selectionId1"
            }
          }
        ]
      }
    ],
    "event": {
      "id": {
        "externalId": "eventId"
      }
    }
  },
  "hwm": {
    "mark": "13",
    "token": "RAMP-34655169-559481544"
  }
}
