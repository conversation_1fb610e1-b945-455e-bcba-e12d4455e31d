customModels:
  FIP_2_PCSA:
    - model: Instruction2ProtoHandler
    - path: "$.event"
      model: Context2EMProtoHandler
  GMA_2_PCSA:
    - model: OverrideContract
  PCSA_RD_2_PCSA_RS:
    - model: RiskDomainProto
  PCSA_RS:
    - model: RiskStreamProto
  PCSA_RD_2_RD_SUBSCRIPTIONS:
    - model: RDSubscriptions
  PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS:
    - model: RDSubscriptionNotifications
  IPMA_2_PCSA:
    - model: IPMAInstructionOverride
  PCSA_2_SEP:
    - model: GMAInstructionOverride
  PCSA_MD_2_PCSA_MS:
    - model: MarketDomainProto

values:
  KafkaPublishPauseDuration: "800"
  FAIL_SUITE_WITH_UNCONSUMED_MESSAGES: "ON"
  IGNORE_EXTERNAL_UNCONSUMED_MESSAGES: "OFF"
  rsBaseUrl: http://localhost:8080
  rsBaseUrlNode2: http://localhost:8081
  rsBaseUrlNode3: http://localhost:8082
  hawtioUrl: http://localhost:9999