kafka:
  topics:
    ##### Risk Domain #####
    #FIP Output Topics / MD Input
    FIP_FOOTBALL: &defaultInputTopicConfigsFromFIP
      bootstrapServers: "activeDCKafkaBootstrapServers"
      topic: ppb.stream.TOPIC_BRAND.instructions.football
      model: FIP_2_PCSA
      autoConsumeEnabled: "off"
    FIP_TENNIS:
      <<: *defaultInputTopicConfigsFromFIP
      topic: ppb.stream.TOPIC_BRAND.instructions.tennis
    FIP_BASKETBALL:
      <<: *defaultInputTopicConfigsFromFIP
      topic: ppb.stream.TOPIC_BRAND.instructions.basketball
    FIP_RACING:
      <<: *defaultInputTopicConfigsFromFIP
      topic: ppb.stream.TOPIC_BRAND.instructions.racing
    FIP_OTHERS:
      <<: *defaultInputTopicConfigsFromFIP
      topic: ppb.stream.TOPIC_BRAND.instructions.others

    # Global Type Hierarchy (GTH)
    GTH_INPUT: &defaultInputTopicConfigsFromGTH
      bootstrapServers: "activeDCKafkaBootstrapServers"
      topic: type.hierarchy.GTH_BRAND #each brand has its own topic coming from GTH
      autoConsumeEnabled: "off"

    # RD Output/RS Input Topics
    RD_OUTPUT_DELTA_DEFAULT: &defaultOutputTopicConfigsFromRD
      bootstrapServers: "activeDCKafkaBootstrapServers"
      topic: catalog.risk.domain.TOPIC_BRAND.others.delta
      model: PCSA_RD_2_PCSA_RS
      autoConsumeEnabled: "on"
      scenarioIdExtractor:
        header: "SCENARIO_ID"
    RD_OUTPUT_DELTA_FOOTBALL:
      <<: *defaultOutputTopicConfigsFromRD
      topic: catalog.risk.domain.TOPIC_BRAND.football.delta
    RD_OUTPUT_DELTA_TENNIS:
      <<: *defaultOutputTopicConfigsFromRD
      topic: catalog.risk.domain.TOPIC_BRAND.tennis.delta
    RD_OUTPUT_DELTA_BASKETBALL:
      <<: *defaultOutputTopicConfigsFromRD
      topic: catalog.risk.domain.TOPIC_BRAND.basketball.delta
    RD_OUTPUT_DELTA_HORSERACING:
      <<: *defaultOutputTopicConfigsFromRD
      topic: catalog.risk.domain.TOPIC_BRAND.racing.delta

    #Subscriptions and notifications
    PCSA_RD_2_RD_SUBSCRIPTIONS: &defaultSubscriptionsFromRD
      bootstrapServers: "activeDCKafkaBootstrapServers"
      topic: pcsa.rd.subscription.request
      model: PCSA_RD_2_RD_SUBSCRIPTIONS
      autoConsumeEnabled: "on"
      scenarioIdExtractor:
        header: "SCENARIO_ID"
    PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS:
      <<: *defaultSubscriptionsFromRD
      topic: pcsa.rd.subscription.notification
      model: PCSA_RD_2_RD_SUBSCRIPTION_NOTIFICATIONS

    #IPMA Output Topics / RD Input
    IPMA_FOOTBALL: &defaultInputTopicConfigsFromIPMA
      bootstrapServers: "activeDCKafkaBootstrapServers"
      topic: powers.stream.TOPIC_BRAND.ipma.overrides.football
      model: IPMA_2_PCSA
      autoConsumeEnabled: "off"
    IPMA_TENNIS:
      <<: *defaultInputTopicConfigsFromIPMA
      topic: powers.stream.TOPIC_BRAND.ipma.overrides.tennis
    IPMA_BASKETBALL:
      <<: *defaultInputTopicConfigsFromIPMA
      topic: powers.stream.TOPIC_BRAND.ipma.overrides.basketball
    IPMA_RACING:
      <<: *defaultInputTopicConfigsFromIPMA
      topic: powers.stream.TOPIC_BRAND.ipma.overrides.racing
    IPMA_OTHERS:
      <<: *defaultInputTopicConfigsFromIPMA
      topic: powers.stream.TOPIC_BRAND.ipma.overrides.others

    ##### Risk Stream #####
    #RS Output Topics
    RS_OUTPUT_FULL_DEFAULT: &defaultOutputTopicConfigsFromRS
      bootstrapServers: "activeDCKafkaBootstrapServers"
      topic: catalog.risk.stream.TOPIC_BRAND.others.full
      model: PCSA_RS
      scenarioIdExtractor:
        header: "SCENARIO_ID"
    RS_OUTPUT_FULL_FOOTBALL:
      <<: *defaultOutputTopicConfigsFromRS
      topic: catalog.risk.stream.TOPIC_BRAND.football.full
    RS_OUTPUT_FULL_TENNIS:
      <<: *defaultOutputTopicConfigsFromRS
      topic: catalog.risk.stream.TOPIC_BRAND.tennis.full
    RS_OUTPUT_FULL_BASKETBALL:
      <<: *defaultOutputTopicConfigsFromRS
      topic: catalog.risk.stream.TOPIC_BRAND.basketball.full
    RS_OUTPUT_FULL_HORSERACING:
      <<: *defaultOutputTopicConfigsFromRS
      topic: catalog.risk.stream.TOPIC_BRAND.racing.full

values:
  BRAND: PP
  TOPIC_BRAND: pp
  GTH_BRAND: paddy_power
  KAFKA_RECORD_WAIT_TIME: "2000"
  activeDCKafkaBootstrapServers: localhost:9092
  passiveDCKafkaBootstrapServers: localhost:9092