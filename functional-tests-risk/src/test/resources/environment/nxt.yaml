kafka:
  topics:
    # GMA Output Topics / RD Input
    GMA_OVERRIDE:
      bootstrapServers: "gmaKafkaCluster"
      topic: catalog.override.pp.delta
      model: GMA_2_PCSA
      autoConsumeEnabled: "off"

    # Global Type Hierarchy (GTH)
    GTH_INPUT: &defaultInputTopicConfigsFromGTH
      bootstrapServers: "activeDCKafkaBootstrapServers"
      topic: type.hierarchy.GTH_BRAND #each brand has its own topic coming from GTH
      autoConsumeEnabled: "off"

    # FIP Output Topics / MD Input
    FIP_FOOTBALL: &defaultInputTopicConfigsFromFIP
      bootstrapServers: "feedsKafkaCluster"
      topic: ppb.stream.TOPIC_BRAND.instructions.football
      model: FIP_2_PCSA
      autoConsumeEnabled: "off"
    FIP_TENNIS:
      <<: *defaultInputTopicConfigsFromFIP
      topic: ppb.stream.TOPIC_BRAND.instructions.tennis

values:
  BRAND: PP
  TOPIC_BRAND: pp
  GTH_BRAND: paddy_power
  gmaKafkaCluster: ie2-mfs001-nxt.nxt.betfair:9092
  feedsKafkaCluster: ie2-mfs001-nxt.nxt.betfair:9092
