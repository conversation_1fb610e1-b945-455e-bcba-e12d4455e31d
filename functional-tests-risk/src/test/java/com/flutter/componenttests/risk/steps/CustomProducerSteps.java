package com.flutter.componenttests.risk.steps;

import com.ppb.feeds.testframework.broker.kafka.ProducerKafka;
import com.ppb.feeds.testframework.cucumber.stepdefinition.FunctionalSteps;
import com.ppb.feeds.testframework.util.Configs;
import com.ppb.feeds.testframework.util.StringsUtil;
import io.cucumber.java.en.Given;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.junit.jupiter.api.Assertions;

public class CustomProducerSteps {

    @Given("^Kafka message with key \"(.+)\" is published to \"(.+)\" topic without headers$")
    public static void publishMessageWithKeyToTopic(String keyValue, String topic) {
        String finalTopicName = StringsUtil.replaceWithExtractedValues(topic);
        String finalValue = StringsUtil.replaceWithExtractedValues(keyValue);
        ProducerKafka.setKafkaProducerHeaders(new RecordHeaders());
        Assertions.assertTrue(ProducerKafka.getTopicKafkaProducer(finalTopicName).publishRecord(finalValue), "Failed to publish Kafka record!");
        FunctionalSteps.wait(Configs.getKafkaPublishPauseDuration());
    }
}
