package com.flutter.componenttests.risk.steps;

import com.ppb.feeds.testframework.cucumber.stepdefinition.KafkaConsumerSteps;
import com.ppb.feeds.testframework.cucumber.stepdefinition.KafkaProducerSteps;
import com.ppb.feeds.testframework.cucumber.stepdefinition.PayloadsSteps;
import com.ppb.feeds.testframework.cucumber.stepdefinition.ScenarioSteps;
import com.ppb.feeds.testframework.cucumber.stepdefinition.StringSteps;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import org.junit.jupiter.api.Assertions;

public class CreateUpperHierarchySteps {

    // TENNIS
    final String TENNIS_CREATE_SUPERCLASS_MOCK = "domain/gth/tennis/GTH_Create_Superclass.json"; // NOSONAR
    final String TENNIS_CREATE_SUBCLASS_MOCK = "domain/gth/tennis/GTH_Create_Subclass.json"; // NOSONAR
    final String TENNIS_CREATE_EVENT_TYPE_GTH_MOCK = "domain/gth/tennis/GTH_Create_Event_Type.json"; // NOSONAR
    final String TENNIS_CREATE_MARKET_TYPE_LINK_GTH_MOCK = "domain/gth/tennis/GTH_Create_MarketTypeLink.json"; // NOSONAR
    final String TENNIS_CREATE_MARKET_TYPE_GTH_MOCK = "domain/gth/tennis/GTH_Create_MarketType.json"; // NOSONAR
    final String TENNIS_CREATE_EVENT_MOCK = "domain/fip/tennis/FIP_ESL_Event_Create_With_Event_Sort.json"; // NOSONAR
    final String TENNIS_CREATE_MARKET_WITH_SELECTIONS_MOCK = "domain/fip/tennis/FIP_ESL_Market_Create_Match_Odds.json"; // NOSONAR

    // FOOTBALL
    final String FOOTBALL_CREATE_SUPERCLASS_MOCK = "domain/gth/football/GTH_Create_Superclass.json"; // NOSONAR
    final String FOOTBALL_CREATE_SUBCLASS_MOCK = "domain/gth/football/GTH_Create_Subclass.json"; // NOSONAR
    final String FOOTBALL_CREATE_EVENT_TYPE_GTH_MOCK = "domain/gth/football/GTH_Create_Event_Type.json"; // NOSONAR
    final String FOOTBALL_CREATE_MARKET_TYPE_LINK_GTH_MOCK = "domain/gth/football/GTH_Create_MarketTypeLink.json"; // NOSONAR
    final String FOOTBALL_CREATE_MARKET_TYPE_GTH_MOCK = "domain/gth/football/GTH_Create_MarketType.json"; // NOSONAR
    final String FOOTBALL_CREATE_EVENT_MOCK = "domain/fip/football/FIP_ESL_Swedish_Division_Create_Event.json"; // NOSONAR
    final String FOOTBALL_CREATE_MARKET_WITH_SELECTIONS_MOCK = "domain/fip/football/FIP_ESL_Swedish_Division_Create_WDW_Market.json"; // NOSONAR

    @Given("\"(.+)\" superclass is created$")
    public void createSuperclassForGivenProfile(String profile) {
        setupScenarioID();
        createSuperclass(profile);
    }

    @Given("\"(.+)\" upper hierarchy is created$")
    public void createUpperHierarchyForGivenProfile(String profile) {
        createSuperclass(profile);
        createSubclass(profile);
    }

    @Given("\"(.+)\" event type is created along with all upper level hierarchy$")
    public void createEventTypeForGivenProfile(String profile) {
        setupScenarioID();
        createEventTypeGTH(profile);
    }

    @Given("\"(.+)\" global type hierarchy from GTH is created$")
    public void createGlobalTypeHierarchyFromGTHForGivenProfile(String profile) {
        createEventTypeForGivenProfile(profile);
        createMarketTypeGTH(profile);
        createMarketTypeLinkGTH(profile);
    }

    @Given("\"(.+)\" event is created when hierarchy already exists$")
    public void createEventWithExistingHierarchy(String profile) {
        createEvent(profile);
    }

    @Given("\"(.+)\" event is created along with all hierarchy$")
    public void createMarketParentsForGivenProfile(String profile) {
        createGlobalTypeHierarchyFromGTHForGivenProfile(profile);
        createEvent(profile);
    }

    @Given("\"(.+)\" market with selections is created along with all hierarchy$")
    public void createMarketWithSelectionsAndParentsForGivenProfile(String profile) {
        createMarketParentsForGivenProfile(profile);
        createMarketWithSelections(profile);
    }

    private static void setupScenarioID() {
        StringSteps.generateAndStoreUuid("correlationId");
        ScenarioSteps.setPropertyScenarioId("correlationId");
    }

    private void createMarketWithSelections(String profile) {
        //generate ids
        StringSteps.generateUniqueLongId("marketId");
        StringSteps.generateUniqueLongId("selectionId1");
        StringSteps.generateUniqueLongId("selectionId2");
        StringSteps.generateUniqueLongId("selectionId3");

        switch (profile) {
            case "TENNIS" -> publishEntityAndConsumeOutputAndNotifs(
                    "marketId",
                    TENNIS_CREATE_MARKET_WITH_SELECTIONS_MOCK,
                    "FIP_TENNIS",
                    StepsHelper.TENNIS_OUTPUT_TOPIC);
            case "FOOTBALL" -> publishEntityAndConsumeOutputAndNotifs(
                    "marketId",
                    FOOTBALL_CREATE_MARKET_WITH_SELECTIONS_MOCK,
                    "FIP_FOOTBALL",
                    StepsHelper.FOOTBALL_OUTPUT_TOPIC);
            default -> Assertions.fail(String.format("Profile %s not supported", profile));
        }
    }

    private void createEvent(String profile) {
        //generate ids
        StringSteps.generateUniqueLongId("eventId");

        switch (profile) {
            case "TENNIS" -> publishEntityAndConsumeOutputAndNotifs(
                    "eventId",
                    TENNIS_CREATE_EVENT_MOCK,
                    "FIP_TENNIS",
                    StepsHelper.TENNIS_OUTPUT_TOPIC);
            case "FOOTBALL" -> publishEntityAndConsumeOutputAndNotifs(
                    "eventId",
                    FOOTBALL_CREATE_EVENT_MOCK,
                    "FIP_FOOTBALL",
                    StepsHelper.FOOTBALL_OUTPUT_TOPIC);
            default -> Assertions.fail(String.format("Profile %s not supported", profile));
        }
    }

    private void createSuperclass(String profile) {

        switch (profile) {
            case "TENNIS" -> publishMessageWithHeadersAndConsumeOutput(
                    "29",
                    TENNIS_CREATE_SUPERCLASS_MOCK,
                    StepsHelper.OTHERS_OUTPUT_TOPIC);
            case "FOOTBALL" -> publishMessageWithHeadersAndConsumeOutput(
                    "27",
                    FOOTBALL_CREATE_SUPERCLASS_MOCK,
                    StepsHelper.OTHERS_OUTPUT_TOPIC);
            default -> Assertions.fail(String.format("Profile %s not supported", profile));

        }
    }

    private void createSubclass(String profile) {

        switch (profile) {
            case "TENNIS" -> publishMessageWithHeadersNotifyAndConsumeOutput(
                    "13",
                    TENNIS_CREATE_SUBCLASS_MOCK,
                    StepsHelper.TENNIS_OUTPUT_TOPIC);
            case "FOOTBALL" -> publishMessageWithHeadersNotifyAndConsumeOutput(
                    "49",
                    FOOTBALL_CREATE_SUBCLASS_MOCK,
                    StepsHelper.FOOTBALL_OUTPUT_TOPIC);
            default -> Assertions.fail(String.format("Profile %s not supported", profile));
        }
    }

    private void createEventTypeGTH(String profile) {
        //generate ids
        StringSteps.generateRandomInt("eventTypeId");

        switch (profile) {
            case "TENNIS" -> publishMessageWithHeadersNotifyAndConsumeOutput(
                    "eventTypeId",
                    TENNIS_CREATE_EVENT_TYPE_GTH_MOCK,
                    StepsHelper.TENNIS_OUTPUT_TOPIC);
            case "FOOTBALL" -> publishMessageWithHeadersNotifyAndConsumeOutput(
                    "eventTypeId",
                    FOOTBALL_CREATE_EVENT_TYPE_GTH_MOCK,
                    StepsHelper.FOOTBALL_OUTPUT_TOPIC);
            default -> Assertions.fail(String.format("Profile %s not supported", profile));
        }
    }

    private void createMarketTypeGTH(String profile) {
        //generate ids
        StringSteps.generateRandomInt("marketTypeId");

        switch (profile) {
            case "TENNIS" -> publishMessageWithHeaders(
                    "marketTypeId",
                    TENNIS_CREATE_MARKET_TYPE_GTH_MOCK
            );
            case "FOOTBALL" -> publishMessageWithHeaders(
                    "marketTypeId",
                    FOOTBALL_CREATE_MARKET_TYPE_GTH_MOCK
            );
            default -> Assertions.fail(String.format("Profile %s not supported", profile));
        }
    }

    private void createMarketTypeLinkGTH(String profile) {
        //generate ids
        StringSteps.generateRandomInt("marketTypeLinkId");

        switch (profile) {
            case "TENNIS" -> publishMessageWithHeadersNotifyAndConsumeOutput(
                    "eventTypeId_marketTypeId",
                    TENNIS_CREATE_MARKET_TYPE_LINK_GTH_MOCK,
                    StepsHelper.TENNIS_OUTPUT_TOPIC
            );
            case "FOOTBALL" -> publishMessageWithHeadersNotifyAndConsumeOutput(
                    "eventTypeId_marketTypeId",
                    FOOTBALL_CREATE_MARKET_TYPE_LINK_GTH_MOCK,
                    StepsHelper.FOOTBALL_OUTPUT_TOPIC
            );
            default -> Assertions.fail(String.format("Profile %s not supported", profile));
        }
    }

    private void consumeMsgFromSubscriptionTopics(String numOfMsgsToConsume) {
        KafkaConsumerSteps.consumeMessagesUntilFromTopic(numOfMsgsToConsume, StepsHelper.SUBSCRIPTIONS_TOPIC);
        KafkaConsumerSteps.consumeMessagesUntilFromTopic(numOfMsgsToConsume, StepsHelper.NOTIFICATIONS_TOPIC);
    }

    private void publishEntityAndConsumeOutputAndNotifs(String entityKey, String mockPath, String topicToInject, String topicToConsume) {

        publishEntityAndConsumeOutput(entityKey, mockPath, topicToInject, topicToConsume);
        consumeMsgFromSubscriptionTopics("1");
    }

    private void publishEntityAndConsumeOutput(String entityKey, String mockPath, String topicToInject, String topicToConsume) {

        PayloadsSteps.setPayloadFromFile("Kafka", mockPath);
        KafkaProducerSteps.publishMessageWithKeyToTopic(entityKey, topicToInject);
        //consume message from output delta topic
        KafkaConsumerSteps.consumeMessagesUntilFromTopic("1", topicToConsume);
    }

    private void publishMessageWithHeaders(String entityKey, String mockPath) {

        PayloadsSteps.setPayloadFromFileWithHeaders("Kafka", mockPath);
        KafkaProducerSteps.publishMessageWithKeyToTopic(entityKey, "GTH_INPUT");
    }

    private void publishMessageWithHeadersAndConsumeOutput(String entityKey, String mockPath, String topicToConsume) {

        PayloadsSteps.setPayloadFromFileWithHeaders("Kafka", mockPath);
        KafkaProducerSteps.publishMessageWithKeyToTopic(entityKey, "GTH_INPUT");
        //consume message from output delta topic
        KafkaConsumerSteps.consumeMessagesUntilFromTopic("1", topicToConsume);
    }

    private void publishMessageWithHeadersNotifyAndConsumeOutput(String entityKey, String mockPath, String topicToConsume) {

        PayloadsSteps.setPayloadFromFileWithHeaders("Kafka", mockPath);
        KafkaProducerSteps.publishMessageWithKeyToTopic(entityKey, "GTH_INPUT");
        //consume 2 messages (request and notify)
        consumeMsgFromSubscriptionTopics("1");
        //consume message from output delta topic
        KafkaConsumerSteps.consumeMessagesUntilFromTopic("1", topicToConsume);
    }

}
