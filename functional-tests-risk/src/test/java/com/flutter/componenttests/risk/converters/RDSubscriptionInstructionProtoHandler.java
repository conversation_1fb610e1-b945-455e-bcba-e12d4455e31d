package com.flutter.componenttests.risk.converters;

import com.flutter.pcsa.rd.subscriptions.contract.proto.RDSubscriptionInstruction;
import com.ppb.feeds.testframework.serdes.ModelHandler;
import org.apache.avro.specific.SpecificRecord;
import org.apache.commons.lang3.NotImplementedException;
import org.jetbrains.annotations.NotNull;
import scalapb.json4s.JsonFormat;

import java.io.IOException;
import java.io.InputStream;

public class RDSubscriptionInstructionProtoHandler implements ModelHandler {
    @NotNull
    @Override
    public String toJsonString(@NotNull byte[] bytes) throws IOException {
        RDSubscriptionInstruction instruction = (RDSubscriptionInstruction) RDSubscriptionInstruction.parseFrom(bytes);
        return JsonFormat.toJsonString(instruction);
    }

    @NotNull
    @Override
    public byte[] toProtoBytes(@NotNull String s) throws IOException {
        throw new NotImplementedException("Event State Serializer not implemented.");
    }

    @NotNull
    @Override
    public SpecificRecord toAvroRecord(@NotNull String s) throws IOException {
        return null;
    }

    @NotNull
    @Override
    public String toJsonString(@NotNull SpecificRecord specificRecord) throws IOException {
        RDSubscriptionInstruction instruction = (RDSubscriptionInstruction) RDSubscriptionInstruction.parseFrom((InputStream) specificRecord);
        return JsonFormat.toJsonString(instruction);
    }
}
