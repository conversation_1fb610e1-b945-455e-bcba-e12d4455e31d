syntax = "proto3";

package com.flutter.eventstate.proto.model;

import "google/protobuf/wrappers.proto";
import "Types.proto";
import "Identifier.proto";

message SelectionModelPB {
    com.flutter.eventstate.proto.model.SelectionIdentifierPB identifier = 1;
    SelectionNamePB name = 2;
    SelectionHierarchyInfoPB hierarchyInfo = 3;
    SelectionTradingPB trading = 4;
    SelectionResultingPB resulting = 5;
    SelectionRacingPB racing = 6;
    ModelMetaPB meta = 7;
}

message SelectionNamePB {
    PopulatedStringPB name = 1;
    PopulatedStringPB alternativeName = 2;
}

message SelectionHierarchyInfoPB {
    int64 id = 1;
    PopulatedIntPB typeId = 2;
    PopulatedSelectionCharacteristicPB characteristic = 3;
    PopulatedPriceLinkPB priceLink = 4;
    PopulatedStringPB playerId = 5;
    PopulatedStringPB participantKey = 6;
    PopulatedLongPB sportexId = 7;
}

message PopulatedSelectionCharacteristicPB {
    VersionPB version = 1;
    SelectionCharacteristicPB value = 2;
}

enum SelectionCharacteristicPB {
    MISSING_SC = 0;
    HOME = 1;
    DRAW = 2;
    AWAY = 3;
    HIGHER = 4;
    LOWER = 5;
    BETTING_WITHOUT = 6;
    UNNAMED_FAVOURITE = 7;
    UNNAMED_SECOND_FAVOURITE = 8;
    NO_SCORER = 9;
    CORRECT_SCORE = 10;
}

message PopulatedPriceLinkPB {
    VersionPB version = 1;
    PriceLinkPB value = 2;
}

message PriceLinkPB {
    PriceLinkActionPB priceLinkAction = 1;
    google.protobuf.StringValue key = 2;
    google.protobuf.Int32Value priority = 3;
}

enum PriceLinkActionPB {
    MISSING_PLA = 0;
    CREATE_LINK = 1;
    LINKED = 2;
    UNLINKED = 3;
}

message SelectionTradingPB {
    PopulatedBettingStatusPB bettingStatus = 1;
    PriceModelPB price = 2;
    DisplayPB display = 3;
    PopulatedBooleanPB showPrice = 4;
    PopulatedSelectionBettingStatusPB lc_betting_status = 5;
    LockableSelectionBettingStatusPB lockable_betting_status = 6;

}

message PriceModelPB {
    PopulatedPricePB price = 1;
    PopulatedPricePB previousPrice = 2;
    PopulatedNudgePB nudge = 3;
    PopulatedNudgePB previousNudge = 4;
}

message PopulatedNudgePB{
    VersionPB version = 1;
    NudgeModelPB value = 2;
}

message NudgeModelPB {
    google.protobuf.Int32Value  count = 1;
    google.protobuf.DoubleValue correctionFactor = 2;
}

message PopulatedSelectionCorrectScoreInfoPB {
    VersionPB version = 1;
    SelectionCorrectScoreInfoPB value = 2;
}

message PopulatedSelectionHandicapScoreInfoPB {
    VersionPB version = 1;
    SelectionHandicapScoreInfoPB value = 2;
}

message SelectionResultingPB {
    PopulatedResultStatusPB resultStatus = 1;
    PopulatedResultTypePB resultType = 2;
    PopulatedResultTypePB unofficialResultType = 3;
    PopulatedIntPB place = 4;
    PopulatedIntPB unofficialPlace = 5;
    repeated PopulatedSelectionIndexResultPB indexResults = 6;
    PopulatedSelectionCorrectScoreInfoPB correctScoreInfo = 7;
    PopulatedSelectionHandicapScoreInfoPB handicapScoreInfo = 8;
    repeated SelectionIndexResultTypeAndStatusPB indexResultsAndStatus = 9;
}

enum ResultStatusPB {
    MISSING_RS = 0;
    CONFIRMED = 1;
    CANCEL_RESULT = 2;
}

message PopulatedResultStatusPB {
    VersionPB version = 1;
    ResultStatusPB resultStatus = 2;
}

enum ResultTypePB {
    MISSING_RT = 0;
    WIN = 1;
    LOSE = 2;
    HANDICAP_RESULT = 3;
    HIGHER_LOWER_RESULT = 4;
    PUSH = 5;
    VOID = 6;
    PLACE = 7;
    INDEX_RESULTED = 8;
}

message PopulatedResultTypePB {
    VersionPB version = 1;
    ResultTypePB resultStatus = 2;
}

message SelectionIndexResultPB {
    int32 indexValue = 1;
    ResultTypePB resultType = 2;
}

message PopulatedSelectionIndexResultPB {
    VersionPB version = 1;
    SelectionIndexResultPB value = 2;
}

message SelectionIndexResultTypeAndStatusPB {
    int32 indexValue = 1;
    PopulatedResultTypePB resultType = 2;
    PopulatedResultStatusPB resultStatus = 3;
}

message SelectionCorrectScoreInfoPB {
    google.protobuf.Int32Value correctScoreA = 1;
    google.protobuf.Int32Value correctScoreB = 2;
}

message SelectionHandicapScoreInfoPB {
    google.protobuf.Int32Value handicapScore = 1;
}

message SelectionRacingPB {
    PopulatedFractionPB startingPrice = 1;
    repeated SelectionPropertyTypeMapPB properties = 2;
    PopulatedStringPB appearanceId = 3;
    DeadHeatReductionPB deadHeatReduction = 4;
    UnnamedFavouriteReductionPB unnamedFavouriteReduction = 5;
}

message UnnamedFavouriteReductionPB {
    PopulatedFractionPB win = 1;
    PopulatedFractionPB place = 2;
}

message SelectionPropertyTypeMapPB {
    SelectionPropertyTypePB key = 1;
    PopulatedStringPB value = 2;
}

enum SelectionPropertyTypePB {
    MISSING_SPT = 0;
    RUNNER_ID = 1;
    RUNNER_NUMBER = 2;
    JOCKEY_NAME = 3;
    RUNNER_STATUS = 4;
    DRAW_NUMBER = 5;
    WEIGHT = 6;
    TRAINER = 7;
    AGE = 8;
}

message DeadHeatReductionPB {
    PopulatedFractionPB win = 1;
    PopulatedFractionPB place = 2;
}



