syntax = "proto3";

package com.flutter.product.catalogue.overrides;

import "data_types/Overrides.proto";
import "data_types/Action.proto";

/*
  EventType message
 */
message EventType {
  string id = 1; // Id
  string subclassId = 2; // Subclass Id
  string superclassId = 3; // Superclass Id

  map<string, com.flutter.product.catalogue.overrides.common.IntOverride> inPlayBettingDelay = 4; // Bet in Running Delay sets the time delay in seconds, for an entire event, from when the customer places the bet and it is actually 'accepted' on OB (i.e. when it’s intended to be applied on all markets of that event).
  map<string, com.flutter.product.catalogue.overrides.common.LongOverride> maxPayout = 5; // Maximum value of payout, stored with each bet.
  map<string, LinkedTypeRestrictionOverride> linkedTypeRestriction = 6; // Apply one or more restrictions for combinability within different Event Types.


  message LinkedTypeRestrictionOverride {
    com.flutter.product.catalogue.overrides.common.Action action = 1;
    repeated string value = 2; // list of event id
  }
}


