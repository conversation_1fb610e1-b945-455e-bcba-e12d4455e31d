syntax = "proto3";

package com.flutter.product.catalogue.market.domain.common;

import "data_types/Flag.proto";

message DoubleField {
  //base value is only needed for PCC & auditing and it means what should be the value without any override
  //without any override base value will be empty
  map<string, OptionalDouble> base = 1; // -> front
  //<Instance, OverrideValue>
  //Instance = ALL for all instances and Instance Id e.g: (ALL -> 1.1, flag -> NONE), (NY -> 3.2, flag -> LOCKED), (NJ -> 2.1, flag -> INHERITED_AND_OVERRIDDEN)
  map<string, DoubleFieldValue> instances = 2;

  message DoubleFieldValue {
    optional double value = 1;
    Flag flag = 2;
  }

  message OptionalDouble {
    optional double value = 1;
  }
}
