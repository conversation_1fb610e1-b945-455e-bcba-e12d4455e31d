syntax = "proto3";

package com.flutter.product.catalogue.market.domain.common;

import "data_types/Flag.proto";

message <PERSON><PERSON><PERSON><PERSON> {
  //base value is only needed for PCC & auditing and it means what should be the value without any override
  //without any override base value will be empty
  map<string, OptionalBool> base = 1; // -> front
  //<Instance, OverrideValue>
  //Instance = ALL for all instances and Instance Id e.g: (ALL -> false, flag -> NONE), (NY -> true, flag -> LOCKED), (NJ -> true, flag -> INHERITED_AND_OVERRIDDEN)
  map<string, BoolFieldValue> instances = 2;

  message BoolFieldValue {
    optional bool value = 1;
    Flag flag = 2;
  }

  message OptionalBool {
    optional bool value = 1;
  }
}
