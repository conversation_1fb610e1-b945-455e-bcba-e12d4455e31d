syntax = "proto3";

package com.flutter.product.catalogue.market.domain.common;

import "data_types/Flag.proto";

message StringField {
  //base value is only needed for PCC & auditing and it means what should be the value without any override
  //without any override base value will be empty
  map<string, OptionalString> base = 1; // -> front
  //<Instance, OverrideValue>
  //Instance = ALL for all instances and Instance Id e.g:
  // (ALL -> "SomeString", flag -> NONE), (NY -> "SomeString1", flag -> LOCKED), (NJ -> "SomeString2", flag -> INHERITED_AND_OVERRIDDEN)
  map<string, StringFieldValue> instances = 2;

  message StringFieldValue {
    optional string value = 1;
    Flag flag = 2;
  }

  message OptionalString {
    optional string value = 1;
  }
}
