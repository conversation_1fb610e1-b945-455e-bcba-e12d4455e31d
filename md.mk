
launch_single_md:
	cd docker && FLAVOUR="md" $(DOCKER_COMPOSE_CMD) --profile single up -d

launch_cluster_md:
	cd docker && FLAVOUR="md" $(DOCKER_COMPOSE_CMD) --profile cluster up -d

local_single_md: _compile_pcsa _build_docker_image _down_all_service_containers launch_single_md

local_cluster_md: _compile_pcsa _build_docker_image _down_all_service_containers launch_cluster_md

ci_infra_up_md: _down_all_service_containers _build_docker_image launch_single_md

ci_infra_down_md: local_down

fun_test_md:
	mvn verify -f ./functional-tests-market/pom.xml -Pcomponent-tests,marketDomain \
		--no-transfer-progress