package com.flutter.pcsa.common.datatypes.gbpid

import com.flutter.pcsa.common.datatypes.GbpEnumTablesFixture
import org.scalacheck.Gen
import org.scalatest.GivenWhenThen
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers.{a, convertToAnyShouldWrapper}
import org.scalatest.prop.TableDrivenPropertyChecks.forAll
import org.scalatest.prop.Tables.Table

class GbpIdentifierValidatorSpec extends GbpIdValidator with AnyFlatSpecLike with GivenWhenThen with GbpEnumTablesFixture {

  "GbpIdValidator" should "validate a long format string to a correct Gbp ID" in {
    forAll(productTable) { product =>
      forAll(entityTable) { entity =>
        forAll(hlTable) { hierarchyLevel =>
          forAll(sourceTable) { source =>
            lazy val genInt: Gen[Int] = Gen.posNum[Int]
            val sourceId = genInt.sample.get
            val gbpIdString = s"urn:$product:$entity:$hierarchyLevel:$source:$sourceId"
            val expected = gbpIdString
            val result = validate(gbpIdString).getOrElse()
            val resultGbpId = validate(GbpId(gbpIdString))
            result shouldBe expected
            resultGbpId shouldBe a[Right[_, GbpId]]
            resultGbpId.right.get.asLong shouldBe expected
          }
        }
      }
    }
  }

  it should "validate a short format string to a correct Gbp ID" in {
    forAll(sourceTable) { source =>
      lazy val genInt: Gen[Int] = Gen.posNum[Int]
      val sourceId = genInt.sample.get
      val gbpIdString = s"urn:$source:$sourceId"
      val gbpId = GbpId(gbpIdString, GbpIdHierarchyLevel.Event)
      val expected = gbpId.asShort
      val result = validate(gbpIdString).getOrElse()
      result shouldBe expected

      val resultGbpID = validate(gbpId)
      resultGbpID.isRight shouldBe true
    }
  }

  it should "validate an infra format GbpID" in {
    val oldGbpId = "urn:GBP:Event:DM:1234"
    val expected = oldGbpId
    val result = validate(oldGbpId).getOrElse()
    result shouldBe expected
  }

  it should "return a GbpIdValidationException type when a criteria is not met" in {
    val cases = Table(
      ("id", "expected"),
      ("urn:GBP:sbk:pc:e:gpd:123", SizeNotMeetCriteria),
      (":sbk:pc:e:gpd:123", UrnNotMeetCriteria),
      ("urn:invalid:pc:e:gpd:123", ProductNotMeetCriteria),
      ("urn:sbk:invalid:e:gpd:123", EntityNotMeetCriteria),
      ("urn:sbk:pc:invalid:gpd:123", HierarchyLevelNotMeetCriteria),
      ("urn:sbk:pc:e:invalid:123", SourceNotMeetCriteria),
      ("urn:sbk:pc:e:gpd: ", NonEmptyNotMeetCriteria)
    )
    forAll(cases) { (id, expected) =>
      val result = validate(id)
      result shouldBe a[Either[GbpIdValidationException, GbpId]]
      result.left.get shouldBe expected
    }
  }

}
