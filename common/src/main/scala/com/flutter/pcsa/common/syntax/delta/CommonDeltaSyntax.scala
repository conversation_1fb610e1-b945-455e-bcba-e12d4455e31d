package com.flutter.pcsa.common.syntax.delta

import com.flutter.pcsa.common.datatypes.{EntityIdentifiers, Platform}

import scala.language.implicitConversions

trait CommonDeltaSyntax {

  implicit def deltaEntityIdentifiers(state: EntityIdentifiers): Delta[EntityIdentifiers] = previousState => {
    //TODO: to be refactored when identifier was introduced at entity model
    val gbpID = previousState.identifiers.get(Platform.Gbp).orElse(state.identifiers.get(Platform.Gbp)).getOrElse("")
    EntityIdentifiers.empty.copy(
      (state.identifiers |-| previousState.identifiers) + (Platform.Gbp -> gbpID)
    )
  }


  implicit def deltaOption[A](state: Option[A]): Delta[Option[A]] = previousState => {
    (state, previousState) match {
      case (Some(stateValue), Some(previousStateValue)) if stateValue == previousStateValue => None
      case (None, Some(_))                                                                  => None //FIXME: find the delete case
      case (Some(value), _)                                                                 => Some(value)
      case (None, _)                                                                        => None
    }
  }

  implicit def deltaMap[K, V](state: Map[K, V]): Delta[Map[K, V]] = previousState => {
    state.filterNot(kv => previousState.get(kv._1).exists(kv._2.equals))
  }

}
