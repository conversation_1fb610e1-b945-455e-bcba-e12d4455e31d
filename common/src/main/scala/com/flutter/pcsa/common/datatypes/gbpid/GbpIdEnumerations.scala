package com.flutter.pcsa.common.datatypes.gbpid

object GbpIdProductItBelongs extends Enumeration {
  type Name = Value
  val Sportsbook = Value("sbk")
}

object GbpIdEntity extends Enumeration {
  type Name = Value
  val ProductCatalogue = Value("pc")
}

object GbpIdHierarchyLevel extends Enumeration {
  type Level = Value
  val Superclass = Value("spc")
  val Subclass = Value("sbc")
  val EventType = Value("et")
  val MarketType = Value("mt")
  val MarketTypeLink = Value("mtl")
  val Event = Value("e")
  val Market = Value("m")
  val Selection = Value("s")
}

object GbpIdExternalSource extends Enumeration {
  type Name = Value
  val CatalogFromGPD = Value("gpd")
  val VirtualsFromInspired = Value("insp")
  val LotteriesFromBetRadar = Value("br")
}
