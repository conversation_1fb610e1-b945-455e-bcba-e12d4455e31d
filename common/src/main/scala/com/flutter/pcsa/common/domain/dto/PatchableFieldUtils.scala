package com.flutter.pcsa.common.domain.dto

import com.flutter.pcsa.common.datatypes.dto.{NoUpdateField, PatchableField, ToUpdateField}

object PatchableFieldUtils {

  implicit def toPatchableField[T](value: Option[Option[T]]): PatchableField[Option[T]] = {
    value match {
      case Some(value) => ToUpdateField(value)
      case None => NoUpdateField
    }
  }

  implicit def fromPatchableField[T](value: PatchableField[Option[T]]): Option[Option[T]] = {
    value match {
      case ToUpdateField(value) => Some(value)
      case NoUpdateField => None
    }
  }
}
