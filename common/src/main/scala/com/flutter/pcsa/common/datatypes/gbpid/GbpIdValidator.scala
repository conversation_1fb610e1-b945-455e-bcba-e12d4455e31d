package com.flutter.pcsa.common.datatypes.gbpid

object GbpIdValida<PERSON> extends GbpIdValidator

trait GbpIdValidator {

  def validate(gbpId: GbpId): Either[GbpIdValidationException, GbpId] = {
    validate(gbpId.asLong) match {
      case Left(exception) => Left(exception)
      case Right(_) => Right(gbpId)
    }
  }
  def validate(id: String): Either[GbpIdValidationException, String] = {
    val parts: Array[String] = id.split(":")
    parts.length match {
      case 6 => validateAsLong(parts)
      case 5 => validateInfraKey(parts)
      case 3 => validateAsShort(parts)
      case _ => Left(SizeNotMeetCriteria)
    }
  }

  private def validateAsLong(parts: Array[String]): Either[GbpIdValidationException, String] = {
    for {
      validateUrn <- validateUrn(parts)
      validateType <- validateProduct(parts)
      validateProvider <- validateEntity(parts)
      validateEntity <- validateHierarchyLevel(parts)
      validateSource <- validateSource(parts)
      validateNonBlank <- validateNonBlank(parts, parts.length - 1)
    } yield validateNonBlank.mkString(":")
  }

  private def validateInfraKey(parts: Array[String]): Either[GbpIdValidationException, String] = {
    for {
      validateUrn <- validateUrn(parts)
      validateInfraKeyProduct <- validateInfraKeyProduct(parts)
      validateInfraKeyHierarchyLevel <- validateInfraKeyHierarchyLevel(parts)
      validateInfraKeySource <- validateInfraKeySource(parts)
      validateNonBlank <- validateNonBlank(parts, parts.length - 1)
    } yield validateNonBlank.mkString(":")
  }

  private def validateAsShort(parts: Array[String]): Either[GbpIdValidationException, String] = {
    for {
      validateUrn <- validateUrn(parts)
      validateSource <- validateSource(idParts = parts, index = 1)
      validateNonBlank <- validateNonBlank(idParts = parts, index = 2)
    } yield validateNonBlank.mkString(":")
  }

  private def validateProduct(idParts: Array[String]): Either[GbpIdValidationException, Array[String]] =
    Either.cond(
      idParts(1).equals("sbk"),
      idParts,
      ProductNotMeetCriteria
    )

  private def validateEntity(idParts: Array[String]): Either[GbpIdValidationException, Array[String]] =
    Either.cond(
      idParts(2).equals("pc"),
      idParts,
      EntityNotMeetCriteria
    )

  private def validateHierarchyLevel(idParts: Array[String]): Either[GbpIdValidationException, Array[String]] =
    Either.cond(
      Array("spc", "sbc", "et", "mt", "mtl", "e", "m", "s").contains(idParts(3)),
      idParts,
      HierarchyLevelNotMeetCriteria
    )

  private def validateUrn(idParts: Array[String]): Either[GbpIdValidationException, Array[String]] =
    Either.cond(
      idParts(0).equals("urn"),
      idParts,
      UrnNotMeetCriteria
    )

  private def validateSource(idParts: Array[String], index: Int = 4): Either[GbpIdValidationException, Array[String]] =
    Either.cond(
      Array("gpd", "insp", "br").contains(idParts(index)),
      idParts,
      SourceNotMeetCriteria
    )

  private def validateNonBlank(idParts: Array[String], index: Int): Either[GbpIdValidationException, Array[String]] =
    Either.cond(
      !idParts(index).isBlank && !idParts(index).equals("0") && !idParts(index).equals("null"),
      idParts,
      NonEmptyNotMeetCriteria
    )

  private def validateInfraKeyProduct(idParts: Array[String]): Either[GbpIdValidationException, Array[String]] =
    Either.cond(
      idParts(1).equals("GBP"),
      idParts,
      ProductNotMeetCriteria
    )

  private def validateInfraKeyHierarchyLevel(idParts: Array[String]): Either[GbpIdValidationException, Array[String]] =
    Either.cond(
      Array("Superclass", "Subclass", "EventType", "MarketTypeLink", "MarketType", "Event", "Market", "Selection").contains(idParts(2)),
      idParts,
      HierarchyLevelNotMeetCriteria
    )

  private def validateInfraKeySource(idParts: Array[String]): Either[GbpIdValidationException, Array[String]] =
    Either.cond(
      idParts(3).equals("DM"),
      idParts,
      SourceNotMeetCriteria
    )

}
