#!/usr/bin/env scala

import java.util.Base64
import java.net.http.{HttpClient, HttpRequest, HttpResponse}
import java.net.URI
import java.time.Duration
import scala.util.{Try, Success, Failure}

/**
 * Jolokia JMX Client Script
 * 
 * This script calls a Jolokia JMX endpoint to retrieve entity data using basic authentication.
 * 
 * Usage:
 *   scala jolokia-client.scala <username> <password> <entity-id> [endpoint-url]
 * 
 * Arguments:
 *   username     - Username for basic authentication
 *   password     - Password for basic authentication  
 *   entity-id    - String ID of the entity to retrieve
 *   endpoint-url - Optional custom endpoint URL (defaults to the provided endpoint)
 */
object JolokiaClient {

  val DEFAULT_ENDPOINT = "http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)"
  val TIMEOUT_SECONDS = 30

  def main(args: Array[String]): Unit = {
    if (args.length < 3) {
      printUsage()
      System.exit(1)
    }

    val username = args(0)
    val password = args(1)
    val entityId = args(2)
    val endpoint = if (args.length > 3) args(3) else DEFAULT_ENDPOINT

    println(s"Calling Jolokia JMX endpoint...")
    println(s"Endpoint: $endpoint")
    println(s"Entity ID: $entityId")
    println(s"Username: $username")
    println()

    callJolokiaEndpoint(username, password, entityId, endpoint) match {
      case Success(response) =>
        println("✅ Request successful!")
        println("Response:")
        println(response)
      case Failure(exception) =>
        println("❌ Request failed!")
        println(s"Error: ${exception.getMessage}")
        exception.printStackTrace()
        System.exit(1)
    }
  }

  def callJolokiaEndpoint(username: String, password: String, entityId: String, endpoint: String): Try[String] = {
    Try {
      // Create HTTP client
      val client = HttpClient.newBuilder()
        .connectTimeout(Duration.ofSeconds(TIMEOUT_SECONDS))
        .build()

      // Encode credentials for basic auth
      val credentials = s"$username:$password"
      val encodedCredentials = Base64.getEncoder.encodeToString(credentials.getBytes("UTF-8"))

      // Build the URL with the entity ID parameter
      val urlWithParams = s"$endpoint/$entityId"

      // Create HTTP request
      val request = HttpRequest.newBuilder()
        .uri(URI.create(urlWithParams))
        .header("Authorization", s"Basic $encodedCredentials")
        .header("Accept", "application/json")
        .header("Content-Type", "application/json")
        .GET()
        .timeout(Duration.ofSeconds(TIMEOUT_SECONDS))
        .build()

      println(s"Making GET request to: $urlWithParams")
      
      // Send request and get response
      val response = client.send(request, HttpResponse.BodyHandlers.ofString())
      
      println(s"Response status: ${response.statusCode()}")
      
      if (response.statusCode() >= 200 && response.statusCode() < 300) {
        response.body()
      } else {
        throw new RuntimeException(s"HTTP ${response.statusCode()}: ${response.body()}")
      }
    }
  }

  def printUsage(): Unit = {
    println("Jolokia JMX Client")
    println("==================")
    println()
    println("Usage:")
    println("  scala jolokia-client.scala <username> <password> <entity-id> [endpoint-url]")
    println()
    println("Arguments:")
    println("  username     - Username for basic authentication")
    println("  password     - Password for basic authentication")
    println("  entity-id    - String ID of the entity to retrieve")
    println("  endpoint-url - Optional custom endpoint URL")
    println()
    println("Default endpoint:")
    println(s"  $DEFAULT_ENDPOINT")
    println()
    println("Example:")
    println("  scala jolokia-client.scala myuser mypass entity123")
  }
}

JolokiaClient.main(args)
