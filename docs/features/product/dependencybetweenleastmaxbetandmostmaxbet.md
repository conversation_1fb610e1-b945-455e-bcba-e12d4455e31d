# Dependency between leastMaxBet and mostMaxBet
There is a dependency between leastMaxBet and mostMaxBet that states that leastMaxBet can't be bigger than mostMaxBet.
However, since we can receive Overrides from UI components and Feed updates for those fields, we need to consider and address scenarios in which this dependency is not assured.

## On Market Calculations
The idea to solve the issue while recalculating Market values, is basically limit the value of mostMaxBet to leastMaxBet value when leastMaxBet is bigger than mostMaxBet, except when 
we have a "Locked Override" on mostMaxBet. In that case, we limit the value of leastMaxBet to mostMaxBet value.

In order to address those scenarios, we implemented the following logic:
* If leasMaxBet > mostMaxBet, we create an "Unlocked Override" on mostMaxBet
* If leastMaxBet > mostMaxBet and mostMaxBet has a "Locked Override", then we create an "Unlocked Override" on leastMaxBet with mostMaxBet value
* If leastMaxBet > mostMaxBet and both have "Locked Overrides", then we remove the "Locked Override" on mostMaxBet and create an "Unlocked Override" on mostMaxBet with leastMaxBet value

In order to test this logic, we implemented JUnit tests considering the following test cases:

| MMB Lock | LMB Lock | Previously Limited | Inheritance vs Inheritance Status | Lock vs Inheritance Status | Lock vs Lock Status | Resulting MMB | Resulting LMB |
| -------- | -------- | ------------------ | --------------------------------- | -------------------------- | ------------------- | ------------- | ------------- |
| No       | No       | No                 | LMB == MMB                        | N/A                        | N/A                 | MMB           | LMB           |
| No       | No       | No                 | LMB < MMB                         | N/A                        | N/A                 | MMB           | LMB           |
| No       | No       | No                 | LMB > MMB                         | N/A                        | N/A                 | LMB           | LMB           |
| No       | No       | leastMaxBet        | LMB == MMB                        | N/A                        | N/A                 | MMB           | LMB           |
| No       | No       | leastMaxBet        | LMB < MMB                         | N/A                        | N/A                 | MMB           | LMB           |
| No       | No       | leastMaxBet        | LMB > MMB                         | N/A                        | N/A                 | LMB           | LMB           |
| No       | No       | mostMaxBet         | LMB == MMB                        | N/A                        | N/A                 | MMB           | LMB           |
| No       | No       | mostMaxBet         | LMB < MMB                         | N/A                        | N/A                 | MMB           | LMB           |
| No       | No       | mostMaxBet         | LMB > MMB                         | N/A                        | N/A                 | LMB           | LMB           |
| No       | Yes      | No                 | LMB == MMB                        | Lock(LMB) == MMB           | N/A                 | MMB           | LMB           |
| No       | Yes      | No                 | LMB < MMB                         | Lock(LMB) == MMB           | N/A                 | MMB           | LMB           |
| No       | Yes      | No                 | LMB > MMB                         | Lock(LMB) == MMB           | N/A                 | MMB           | LMB           |
| No       | Yes      | No                 | LMB == MMB                        | Lock(LMB) < MMB            | N/A                 | MMB           | LMB           |
| No       | Yes      | No                 | LMB < MMB                         | Lock(LMB) < MMB            | N/A                 | MMB           | LMB           |
| No       | Yes      | No                 | LMB > MMB                         | Lock(LMB) < MMB            | N/A                 | MMB           | LMB           |
| No       | Yes      | No                 | LMB == MMB                        | Lock(LMB) > MMB            | N/A                 | LMB           | LMB           |
| No       | Yes      | No                 | LMB < MMB                         | Lock(LMB) > MMB            | N/A                 | LMB           | LMB           |
| No       | Yes      | No                 | LMB > MMB                         | Lock(LMB) > MMB            | N/A                 | LMB           | LMB           |
| No       | Yes      | mostMaxBet         | LMB == MMB                        | Lock(LMB) == MMB           | N/A                 | MMB           | LMB           |
| No       | Yes      | mostMaxBet         | LMB < MMB                         | Lock(LMB) == MMB           | N/A                 | MMB           | LMB           |
| No       | Yes      | mostMaxBet         | LMB > MMB                         | Lock(LMB) == MMB           | N/A                 | MMB           | LMB           |
| No       | Yes      | mostMaxBet         | LMB == MMB                        | Lock(LMB) < MMB            | N/A                 | MMB           | LMB           |
| No       | Yes      | mostMaxBet         | LMB < MMB                         | Lock(LMB) < MMB            | N/A                 | MMB           | LMB           |
| No       | Yes      | mostMaxBet         | LMB > MMB                         | Lock(LMB) < MMB            | N/A                 | MMB           | LMB           |
| No       | Yes      | mostMaxBet         | LMB == MMB                        | Lock(LMB) > MMB            | N/A                 | LMB           | LMB           |
| No       | Yes      | mostMaxBet         | LMB < MMB                         | Lock(LMB) > MMB            | N/A                 | LMB           | LMB           |
| No       | Yes      | mostMaxBet         | LMB > MMB                         | Lock(LMB) > MMB            | N/A                 | LMB           | LMB           |
| Yes      | No       | No                 | LMB == MMB                        | LMB == Lock(MMB)           | N/A                 | MMB           | LMB           |
| Yes      | No       | No                 | LMB < MMB                         | LMB == Lock(MMB)           | N/A                 | MMB           | LMB           |
| Yes      | No       | No                 | LMB > MMB                         | LMB == Lock(MMB)           | N/A                 | MMB           | LMB           |
| Yes      | No       | No                 | LMB == MMB                        | LMB < Lock(MMB)            | N/A                 | MMB           | LMB           |
| Yes      | No       | No                 | LMB < MMB                         | LMB < Lock(MMB)            | N/A                 | MMB           | LMB           |
| Yes      | No       | No                 | LMB > MMB                         | LMB < Lock(MMB)            | N/A                 | MMB           | LMB           |
| Yes      | No       | No                 | LMB == MMB                        | LMB > Lock(MMB)            | N/A                 | MMB           | MMB           |
| Yes      | No       | No                 | LMB < MMB                         | LMB > Lock(MMB)            | N/A                 | MMB           | MMB           |
| Yes      | No       | No                 | LMB > MMB                         | LMB > Lock(MMB)            | N/A                 | MMB           | MMB           |
| Yes      | No       | leastMaxBet        | LMB == MMB                        | LMB == Lock(MMB)           | N/A                 | MMB           | LMB           |
| Yes      | No       | leastMaxBet        | LMB < MMB                         | LMB == Lock(MMB)           | N/A                 | MMB           | LMB           |
| Yes      | No       | leastMaxBet        | LMB > MMB                         | LMB == Lock(MMB)           | N/A                 | MMB           | LMB           |
| Yes      | No       | leastMaxBet        | LMB == MMB                        | LMB < Lock(MMB)            | N/A                 | MMB           | LMB           |
| Yes      | No       | leastMaxBet        | LMB < MMB                         | LMB < Lock(MMB)            | N/A                 | MMB           | LMB           |
| Yes      | No       | leastMaxBet        | LMB > MMB                         | LMB < Lock(MMB)            | N/A                 | MMB           | LMB           |
| Yes      | No       | leastMaxBet        | LMB == MMB                        | LMB > Lock(MMB)            | N/A                 | MMB           | MMB           |
| Yes      | No       | leastMaxBet        | LMB < MMB                         | LMB > Lock(MMB)            | N/A                 | MMB           | MMB           |
| Yes      | No       | leastMaxBet        | LMB > MMB                         | LMB > Lock(MMB)            | N/A                 | MMB           | MMB           |
| Yes      | Yes      | No                 | LMB == MMB                        | N/A                        | LMB == MMB          | MMB           | LMB           |
| Yes      | Yes      | No                 | LMB < MMB                         | N/A                        | LMB == MMB          | MMB           | LMB           |
| Yes      | Yes      | No                 | LMB > MMB                         | N/A                        | LMB == MMB          | MMB           | LMB           |
| Yes      | Yes      | No                 | LMB == MMB                        | N/A                        | LMB < MMB           | MMB           | LMB           |
| Yes      | Yes      | No                 | LMB < MMB                         | N/A                        | LMB < MMB           | MMB           | LMB           |
| Yes      | Yes      | No                 | LMB > MMB                         | N/A                        | LMB < MMB           | MMB           | LMB           |
| Yes      | Yes      | No                 | LMB == MMB                        | N/A                        | LMB > MMB           | LMB           | LMB           |
| Yes      | Yes      | No                 | LMB < MMB                         | N/A                        | LMB > MMB           | LMB           | LMB           |
| Yes      | Yes      | No                 | LMB > MMB                         | N/A                        | LMB > MMB           | LMB           | LMB           |


## On Event Update Feed Value
When receiving an update feed value for leastMaxBet or mostMaxBet, the feed update is mandatory and we have to accept and process it.
However, updating one of those values may lead to scenarios with leastMaxBet > mostMaxBet. In such scenarios, we need to create
an Unlocked Override for the attribute that is getting the feed update, with the previous value of the attribute. If there is no previous value, the Unlocked Override will get the value of the other attribute.
* Example 1:

| attribute | feed value | Override |
|-----------|------------|----------|
| lmb       | 3.0        | None     |
| mmb       | 7.0        | None     |

Then we receive feed update for lmb with value 9.0. The result is the following:

|attribute|feed value| Override                       |
|---------|----------|--------------------------------|
|lmb      |9.0       | Override(ALL -> Unlocked(3.0)) |
|mmb      |7.0       | None                           |

* Example 2 (We Override with the previous value):

|attribute|feed value| Override                    |
|---------|----------|-----------------------------|
|lmb      |3.0       | None                        |
|mmb      |7.0       | Override(NY -> Locked(8.0)) |

Then we receive feed update for lmb with value 9.0. The result is the following:

|attribute|feed value| Override                    |
|---------|----------|-----------------------------|
|lmb      |9.0       | Override(NY -> Locked(3.0)) |
|mmb      |7.0       | Override(NY -> Locked(8.0)) |

* Example 3 (when we have no previous feed value, we get the value from the other attribute):

|attribute| feed value | Override                    |
|---------|------------|-----------------------------|
|lmb      | None       | None                        |
|mmb      | 7.0        | Override(NY -> Locked(8.0)) |

Then we receive feed update for lmb with value 9.0. The result is the following:

|attribute|feed value| Override                    |
|---------|----------|-----------------------------|
|lmb      |9.0       | Override(NY -> Locked(8.0)) |
|mmb      |7.0       | Override(NY -> Locked(8.0)) |

## On Event and Market Override and reset Operations
When executing Override commands on leastMaxBet or mostMaxBet, we first validate if the command execution will lead to scenarios with leastMaxBet > mostMaxBet.
If it does, then we remove the operation from the command and execute the remaining operations of the command. 
If the command ends up having no operations to do, then the command os rejected and therefore not executed.
Reset command is also checked, and if the result is a scenario with leastMaxBet > mostMaxBet, the command is rejected and therefore not executed.
These rules are implemented for both Event and Market Override commands.
