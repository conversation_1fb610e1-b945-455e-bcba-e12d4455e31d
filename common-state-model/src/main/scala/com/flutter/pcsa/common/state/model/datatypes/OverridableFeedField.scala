package com.flutter.pcsa.common.state.model.datatypes

import com.flutter.pcsa.common.Global.Instance

object OverridableFeedField {
  def empty[T]: OverridableFeedField[T] = OverridableFeedField(Map.empty, None)
}

final case class OverridableFeedField[T](overrides: Map[Instance, Lockable[Option[T]]], value: Option[T]) extends Overridable[T] with FeedValue[T] {
  def isEmpty: Boolean = value.isEmpty && overrides.isEmpty
}
