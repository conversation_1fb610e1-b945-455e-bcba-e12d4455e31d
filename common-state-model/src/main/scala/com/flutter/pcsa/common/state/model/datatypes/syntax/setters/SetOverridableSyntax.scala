package com.flutter.pcsa.common.state.model.datatypes.syntax.setters

import com.flutter.pcsa.common.Global.{GLOBAL, Instance}
import com.flutter.pcsa.common.state.model.datatypes.{
  Overridable,
  OverridableAndInheritableFeedField,
  OverridableAndInheritableField,
  OverridableFeedField,
  OverridableField
}
import com.flutter.pcsa.overrides.{FlagDTO, OverridesDTO}
import com.flutter.pcsa.common.state.model.datatypes.{Lockable, Locked, Unlocked}

import scala.language.postfixOps

trait SetOverridableSyntax {
  implicit class UpdateOverrides[A, C[X] <: Overridable[X]](instance: C[A]) {

    def updateOverrides(overrides: Map[Instance, Lockable[Option[A]]]): C[A] = {
      instance match {
        case obj @ OverridableFeedField(_, _)                  => obj.copy(overrides = overrides).asInstanceOf[C[A]]
        case obj @ OverridableField(_)                         => obj.copy(overrides = overrides).asInstanceOf[C[A]]
        case obj @ OverridableAndInheritableField(_, _)        => obj.copy(overrides = overrides).asInstanceOf[C[A]]
        case obj @ OverridableAndInheritableFeedField(_, _, _) => obj.copy(overrides = overrides).asInstanceOf[C[A]]
        case obj @ _                                           => throw new Exception(s"Not expected kind of overridable - ${obj.getClass.getSimpleName}")
      }
    }
  }

  implicit class OverridableOps[A, C[X] <: Overridable[X]](overridable: C[A]) {

    final def applyOverrideDTO(overrides: Option[OverridesDTO[A]]): C[A] = {
      overrides
        .map(overridable.applyOverrideDTO)
        .getOrElse(overridable)
    }

    final def applyOverrideDTO(overrides: OverridesDTO[A]): C[A] = {
      def haveGlobalReset: OverridesDTO[A] => Boolean = _.overrides.exists(x => GLOBAL.equals(x._1) && x._2.flag.eq(FlagDTO.RESET))

      if (haveGlobalReset(overrides)) {
        overridable.globalReset()
      } else {
        overrides.overrides.foldLeft(overridable) { (o, x) =>
          x._2.flag match {
            case FlagDTO.OVERRIDE | FlagDTO.UNLOCK => o.overrideInstance(x._1, Unlocked(x._2.instanceOverride))
            case FlagDTO.LOCK                      => o.overrideInstance(x._1, Locked(x._2.instanceOverride))
            case FlagDTO.RESET                     => o.removeInstanceOverride(x._1)
            case FlagDTO.UNKNOWN                   => throw new Exception(s"Not expected Flag = UNKNOWN - $x")
          }
        }
      }
    }

    final def globalReset(): C[A] = overridable.updateOverrides(Map.empty)

    final def removeInstanceOverride(instance: Instance): C[A] = {
      overridable.updateOverrides(overridable.overrides.filterNot(_._1.equals(instance)))
    }

    final def overrideValue(lockable: Lockable[Option[A]]): C[A] = {
      overridable.updateOverrides(Map(GLOBAL -> lockable))
    }

    final def overrideInstances(instancesOverride: Map[Instance, Lockable[Option[A]]]): C[A] =
      overridable.updateOverrides(overridable.overrides ++ instancesOverride)

    final def unlockInstance(instance: Instance): C[A] = {
      overridable.overrides.get(instance) match {
        case Some(lockable @ Locked(_)) =>
          overrideInstance(instance, lockable.release)
        case _ => overridable.updateOverrides(overridable.overrides)
      }
    }

    final def overrideInstance(instance: Instance, lockable: Lockable[Option[A]]): C[A] =
      overridable.updateOverrides(overridable.overrides ++ Map(instance -> lockable))

    final def removeAllUnlocked(): C[A] = {
      overridable.updateOverrides(overridable.overrides.filter(_._2.isLocked))
    }
  }
}
