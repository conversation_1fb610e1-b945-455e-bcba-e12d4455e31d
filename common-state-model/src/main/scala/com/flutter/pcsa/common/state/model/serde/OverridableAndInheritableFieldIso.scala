package com.flutter.pcsa.common.state.model.serde

import com.flutter.pcsa.common.state.model.datatypes.OverridableAndInheritableField
import com.flutter.pcsa.common.state.model.proto._
import com.flutter.pcsa.common.state.model.serde.LockableOptionIso.{LockableOptionBoolIso, LockableOptionDoubleIso, LockableOptionFloatIso, LockableOptionIntIso, LockableOptionLongIso, LockableOptionStringIso}
import monocle.Iso

object OverridableAndInheritableFieldIso {

  val OverridableAndInheritableFieldIntIso: Iso[OverridableAndInheritableField[Int], OverridableAndInheritableFieldInt32Proto] =
    Iso[OverridableAndInheritableField[Int], OverridableAndInheritableFieldInt32Proto] { im =>
      OverridableAndInheritableFieldInt32Proto(
        overrides = im.overrides.map(i => (i._1 -> LockableOptionIntIso.get(i._2))),
        inheritData = im.inheritData.map(i => (i._1 -> OptionalInt32Proto(i._2)))
      )
    } { pb =>
      OverridableAndInheritableField(
        overrides = pb.overrides.map(i => (i._1 -> LockableOptionIntIso.reverseGet(i._2))),
        inheritData = pb.inheritData.map(i => (i._1 -> i._2.value))
      )
    }

  val OverridableAndInheritableFieldLongIso: Iso[OverridableAndInheritableField[Long], OverridableAndInheritableFieldInt64Proto] =
    Iso[OverridableAndInheritableField[Long], OverridableAndInheritableFieldInt64Proto] { im =>
      OverridableAndInheritableFieldInt64Proto(
        overrides = im.overrides.map(i => (i._1 -> LockableOptionLongIso.get(i._2))),
        inheritData = im.inheritData.map(i => (i._1 -> OptionalInt64Proto(i._2)))
      )
    } { pb =>
      OverridableAndInheritableField(
        overrides = pb.overrides.map(i => (i._1 -> LockableOptionLongIso.reverseGet(i._2))),
        inheritData = pb.inheritData.map(i => (i._1 -> i._2.value))
      )
    }

  val OverridableAndInheritableFieldBoolIso: Iso[OverridableAndInheritableField[Boolean], OverridableAndInheritableFieldBoolProto] =
    Iso[OverridableAndInheritableField[Boolean], OverridableAndInheritableFieldBoolProto] { im =>
      OverridableAndInheritableFieldBoolProto(
        overrides = im.overrides.map(i => (i._1 -> LockableOptionBoolIso.get(i._2))),
        inheritData = im.inheritData.map(i => (i._1 -> OptionalBoolProto(i._2)))
      )
    } { pb =>
      OverridableAndInheritableField(
        overrides = pb.overrides.map(i => (i._1 -> LockableOptionBoolIso.reverseGet(i._2))),
        inheritData = pb.inheritData.map(i => (i._1 -> i._2.value))
      )
    }

  val OverridableAndInheritableFieldDoubleIso: Iso[OverridableAndInheritableField[Double], OverridableAndInheritableFieldDoubleProto] =
    Iso[OverridableAndInheritableField[Double], OverridableAndInheritableFieldDoubleProto] { im =>
      OverridableAndInheritableFieldDoubleProto(
        overrides = im.overrides.map(i => (i._1 -> LockableOptionDoubleIso.get(i._2))),
        inheritData = im.inheritData.map(i => (i._1 -> OptionalDoubleProto(i._2)))
      )
    } { pb =>
      OverridableAndInheritableField(
        overrides = pb.overrides.map(i => (i._1 -> LockableOptionDoubleIso.reverseGet(i._2))),
        inheritData = pb.inheritData.map(i => (i._1 -> i._2.value))
      )
    }

  val OverridableAndInheritableFieldFloatIso: Iso[OverridableAndInheritableField[Float], OverridableAndInheritableFieldFloatProto] =
    Iso[OverridableAndInheritableField[Float], OverridableAndInheritableFieldFloatProto] { im =>
      OverridableAndInheritableFieldFloatProto(
        overrides = im.overrides.map(i => (i._1 -> LockableOptionFloatIso.get(i._2))),
        inheritData = im.inheritData.map(i => (i._1 -> OptionalFloatProto(i._2)))
      )
    } { pb =>
      OverridableAndInheritableField(
        overrides = pb.overrides.map(i => (i._1 -> LockableOptionFloatIso.reverseGet(i._2))),
        inheritData = pb.inheritData.map(i => (i._1 -> i._2.value))
      )
    }

  val OverridableAndInheritableFieldStringIso: Iso[OverridableAndInheritableField[String], OverridableAndInheritableFieldStringProto] =
    Iso[OverridableAndInheritableField[String], OverridableAndInheritableFieldStringProto] { im =>
      OverridableAndInheritableFieldStringProto(
        overrides = im.overrides.map(i => (i._1 -> LockableOptionStringIso.get(i._2))),
        inheritData = im.inheritData.map(i => (i._1 -> OptionalStringProto(i._2)))
      )
    } { pb =>
      OverridableAndInheritableField(
        overrides = pb.overrides.map(i => (i._1 -> LockableOptionStringIso.reverseGet(i._2))),
        inheritData = pb.inheritData.map(i => (i._1 -> i._2.value))
      )
    }
}
