package com.flutter.pcsa.common.state.model.datatypes.syntax.setters

import com.flutter.pcsa.common.Global.Instance
import com.flutter.pcsa.common.state.model.datatypes._
import com.flutter.pcsa.common.state.model.datatypes.fixture.OverrideFixture
import com.flutter.pcsa.common.state.model.datatypes.syntax.getters.implicits.getFeedValueSyntax.GetFeedValue
import com.flutter.pcsa.common.state.model.datatypes.syntax.setters.implicits.setFeedValueSyntax.SetFeedValue
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.{TableDrivenPropertyChecks, TableFor3}

class SetFeedValueSyntaxSpec extends FeedValueSyntaxFixture {

  "SetFeedValueSyntax" should "for updateWithOnCreation should throw if get FeedField structure that do not support Override" in {
    assertThrows[Exception](
      FeedField[String](None).updateWithOnCreation(Some(Map("urn:i:FD:US-NY" -> Some("USNyVal"), "ALL" -> Some("ALLVal"))))
    )
    assertThrows[Exception](
      InheritableFeedField[String](Map.empty, None).updateWithOnCreation(Some(Map("urn:i:FD:US-NY" -> Some("USNyVal"), "ALL" -> Some("ALLVal"))))
      )
  }

  forAll(onCreateCases) { (desc, previous, update, expected) =>
    it should s"$desc" in {
      assertResult(expected)(previous.updateWithOnCreation(update))
    }
  }

  forAll(cases) { (caseObject, expectedResult, expectedException) =>
    it should s"for updateFeedValue update FeedValue with ${caseObject.getClass.getSimpleName}" in {
      val feedValueObj = caseObject
      if (!expectedException) {
        assertResult(Some(expectedResult))(feedValueObj.getFeedValue)

        val newFeedValue = Some(genString.sample.get)
        val result = feedValueObj.updateFeedValue(newFeedValue)
        assertResult(newFeedValue)(result.value)
      } else {
        assertThrows[Exception](feedValueObj.getFeedValue)
        assertThrows[Exception] {
          val newFeedValue = Some(genString.sample.get)
          feedValueObj.updateFeedValue(newFeedValue)
        }
      }
    }
  }

  it should "apply a default value by returning an object with only the provided value" in {
    val defaultValue = genString.sample.get

    val previousValue = genSomeString.sample.get
    val previousOverridableFeedFieldWithValues = genOverridableFeedStringField.sample.get
    val previousInheritableFeedFieldWithValues = genInheritableStringField.sample.get
    val previousOverridableAndInheritableFeedFieldWithValues =
      OverridableAndInheritableFeedField(
        value = None,
        overrides = Map(genString.sample.get -> genLockedOptionalStringField.sample.get),
        inheritData = Map.empty
      )

    val table = Table(
      ("victim", "defaultValue", "expectation"),
      (
        OverridableAndInheritableFeedField.empty[String],
        defaultValue,
        OverridableAndInheritableFeedField.empty.copy(value = Some(defaultValue))
      ),
      (
        OverridableFeedField.empty[String],
        defaultValue,
        OverridableFeedField.empty.copy(value = Some(defaultValue))
      ),
      (
        InheritableFeedField.empty[String],
        defaultValue,
        InheritableFeedField.empty.copy(value = Some(defaultValue))
      ),
      (
        FeedField.empty[String],
        defaultValue,
        FeedField.empty.copy(value = Some(defaultValue))
      ),
      (
        previousOverridableAndInheritableFeedFieldWithValues,
        defaultValue,
        previousOverridableAndInheritableFeedFieldWithValues
      ),
      (
        previousOverridableFeedFieldWithValues,
        defaultValue,
        previousOverridableFeedFieldWithValues
      ),
      (
        previousInheritableFeedFieldWithValues,
        defaultValue,
        previousInheritableFeedFieldWithValues
      ),
      (
        FeedField(value = previousValue),
        defaultValue,
        FeedField(value = previousValue)
      )
    )
    forAll(table) { (victim, defaultValue, expectation) =>
      victim.applyDefault(defaultValue) shouldBe expectation
    }
  }

  it should "apply a default value throw exception if datatype doesn't support Feed Value" in {

    val table = Table(
      ("victim", "defaultValue"),
      (
        Custom(value = None),
        genString.sample.get
      )
    )

    forAll(table) { (victim, defaultValue) =>
      val exception = intercept[Exception](victim.applyDefault(defaultValue))
      exception shouldBe a[Exception]
      exception.getMessage shouldBe "applyDefault: Not expected kind of feed - Custom"
    }
  }
}

trait FeedValueSyntaxFixture
    extends TableDrivenPropertyChecks with AnyFlatSpecLike with OverrideFixture with Matchers with GenOverridableFeedField with GenInheritableFeedField {

  val cases: TableFor3[FeedValue[String], String, Boolean] = Table(
    ("caseObject", "expectedResult", "expectedException"),
    (OverridableFeedField[String](Map.empty, Some("SomeOverridableField")), "SomeOverridableField", false),
    (OverridableAndInheritableFeedField[String](Map.empty, Map.empty, Some("OverridableAndInheritableField")), "OverridableAndInheritableField", false),
    (InheritableFeedField[String](Map.empty, Some("InheritableField")), "InheritableField", false),
    (Custom(Some("customClass")), "", true)
  )

  val onCreateCases = Table(
    ("should", "previous", "update", "expected"),
    (
      "for updateWithOnCreation handle OverridableFeedField",
      OverridableFeedField[String](Map.empty, None),
      Some(Map("urn:i:FD:US-NY" -> Some("USNyVal"))),
      OverridableFeedField(Map("urn:i:FD:US-NY" -> Unlocked(Some("USNyVal"))), None)
    ),
    (
      "for updateWithOnCreation handle OverridableAndInheritableFeedField",
      OverridableAndInheritableFeedField[String](Map.empty, Map.empty, None),
      Some(Map("urn:i:FD:US-NY" -> Some("USNyVal"), "ALL" -> Some("ALLVal"))),
      OverridableAndInheritableFeedField(Map("urn:i:FD:US-NY" -> Unlocked(Some("USNyVal"))), Map.empty, Some("ALLVal"))
    ),
    (
      "for updateWithOnCreation ignore update is feed is set",
      OverridableAndInheritableFeedField[String](Map.empty, Map.empty, Some("someOld")),
      Some(Map("urn:i:FD:US-NY" -> Some("USNyVal"), "ALL" -> Some("ALLVal"))),
      OverridableAndInheritableFeedField(Map.empty, Map.empty, Some("someOld"))
    ),
    (
      "for updateWithOnCreation ignore on empty update",
      OverridableAndInheritableFeedField[String](Map.empty, Map.empty, Some("someOld")),
      None,
      OverridableAndInheritableFeedField(Map.empty, Map.empty, Some("someOld"))
    ),
    (
      "for updateWithOnCreation ignore on update with empty map",
      OverridableAndInheritableFeedField[String](Map.empty, Map.empty, Some("someOld")),
      Some(Map.empty[Instance, Option[String]]),
      OverridableAndInheritableFeedField(Map.empty, Map.empty, Some("someOld"))
    )
  )

  case class Custom(value: Option[String]) extends FeedValue[String] {
    override def isEmpty: Boolean = value.isEmpty
  }
}
