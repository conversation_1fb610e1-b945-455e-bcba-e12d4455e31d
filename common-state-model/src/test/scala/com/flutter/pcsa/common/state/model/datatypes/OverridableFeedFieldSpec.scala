package com.flutter.pcsa.common.state.model.datatypes

import com.flutter.pcsa.common.state.model.datatypes.Locked
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.{TableDrivenPropertyChecks, TableFor2}

class OverridableFeedFieldSpec extends AnyFlatSpecLike with TableDrivenPropertyChecks with Matchers {

  "OverridableFeedField" should "return empty true if empty" in {
    val overridableFieldEmpty: OverridableFeedField[String] = OverridableFeedField.empty
    val overridableFieldWithOverride = OverridableFeedField(Map("test" -> Locked[Option[String]](Some("str"))), None)
    val overridableFieldWithValue = OverridableFeedField(Map(), Some("test"))

    val table: TableFor2[OverridableFeedField[String], Boolean] = Table(
      ("value", "expectation"),
      (overridableFieldEmpty, true),
      (overridableFieldWithOverride, false),
      (overridableFieldWithValue, false)
    )
    forAll(table) { (value, expectation) =>
      value.isEmpty shouldBe expectation
    }
  }
}
