package com.flutter.pcsa.common.state.model.datatypes

import com.flutter.pcsa.common.GenCommon
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.{TableDrivenPropertyChecks, TableFor2}

class FeedFieldSpec extends AnyFlatSpecLike with TableDrivenPropertyChecks with Matchers with GenCom<PERSON> {

  it should "return empty true if empty" in {
    val feedFieldEmpty: FeedField[String] = FeedField.empty
    val feedFieldWithValue = FeedField(genSomeString.sample.get)

    val table: TableFor2[FeedField[String], Boolean] = Table(
      ("value", "expectation"),
      (feedFieldEmpty, true),
      (feedFieldWithValue, false)
    )
    forAll(table) { (value, expectation) =>
      value.isEmpty shouldBe expectation
    }
  }
}
