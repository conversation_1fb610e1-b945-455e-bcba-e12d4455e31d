package com.flutter.pcsa.common.state.model.model.generators

import com.flutter.pcsa.common.datatypes.GenPlatform
import com.flutter.pcsa.common.state.model.model.{EntityMetadata, EntityModel, LiveEntityMetadata}
import org.scalacheck.Gen

trait GenLiveEntityMetadata extends GenPlatform {

  def genLiveEntityMetadata[M <: EntityModel](genModel: Gen[M]): Gen[LiveEntityMetadata[M]] = for {
    model <- genModel
    feedCreated <- genBoolean
    hierarchyCreated <- genBoolean
    isLive <- genBoolean
  } yield LiveEntityMetadata(
    model = model,
    feedCreated = feedCreated,
    hierarchyCreated = hierarchyCreated,
    isLive = isLive
  )

  def invalidLiveEntityMetadata[M <: EntityModel](modelGen: Gen[M]): LiveEntityMetadata[M] = (for {
    model <- modelGen
  } yield LiveEntityMetadata(
    model = model,
    feedCreated = false,
    hierarchyCreated = false,
    isLive = true
  )).sample.get

  def validLiveEntityMetadata[M <: EntityModel](modelGen: Gen[M]): LiveEntityMetadata[M] = (for {
    model <- modelGen
  } yield LiveEntityMetadata(
    model = model,
    feedCreated = true,
    hierarchyCreated = true,
    isLive = true
  )).sample.get

}
