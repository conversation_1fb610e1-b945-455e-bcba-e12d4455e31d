<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.flutter.pcsa</groupId>
    <artifactId>pcsa-service</artifactId>
    <packaging>pom</packaging>
    <version>0.0.1-SNAPSHOT</version>

    <modules>
        <module>common</module>
        <module>common-state-model</module>
        <module>core</module>
        <module>apps</module>
        <module>launcher</module>
        <module>functional-tests-risk</module>
        <module>functional-tests-market</module>
        <module>capacity-tests</module>
        <module>overrides</module>
        <module>functional-tests-util</module>
    </modules>

    <properties>
        <app.name>pcsa-service</app.name>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.encoding>UTF-8</project.encoding>
        <project.root.basedir>${project.basedir}</project.root.basedir>
        <!-- Scala -->
        <scala.binary.version>2.13</scala.binary.version>
        <scala.version>${scala.binary.version}.10</scala.version>
        <!-- Scala Style -->
        <scalastyle.plugin.version>1.0.0</scalastyle.plugin.version>
        <scalastyle.path>${project.root.basedir}/scalastyle_config.xml</scalastyle.path>
        <!-- Scala Check -->
        <scala-check.version>1.18.1</scala-check.version>
        <!-- Typesafe -->
        <typesafe.version>1.4.3</typesafe.version>
        <!-- Cats -->
        <cats.version>2.5.0</cats.version>
        <!-- Guava -->
        <guava.version>33.3.1-jre</guava.version>
        <!-- Metrics -->
        <metrics-core.version>3.2.2</metrics-core.version>
        <common-scala-metrics.version>2.0.0</common-scala-metrics.version>
        <kamon.version>2.7.3</kamon.version>
        <kanela-agent.version>1.0.18</kanela-agent.version>
        <kamon-jmx-reporter.version>3.0.0</kamon-jmx-reporter.version>
        <!-- Proto -->
        <proto.version>3.19.6</proto.version>
        <protobuf.version>3.17.3</protobuf.version>
        <protoc-jar-maven-plugin.version>3.11.4</protoc-jar-maven-plugin.version>
        <!-- Cassandra -->
        <cassandra-driver.version>4.17.0</cassandra-driver.version>
        <!-- Netty -->
        <netty-handler.version>4.1.94.Final</netty-handler.version>
        <netty.version>3.10.6.Final</netty.version>
        <pekko.version>1.1.2</pekko.version>
        <pekko-management.version>1.1.0</pekko-management.version>
        <pekko-http.version>1.1.0</pekko-http.version>
        <pekko-persistence.version>1.0.0</pekko-persistence.version>
        <pekko-connectors-cassandra.version>1.0.2</pekko-connectors-cassandra.version>
        <pekko-connectors-kafka.version>1.1.0</pekko-connectors-kafka.version>
        <pekko-connectors-amqp.version>1.0.2</pekko-connectors-amqp.version>
        <flutter-pekko.version>1.0.0</flutter-pekko.version>
        <aeron.version>1.45.1</aeron.version>
        <!-- Jackson -->
        <jackson.version>2.17.2</jackson.version>
        <!-- Reactive streams -->
        <reactive-streams.version>1.0.4</reactive-streams.version>
        <!-- FIP Contracts -->
        <fip-contracts.version>2.0.1</fip-contracts.version>
        <!-- IPMA Contracts -->
        <feeds.event.model.version>1.75.1</feeds.event.model.version>
        <!-- PBC Contracts -->
        <pbc-constracts.version>1.24</pbc-constracts.version>
        <!-- Sportex Contracts -->
        <sportex-contracts.version>1.2.2</sportex-contracts.version>
        <!-- Micrometer -->
        <micrometer-jvm-extras.version>0.2.2</micrometer-jvm-extras.version>
        <!-- Disruptor -->
        <disruptor.version>3.4.4</disruptor.version>
        <!-- Jaeger -->
        <jaeger.version>1.8.1</jaeger.version>
        <!-- AWS -->
        <aws-sigv4-auth-cassandra-java-driver-plugin.version>4.0.8</aws-sigv4-auth-cassandra-java-driver-plugin.version>
        <aws-msk-iam-auth.version>2.3.2</aws-msk-iam-auth.version>
        <!-- STS -->
        <aws-sts.version>2.31.21</aws-sts.version>
        <open-tracing-kafka.version>0.1.15</open-tracing-kafka.version>
        <!-- Kafka -->
        <kafka-clients.version>3.9.1</kafka-clients.version>
        <!-- Serializers -->
        <serializers.version>1.1.87</serializers.version>
        <!-- Contracts -->
        <contracts.version>1.2.47</contracts.version>
        <product-catalogue-risk-domain-contract.version>1.0.4</product-catalogue-risk-domain-contract.version>
        <product-catalogue-market-domain-contract.version>0.0.5</product-catalogue-market-domain-contract.version>
        <product-catalogue-overrides-contract.version>0.0.9</product-catalogue-overrides-contract.version>
        <product-catalogue-risk-stream.version>0.0.8</product-catalogue-risk-stream.version>
        <pegdown.version>1.6.0</pegdown.version>
        <!-- Versioned -->
        <versioned.version>1.5.0</versioned.version>
        <!-- OpenTracing -->
        <opentracing.version>0.33.0</opentracing.version>
        <open-tracing-kafka.version>0.1.15</open-tracing-kafka.version>
        <!-- Json4s -->
        <json4s.version>3.6.12</json4s.version>
        <!-- Slf4j -->
        <slf4j.version>2.0.9</slf4j.version>
        <log4j2.slf4j.version>2.22.1</log4j2.slf4j.version>
        <log4j2.api.version>13.1.0</log4j2.api.version>
        <!-- Flexmark -->
        <flexmark.version>0.64.8</flexmark.version>
        <!-- Monocle -->
        <monocle.version>2.1.0</monocle.version>
        <!-- ScalaTest -->
        <scalatest.version>3.2.17</scalatest.version>
        <scalatest-maven-plugin.version>2.2.0</scalatest-maven-plugin.version>
        <scala-test-plus-scala-check.version>3.2.16.0</scala-test-plus-scala-check.version>
        <scala-test-plus-scala-mockito.version>3.1.0.0</scala-test-plus-scala-mockito.version>
        <!-- Mockito -->
        <mockito-scala.version>1.17.44</mockito-scala.version>
        <byte-buddy-agent.version>1.15.10</byte-buddy-agent.version>
        <!-- Scala PB -->
        <scalapb-json4s.version>0.11.1</scalapb-json4s.version>
        <scalapb-runtime.version>0.11.15</scalapb-runtime.version>
        <!-- Java -->
        <java.version>17</java.version>
        <!-- Maven -->
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.root.basedir>${project.basedir}</project.root.basedir>
        <maven-jar-plugin-version>3.4.2</maven-jar-plugin-version>
        <maven-surefire-plugin.version>3.5.3</maven-surefire-plugin.version>
        <scala-maven-plugin.version>4.9.2</scala-maven-plugin.version>
        <docker-maven-plugin.version>0.46.0</docker-maven-plugin.version>
        <maven-dependency-plugin.version>3.8.1</maven-dependency-plugin.version>
        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
        <!-- Scoverage -->
        <scoverage.plugin.version>2.1.0</scoverage.plugin.version>
        <scoverage.scalacPluginVersion>2.1.1</scoverage.scalacPluginVersion>
        <!-- Sonar -->
        <sonar.projectBaseDir>.</sonar.projectBaseDir>
        <sonar.sources>pom.xml,src/main/scala</sonar.sources>
        <sonar.tests>src/test/</sonar.tests>
        <sonar.tests.exclusions>capacity-tests/**</sonar.tests.exclusions>
        <sonar.exclusions>functional-tests-*/**,capacity-tests/**</sonar.exclusions>
        <sonar.core.codeCoveragePlugin>scoverage</sonar.core.codeCoveragePlugin>
        <sonar.scala.coverage.reportPaths>./common/target/scoverage.xml,./core/target/scoverage.xml,./common-state-model/target/scoverage.xml,./catalogue-model/target/scoverage.xml,./launcher/target/scoverage.xml,./apps/rs/target/scoverage.xml,./apps/rd/target/scoverage.xml,./apps/ms/target/scoverage.xml,./apps/md/target/scoverage.xml,./overrides/target/scoverage.xml</sonar.scala.coverage.reportPaths>

        <!-- Component testing -->
        <test-framework.version>5.7.11</test-framework.version>
        <instruction-model.version>1.2</instruction-model.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <maven-failsafe-plugin.version>3.5.2</maven-failsafe-plugin.version>
        <maven-surefire-junit5-tree-reporter.version>1.3.0</maven-surefire-junit5-tree-reporter.version>
        <!-- RPM -->
        <rpm-maven-plugin.version>2.2.0</rpm-maven-plugin.version>
        <rpm.version>0.0.1</rpm.version>
        <maven-assembly-plugin.version>2.2-beta-3</maven-assembly-plugin.version>
        <!-- Scalatra -->
        <scalatra.version>2.7.1</scalatra.version>
        <!-- Jetty Webapp -->
        <jetty-webapp-version>9.2.15.v20160210</jetty-webapp-version>
        <!-- plugins -->
        <maven-antrun-plugin.version>1.7</maven-antrun-plugin.version>
        <build-helper-maven-plugin.version>3.6.0</build-helper-maven-plugin.version>
        <!-- skips-->
        <scalaTest.skip>false</scalaTest.skip>
        <coverage.skip>false</coverage.skip>
        <scalastyle.skip>false</scalastyle.skip>
        <maven-jar-plugin.skip>false</maven-jar-plugin.skip>
        <skipTests>false</skipTests>

        <hazelcast.version>5.4.0</hazelcast.version>
        <hawtio.version>2.16.3</hawtio.version>
        <!-- hawtio authentication -->
        <karaf.version>4.3.4</karaf.version>
        <osgi.version>8.0.0</osgi.version>

        <kafbat-ui.version>1.0.0</kafbat-ui.version>
        <json.version>20250517</json.version>
        <mockito-java.version>5.18.0</mockito-java.version>
        <junit.version>5.12.2</junit.version>
        <sdnotify.version>1.5</sdnotify.version>
        <logging-scala.version>1.0.0</logging-scala.version>
        <logging.version>1.0.1</logging.version>
        <log4j-layout-template-json.version>2.22.1</log4j-layout-template-json.version>
        <jolokia-agent-jvm.version>2.2.9</jolokia-agent-jvm.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- project -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-layout-template-json</artifactId>
                <version>${log4j-layout-template-json.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>overrides</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>overrides</artifactId>
                <version>${project.version}</version>
                <type>test-jar</type>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
                <type>test-jar</type>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>common-state-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>common-state-model</artifactId>
                <version>${project.version}</version>
                <type>test-jar</type>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>core</artifactId>
                <version>${project.version}</version>
                <type>test-jar</type>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>md</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>rd</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>rs</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.pcsa</groupId>
                <artifactId>ms</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- dropwizard -->
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>${metrics-core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.sl4j</groupId>
                        <artifactId>sl4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jetty9</artifactId>
                <version>${metrics-core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.sl4j</groupId>
                        <artifactId>sl4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.errorprone</groupId>
                        <artifactId>error_prone_annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- logging -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api-scala_${scala.binary.version}</artifactId>
                <version>${log4j2.api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j2-impl</artifactId>
                <version>${log4j2.slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.sharedplatforms</groupId>
                <artifactId>logging-scala</artifactId>
                <version>${logging-scala.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.sharedplatforms</groupId>
                <artifactId>logging</artifactId>
                <version>${logging.version}</version>
            </dependency>
            <!-- disruptor -->
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>
            <!-- jaeger -->
            <dependency>
                <groupId>io.jaegertracing</groupId>
                <artifactId>jaeger-client</artifactId>
                <version>${jaeger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.sl4j</groupId>
                        <artifactId>sl4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.jaegertracing</groupId>
                <artifactId>jaeger-core</artifactId>
                <version>${jaeger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.sl4j</groupId>
                        <artifactId>sl4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.jaegertracing</groupId>
                <artifactId>jaeger-thrift</artifactId>
                <version>${jaeger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.sl4j</groupId>
                        <artifactId>sl4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- akka -->
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.version}</version>
            </dependency>
            <!-- kafka -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka-clients.version}</version>
            </dependency>
            <!-- pekko -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-bom_${scala.binary.version}</artifactId>
                <version>${pekko.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-slf4j_${scala.binary.version}</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <!-- pekko-http -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-http-bom_${scala.binary.version}</artifactId>
                <version>${pekko-http.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- pekko-management -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-management-cluster-http_${scala.binary.version}</artifactId>
                <version>${pekko-management.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-management-cluster-bootstrap_${scala.binary.version}</artifactId>
                <version>${pekko-management.version}</version>
            </dependency>
            <!-- pekko-persistence-cassandra -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-persistence-cassandra_${scala.binary.version}</artifactId>
                <version>${pekko-persistence.version}</version>
            </dependency>
            <!--pekko-amqp-->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-connectors-amqp_${scala.binary.version}</artifactId>
                <version>${pekko-connectors-amqp.version}</version>
            </dependency>
            <!-- pekko-stream-kafka -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-connectors-kafka_${scala.binary.version}</artifactId>
                <version>${pekko-connectors-kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-connectors-kafka-testkit_${scala.binary.version}</artifactId>
                <version>${pekko-connectors-kafka.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.sl4j</groupId>
                        <artifactId>sl4j-api</artifactId>
                    </exclusion>
                </exclusions>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-discovery-kubernetes-api_${scala.binary.version}</artifactId>
                <version>${pekko-management.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-lease-kubernetes_${scala.binary.version}</artifactId>
                <version>${pekko-management.version}</version>
            </dependency>
            <!-- pekko-remote -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-remote_${scala.binary.version}</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <!-- aeron -->
            <dependency>
                <groupId>io.aeron</groupId>
                <artifactId>aeron-driver</artifactId>
                <version>${aeron.version}</version>
            </dependency>
            <dependency>
                <groupId>io.aeron</groupId>
                <artifactId>aeron-client</artifactId>
                <version>${aeron.version}</version>
            </dependency>

            <!-- cassandra-driver -->
            <dependency>
                <groupId>com.datastax.oss</groupId>
                <artifactId>java-driver-core</artifactId>
                <version>${cassandra-driver.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.lightbend.akka</groupId>
                        <artifactId>akka-stream-alpakka-cassandra_${scala.binary.version}</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.sl4j</groupId>
                        <artifactId>sl4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>software.aws.mcs</groupId>
                <artifactId>aws-sigv4-auth-cassandra-java-driver-plugin</artifactId>
                <version>${aws-sigv4-auth-cassandra-java-driver-plugin.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sts</artifactId>
                <version>${aws-sts.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.msk</groupId>
                <artifactId>aws-msk-iam-auth</artifactId>
                <version>${aws-msk-iam-auth.version}</version>
            </dependency>
            <!-- netty -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty-handler.version}</version>
            </dependency>
            <!-- open-tracing -->
            <dependency>
                <groupId>io.opentracing.contrib</groupId>
                <artifactId>opentracing-kafka-client</artifactId>
                <version>${open-tracing-kafka.version}</version>
            </dependency>
            <!-- github -->
            <dependency>
                <groupId>com.flutter.product.catalogue.risk.domain.contract</groupId>
                <artifactId>product-catalogue-risk-domain-contract-scalapb_${scala.binary.version}</artifactId>
                <version>${product-catalogue-risk-domain-contract.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.product.catalogue.market.domain.contract</groupId>
                <artifactId>product-catalogue-market-domain-contract-scalapb_${scala.binary.version}</artifactId>
                <version>${product-catalogue-market-domain-contract.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.product.catalogue.overrides.contract</groupId>
                <artifactId>product-catalogue-overrides-contract-scalapb_${scala.binary.version}</artifactId>
                <version>${product-catalogue-overrides-contract.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.product.catalogue.risk.stream.contract</groupId>
                <artifactId>product-catalogue-risk-stream-contract-scalapb_${scala.binary.version}</artifactId>
                <version>${product-catalogue-risk-stream.version}</version>
            </dependency>
            <!-- jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <!-- json4s-jackson -->
            <dependency>
                <groupId>org.json4s</groupId>
                <artifactId>json4s-jackson_${scala.binary.version}</artifactId>
                <version>${json4s.version}</version>
            </dependency>
            <!-- proto -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${proto.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${proto.version}</version>
                <scope>runtime</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.errorprone</groupId>
                        <artifactId>error_prone_annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.code.gson</groupId>
                        <artifactId>gson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.9</version>
            </dependency>
            <dependency>
                <groupId>org.json4s</groupId>
                <artifactId>json4s-core_${scala.binary.version}</artifactId>
                <version>${json4s.version}</version>
            </dependency>
            <!-- Json4s native -->
            <dependency>
                <groupId>org.json4s</groupId>
                <artifactId>json4s-native_${scala.binary.version}</artifactId>
                <version>${json4s.version}</version>
            </dependency>
            <!-- scalapb -->
            <dependency>
                <groupId>com.thesamet.scalapb</groupId>
                <artifactId>scalapb-runtime_${scala.binary.version}</artifactId>
                <version>${scalapb-runtime.version}</version>
            </dependency>
            <!-- cats -->
            <dependency>
                <groupId>org.typelevel</groupId>
                <artifactId>cats-core_${scala.binary.version}</artifactId>
                <version>${cats.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.typelevel</groupId>
                        <artifactId>cats-kernel_${scala.binary.version}</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.typelevel</groupId>
                <artifactId>cats-effect_${scala.binary.version}</artifactId>
                <version>${cats.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.typelevel</groupId>
                        <artifactId>cats-kernel_${scala.binary.version}</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.typelevel</groupId>
                <artifactId>cats-kernel_${scala.binary.version}</artifactId>
                <version>${cats.version}</version>
            </dependency>

            <!-- pegdown -->
            <dependency>
                <groupId>org.pegdown</groupId>
                <artifactId>pegdown</artifactId>
                <version>${pegdown.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm-tree</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm-analysis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- scala-check -->
            <dependency>
                <groupId>org.scalacheck</groupId>
                <artifactId>scalacheck_${scala.binary.version}</artifactId>
                <version>${scala-check.version}</version>
            </dependency>
            <!-- versioned -->
            <dependency>
                <groupId>com.ppb</groupId>
                <artifactId>versioned_${scala.binary.version}</artifactId>
                <version>${versioned.version}</version>
            </dependency>
            <!-- explicitDeps -->
            <dependency>
                <groupId>com.typesafe</groupId>
                <artifactId>config</artifactId>
                <version>${typesafe.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <!-- flexmark -->
            <dependency>
                <groupId>com.vladsch.flexmark</groupId>
                <artifactId>flexmark</artifactId>
                <version>${flexmark.version}</version>
            </dependency>
            <dependency>
                <groupId>com.vladsch.flexmark</groupId>
                <artifactId>flexmark-profile-pegdown</artifactId>
                <version>${flexmark.version}</version>
            </dependency>
            <dependency>
                <groupId>com.vladsch.flexmark</groupId>
                <artifactId>flexmark-util</artifactId>
                <version>${flexmark.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.mweirauch</groupId>
                <artifactId>micrometer-jvm-extras</artifactId>
                <version>${micrometer-jvm-extras.version}</version>
            </dependency>
            <!--Flutter internal dependencies-->
            <dependency>
                <groupId>com.ppb.feeds</groupId>
                <artifactId>ppb-feeds-instruction-model-scalapb_${scala.binary.version}</artifactId>
                <version>${fip-contracts.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ppb.feeds</groupId>
                <artifactId>ppb-feeds-instruction-model-java8</artifactId>
                <version>${fip-contracts.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.ppb.feeds</groupId>
                <artifactId>ppb-feeds-event-model-scalapb_${scala.binary.version}</artifactId>
                <version>${feeds.event.model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ppb.feeds</groupId>
                <artifactId>ppb-feeds-event-model-java8</artifactId>
                <version>${feeds.event.model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.trading</groupId>
                <artifactId>global-outbound-mapping-model-proto</artifactId>
                <version>${sportex-contracts.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter.trading</groupId>
                <artifactId>global-outbound-mapping-model-java-8</artifactId>
                <version>${sportex-contracts.version}</version>
            </dependency>
            <dependency>
                <groupId>com.flutter</groupId>
                <artifactId>flutter-pekko_${scala.binary.version}</artifactId>
                <version>${flutter-pekko.version}</version>
            </dependency>

            <!-- monocle -->
            <dependency>
                <groupId>com.github.julien-truffaut</groupId>
                <artifactId>monocle-core_${scala.binary.version}</artifactId>
                <version>${monocle.version}</version>
            </dependency>
            <!-- scalatest -->
            <dependency>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest_${scala.binary.version}</artifactId>
                <version>${scalatest.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- scala-test-plus -->
            <dependency>
                <groupId>org.scalatestplus</groupId>
                <artifactId>scalacheck-1-17_${scala.binary.version}</artifactId>
                <version>${scala-test-plus-scala-check.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.scalatest</groupId>
                        <artifactId>scalatest-core_${scala.binary.version}</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.scalatestplus</groupId>
                <artifactId>mockito-1-10_${scala.binary.version}</artifactId>
                <version>${scala-test-plus-scala-mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-scala_${scala.binary.version}</artifactId>
                <version>${mockito-scala.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.thesamet.scalapb</groupId>
                <artifactId>scalapb-json4s_${scala.binary.version}</artifactId>
                <version>${scalapb-json4s.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${byte-buddy-agent.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${byte-buddy-agent.version}</version>
            </dependency>
            <!-- Component testing -->
            <dependency>
                <groupId>com.ppb.feeds</groupId>
                <artifactId>test-framework</artifactId>
                <version>${test-framework.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.activemq</groupId>
                        <artifactId>activemq-broker</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.rabbitmq</groupId>
                        <artifactId>amqp-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.cassandra</groupId>
                        <artifactId>java-driver-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.cassandra</groupId>
                        <artifactId>java-driver-query-builder</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ppb.platform.fp</groupId>
                <artifactId>instruction-model</artifactId>
                <version>${instruction-model.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.protobuf</groupId>
                        <artifactId>protobuf-java-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ppb.feeds.common</groupId>
                <artifactId>feeds-ramp-model_2_13</artifactId>
                <version>${pbc-constracts.version}</version>
            </dependency>
            <dependency>
                <groupId>org.simplify4u</groupId>
                <artifactId>slf4j-mock</artifactId>
                <version>2.3.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.hazelcast</groupId>
                <artifactId>hazelcast</artifactId>
                <version>${hazelcast.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hazelcast</groupId>
                <artifactId>hazelcast</artifactId>
                <version>${hazelcast.version}</version>
                <classifier>tests</classifier>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.kafbat.ui</groupId>
                <artifactId>serde-api</artifactId>
                <version>${kafbat-ui.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${json.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-java.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- Scalatra -->
            <dependency>
                <groupId>org.scalatra</groupId>
                <artifactId>scalatra-json_${scala.binary.version}</artifactId>
                <version>${scalatra.version}</version>
            </dependency>
            <dependency>
                <groupId>org.scalatra</groupId>
                <artifactId>scalatra_${scala.binary.version}</artifactId>
                <version>${scalatra.version}</version>
            </dependency>
            <dependency>
                <groupId>org.scalatra</groupId>
                <artifactId>scalatra-swagger_${scala.binary.version}</artifactId>
                <version>${scalatra.version}</version>
            </dependency>
            <dependency>
                <groupId>org.scalatra</groupId>
                <artifactId>scalatra-scalatest_${scala.binary.version}</artifactId>
                <version>${scalatra.version}</version>
            </dependency>
            <!-- Jetty Webapp -->
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-webapp</artifactId>
                <version>${jetty-webapp-version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-client</artifactId>
                <version>${jetty-webapp-version}</version>
            </dependency>
            <dependency>
                <groupId>io.hawt</groupId>
                <artifactId>hawtio-embedded</artifactId>
                <version>${hawtio.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.hawt</groupId>
                        <artifactId>hawtio-default</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-server</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ppb.platform.sb</groupId>
                <artifactId>common-scala-metrics_${scala.binary.version}</artifactId>
                <version>${common-scala-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.kamon</groupId>
                <artifactId>kamon-core_${scala.binary.version}</artifactId>
                <version>${kamon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.kamon</groupId>
                <artifactId>kamon-executors_${scala.binary.version}</artifactId>
                <version>${kamon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.kamon</groupId>
                <artifactId>kamon-pekko_${scala.binary.version}</artifactId>
                <version>${kamon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.kamon</groupId>
                <artifactId>kamon-prometheus_${scala.binary.version}</artifactId>
                <version>${kamon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.kamon</groupId>
                <artifactId>kanela-agent</artifactId>
                <version>${kanela-agent.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jolokia</groupId>
                <artifactId>jolokia-agent-jvm</artifactId>
                <classifier>javaagent</classifier>
                <version>${jolokia-agent-jvm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ppb.platform.sb</groupId>
                <artifactId>kamon-jmx-reporter_${scala.binary.version}</artifactId>
                <version>${kamon-jmx-reporter.version}</version>
            </dependency>
            <dependency>
                <groupId>info.faljse</groupId>
                <artifactId>SDNotify</artifactId>
                <version>${sdnotify.version}</version>
            </dependency>

            <!-- HAWTIO authentication -->
            <dependency>
                <groupId>org.apache.karaf.jaas</groupId>
                <artifactId>org.apache.karaf.jaas.modules</artifactId>
                <version>${karaf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.karaf.jaas</groupId>
                <artifactId>org.apache.karaf.jaas.boot</artifactId>
                <version>${karaf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.osgi</groupId>
                <artifactId>osgi.core</artifactId>
                <version>${osgi.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>dist-snapshot</id>
            <uniqueVersion>false</uniqueVersion>
            <releases>
                <enabled>false</enabled>
            </releases>
        </snapshotRepository>
    </distributionManagement>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>net.alchim31.maven</groupId>
                    <artifactId>scala-maven-plugin</artifactId>
                    <version>${scala-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>scala-compile-first</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>add-source</goal>
                                <goal>compile</goal>
                            </goals>
                            <configuration>
                                <scalaCompatVersion>${scala.binary.version}</scalaCompatVersion>
                                <scalaVersion>${scala.version}</scalaVersion>
                                <recompileMode>incremental</recompileMode>
                                <args>
                                    <arg>-encoding</arg>
                                    <arg>${project.build.sourceEncoding}</arg> <!-- Specify character encoding used by source files. -->
                                    <arg>-deprecation</arg> <!-- Emit warning and location for usages of deprecated APIs. -->
                                    <arg>-feature</arg> <!-- Emit warning and location for usages of features that should be imported explicitly. -->
                                    <arg>-explaintypes</arg> <!-- Explain type errors in more detail. -->
                                    <arg>-language:existentials</arg> <!-- Existential types (besides wildcard types) can be written and inferred -->
                                    <arg>-language:higherKinds</arg> <!-- Allow higher-kinded types -->
                                    <arg>-unchecked</arg> <!-- Enable additional warnings where generated code depends on assumptions. -->
                                </args>
                                <jvmArgs>
                                    <jvmArg>-Xms512m</jvmArg>
                                    <jvmArg>-Xmx1024m</jvmArg>
                                </jvmArgs>
                            </configuration>
                        </execution>
                        <execution>
                            <id>scala-test-compile</id>
                            <phase>process-test-resources</phase>
                            <goals>
                                <goal>testCompile</goal>
                            </goals>
                            <configuration>
                                <scalaCompatVersion>${scala.binary.version}</scalaCompatVersion>
                                <scalaVersion>${scala.version}</scalaVersion>
                                <recompileMode>incremental</recompileMode>
                                <args>
                                    <arg>-encoding</arg>
                                    <arg>${project.build.sourceEncoding}</arg> <!-- Specify character encoding used by source files. -->
                                    <arg>-deprecation</arg> <!-- Emit warning and location for usages of deprecated APIs. -->
                                    <arg>-feature</arg> <!-- Emit warning and location for usages of features that should be imported explicitly. -->
                                    <arg>-explaintypes</arg> <!-- Explain type errors in more detail. -->
                                    <arg>-language:existentials</arg> <!-- Existential types (besides wildcard types) can be written and inferred -->
                                    <arg>-language:higherKinds</arg> <!-- Allow higher-kinded types -->
                                    <arg>-unchecked</arg> <!-- Enable additional warnings where generated code depends on assumptions. -->
                                </args>
                                <jvmArgs>
                                    <jvmArg>-Xms512m</jvmArg>
                                    <jvmArg>-Xmx1024m</jvmArg>
                                </jvmArgs>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin-version}</version>
                    <configuration>
                        <skip>${maven-jar-plugin.skip}</skip>
                        <finalName>${project.artifactId}-${project.version}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>test-compile</phase>
                            <goals>
                                <goal>test-jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- enable scalatest -->
                <plugin>
                    <groupId>org.scalatest</groupId>
                    <artifactId>scalatest-maven-plugin</artifactId>
                    <version>${scalatest-maven-plugin.version}</version>
                    <configuration>
                        <argLine>-DskipTests=${scalaTest.skip}</argLine>
                        <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                        <junitxml>.</junitxml>
                        <filereports>WDF TestSuite.txt</filereports>
                        <skipTests>${scalaTest.skip}</skipTests>
                        <parallel>true</parallel>
                        <argLine>-Dfile.encoding=UTF-8</argLine>
                        <testFailureIgnore>false</testFailureIgnore>
                        <forkMode>once</forkMode>
                        <parallel>true</parallel>
                    </configuration>
                    <executions>
                        <execution>
                            <id>unit-and-it-test</id>
                            <goals>
                                <goal>test</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- code coverage -->
                <plugin>
                    <groupId>org.scoverage</groupId>
                    <artifactId>scoverage-maven-plugin</artifactId>
                    <version>${scoverage.plugin.version}</version>
                    <configuration>
                        <skip>${coverage.skip}</skip>
                        <scalaVersion>${scala.version}</scalaVersion>
                        <minimumCoverage>80</minimumCoverage>
                        <failOnMinimumCoverage>true</failOnMinimumCoverage>
                        <aggregate>false</aggregate>
                        <highlighting>true</highlighting>
                        <additionalForkedProjectProperties>skipTests=false;scalaTest.skip=false</additionalForkedProjectProperties>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>prepare-package</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- Add protobuf-maven-plugin and generate -->
                <plugin>
                    <groupId>com.github.os72</groupId>
                    <artifactId>protoc-jar-maven-plugin</artifactId>
                    <version>${protoc-jar-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <protocVersion>${protobuf.version}</protocVersion>
                                <addProtoSources>no</addProtoSources>
                                <includeMavenTypes>direct</includeMavenTypes>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven-dependency-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>mkdir-generated-sources</id>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <phase>generate-sources</phase>
                            <configuration>
                                <target>
                                    <mkdir dir="${project.build.directory}/generated-sources" />
                                </target>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${build-helper-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>add-source</goal>
                            </goals>
                            <configuration>
                                <sources>
                                    <source>${project.build.directory}/proto</source>
                                </sources>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>default-test</id>
                            <phase>none</phase>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker-maven-plugin.version}</version>
                    <configuration>
                        <images>
                            <image>
                                <name>pcsa-service</name>
                                <build>
                                    <from>eclipse-temurin:17.0.9_9-jdk-centos7</from>
                                    <assemblies>
                                        <assembly>
                                            <name>pcsa-dependencies</name>
                                            <descriptorRef>dependencies</descriptorRef>
                                            <targetDir>/docker/lib</targetDir>
                                        </assembly>
                                        <assembly>
                                            <name>pcsa-artifacts</name>
                                            <descriptorRef>artifact</descriptorRef>
                                            <targetDir>/docker</targetDir>
                                        </assembly>
                                    </assemblies>
                                    <runCmds>
                                        <run>mkdir /local/ &amp;&amp; echo "include required(\"local-docker.conf\")" > /local/application.conf</run>
                                        <!-- Extract the hawtio to root folder because Jetty needs a clean war path of Hawtio-->
                                        <run>mkdir /extracted &amp;&amp; cd /extracted &amp;&amp; jar xf /docker/launcher-${project.version}.jar</run>
                                        <run>mv /extracted/hawtio-default-${hawtio.version}.war .</run>
                                        <run>rm -Rf /extracted</run>
                                    </runCmds>
                                    <entryPoint>
                                        <shell>java -cp "/local/:/docker/launcher-${project.version}.jar:/docker/lib/*" Boot</shell>
                                    </entryPoint>
                                    <ports>
                                        <port>9999</port>
                                        <port>8080</port>
                                        <port>7878</port>
                                        <port>25520</port>
                                    </ports>
                                    <tags>
                                        <tag>latest</tag>
                                        <tag>${project.version}</tag>
                                    </tags>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>
                <!-- Plugins for component testing -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>UTF-8</encoding>
                    </configuration>
                    <executions>
                        <execution>
                            <id>default-compile</id>
                            <phase>none</phase>
                        </execution>
                        <execution>
                            <id>default-testCompile</id>
                            <phase>none</phase>
                        </execution>
                    </executions>
                </plugin>
                <!-- code coverage -->
                <plugin>
                    <groupId>org.scalastyle</groupId>
                    <artifactId>scalastyle-maven-plugin</artifactId>
                    <version>${scalastyle.plugin.version}</version>
                    <configuration>
                        <verbose>false</verbose>
                        <failOnViolation>true</failOnViolation>
                        <includeTestSourceDirectory>true</includeTestSourceDirectory>
                        <failOnWarning>false</failOnWarning>
                        <sourceDirectory>${basedir}/src/main/scala</sourceDirectory>
                        <testSourceDirectory>${basedir}/src/test/scala</testSourceDirectory>
                        <configLocation>${scalastyle.path}</configLocation>
                        <outputFile>${project.build.directory}/scalastyle-output.xml</outputFile>
                        <outputEncoding>${project.encoding}</outputEncoding>
                        <skip>${scalastyle.skip}</skip>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>validate</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>rpm-maven-plugin</artifactId>
                    <version>${rpm-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <!-- skip running all checks -->
            <id>skipChecks</id>
            <properties>
                <coverage.skip>true</coverage.skip>
                <scalaTest.skip>true</scalaTest.skip>
                <scalaStyle.skip>true</scalaStyle.skip>
                <skipTests>true</skipTests>
            </properties>
        </profile>
    </profiles>
</project>

<!-- Change the line below to force a re-hashing of the pipe cache -->
<!-- pintom3 2025-02-05-17:52 -->
