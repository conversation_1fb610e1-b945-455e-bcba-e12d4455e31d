package com.flutter.pcsa.capacity.tests.datasource

import com.flutter.pcsa.capacity.tests.datasource.model._
import com.flutter.pcsa.capacity.tests.jmeter.PropsKeys.HWM
import com.flutter.pcsa.capacity.tests.util.{FIPInbound, SingletonInstance}
import com.ppb.feeds.model.context2.{EntityId, EventAndMarkets}
import com.ppb.feeds.model.instruction2.{Instruction, InstructionType}
import org.apache.jmeter.protocol.java.sampler.JavaSamplerContext

class FIPRiskSettingsDataSource(override val conf: Configuration, override val changeRatio: EventTypeDataSet => Int)
    extends AbstractCatalogueDataSource[FIPInbound] {

  override def generate(context: JavaSamplerContext): FIPInbound = {
    val hwm = context.getParameter(HWM).toInt
    val eventType = getRandomEventsType(hwm)
    val event = eventType.getRandomEvent

    FIPInbound(
      key = event.id,
      message = generateEventRiskSettingsUpdate(eventType, event, hwm)
    )
  }

  private def generateEventRiskSettingsUpdate(eventType: EventType, event: Event, hwm: Int): Instruction = {
    val payload = EventAndMarkets(
      event = Some(
        com.ppb.feeds.model.context2.Event(
          id = Some(EntityId(externalId = Some(event.id))),
          riskManagement = Some(genFIPRiskManagement.sample.get)
        )
      )
    )
    createInstruction(eventType, payload, InstructionType.UPDATE, s"Ramp-${event.id}", hwm)
  }
}

object FIPRiskSettingsDataSource extends SingletonInstance[FIPRiskSettingsDataSource] {

  def apply(conf: Configuration, changeRatio: EventTypeDataSet => Int): FIPRiskSettingsDataSource =
    getInstance(() => new FIPRiskSettingsDataSource(conf, changeRatio))
}
