package com.flutter.pcsa.capacity.tests.jmeter

import com.flutter.pcsa.capacity.tests.config.ConfigLoader
import com.flutter.pcsa.capacity.tests.datasource.GTHPreCreateCatalogueDataSource
import com.flutter.pcsa.capacity.tests.datasource.model.EventTypeDataSet
import com.flutter.pcsa.capacity.tests.util.GTHInbound
import org.apache.jmeter.protocol.java.sampler.JavaSamplerContext

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, Future}

class GTHPreCreateCatalogueSampler extends AbstractKafkaSampler[String, String, List[GTHInbound]] {

  override def samplerLogic(context: JavaSamplerContext): String = {
    val inbounds = catalogueDataSource.get.generate(context)
    val futures = inbounds.map(inbound => produce(inbound))
    Await.result(Future.sequence(futures), 5.minutes)


    "Finish creating Superclasses, Subclasses, Event Types, Market Types and MarketTypeLinks"
  }

  override def setupTest(context: JavaSamplerContext): Unit = {
    super.setupTest(context)

    val configuration = ConfigLoader.loadPerformanceConfiguration(context)
    this.catalogueDataSource = Some(GTHPreCreateCatalogueDataSource(configuration, getChangeRatio))
  }

  override def getChangeRatio: EventTypeDataSet => Int = _ => 0
}
