package com.flutter.pcsa.capacity.tests.jmeter

import com.flutter.pcsa.capacity.tests.config.ConfigLoader
import com.flutter.pcsa.capacity.tests.datasource.GMAMarketTypeLinkOverrideDataSource
import com.flutter.pcsa.capacity.tests.datasource.model.EventTypeDataSet
import com.flutter.pcsa.capacity.tests.jmeter.PropsKeys.{COUNTER, COUNTER_DEFAULT, HWM, HWM_DEFAULT}
import com.flutter.pcsa.capacity.tests.util.GMAInbound
import org.apache.jmeter.config.Arguments
import org.apache.jmeter.protocol.java.sampler.JavaSamplerContext

import scala.concurrent.Await
import scala.concurrent.duration.DurationInt

class GMAMarketTypeLinkOverrideSampler extends AbstractKafkaSampler[String, Array[Byte], GMAInbound] {

  override def getDefaultParameters: Arguments = {
    val defaultParameters = super.getDefaultParameters()
    defaultParameters.addArgument(COUNTER, COUNTER_DEFAULT)
    defaultParameters.addArgument(HWM, HWM_DEFAULT)
    defaultParameters
  }

  override def setupTest(context: JavaSamplerContext): Unit = {
    super.setupTest(context)

    val configuration = ConfigLoader.loadPerformanceConfiguration(context)
    catalogueDataSource = Some(GMAMarketTypeLinkOverrideDataSource(configuration, getChangeRatio))
  }

  override def samplerLogic(context: JavaSamplerContext): String = {
    val inbound = catalogueDataSource.get.generate(context)
    produce(inbound)
    "Produced"
  }

  override def getChangeRatio: EventTypeDataSet => Int = _.gmaOverrideChangesRatio
}
