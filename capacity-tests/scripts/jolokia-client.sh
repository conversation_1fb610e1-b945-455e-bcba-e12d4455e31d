#!/bin/bash

# Jolokia JMX Client Shell Script
# This script provides a convenient wrapper for calling the Jolokia JMX endpoint

set -e

# Default values
endpoint="http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)"
username=jmxadmin
password=password03

# Function to print usage
print_usage() {
    echo "Jolokia JMX Client"
    echo "=================="
    echo ""
    echo "Usage:"
    echo "  $0 <entity-id>"
    echo ""
    echo "Arguments:"
    echo "  entity-id    - String ID of the entity to retrieve"
    echo ""
    echo "Examples:"
    echo "  $0 entity123"
    echo ""
}

# Function to make HTTP request using curl
call_jolokia_endpoint() {
    local entity_id="$1"

    # Build the full URL with entity ID parameter
    local full_url="${endpoint}/${entity_id}"
    
    echo "🔗 Calling Jolokia JMX endpoint..."
    echo "📍 Endpoint: $endpoint"
    echo "🆔 Entity ID: $entity_id"
    echo ""
    
    # Make the HTTP request
    echo "📡 Making GET request to: $full_url"
    echo ""
    
    local response
    local http_code
    
    # Use curl to make the request with basic auth
    response=$(curl -s -w "\n%{http_code}" \
        --user "${username}:${password}" \
        --header "Accept: application/json" \
        --header "Content-Type: application/json" \
        --connect-timeout 30 \
        --max-time 60 \
        "$full_url")


    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    response_body=$(echo "$response" | head -n1)
    
    echo "📊 Response status: $http_code"
    echo ""
    
    if [[ "$http_code" -ge 200 && "$http_code" -lt 300 ]]; then
        echo "✅ Request successful!"
        echo "📄 Response:"
#        echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
    else
        echo "❌ Request failed!"
        echo "📄 Error response:"
        echo "$response_body"
        exit 1
    fi
}

# Main script logic
main() {
    # Check if help is requested
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then
        print_usage
        exit 0
    fi
    
    if [[ $# -ge 1 ]]; then
        entity_id="$1"
    fi
    
    # Validate required arguments
    if [[ -z "$entity_id" ]]; then
        echo "❌ Error: Missing required arguments"
        echo ""
        print_usage
        exit 1
    fi
    
    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        echo "❌ Error: curl is required but not installed"
        exit 1
    fi
    
    # Call the endpoint
    call_jolokia_endpoint "$entity_id"
}

# Run main function with all arguments
main "$@"
