#!/usr/bin/env python3

"""
Jolokia JMX Client Script

This script calls a Jolokia JMX endpoint to retrieve entity data using basic authentication.

Usage:
    python3 jolokia-client.py <username> <password> <entity-id> [endpoint-url]

Arguments:
    username     - Username for basic authentication
    password     - Password for basic authentication  
    entity-id    - String ID of the entity to retrieve
    endpoint-url - Optional custom endpoint URL (defaults to the provided endpoint)
"""

import sys
import json
import base64
import urllib.request
import urllib.error
from typing import Optional

DEFAULT_ENDPOINT = "http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)"
TIMEOUT_SECONDS = 30


def print_usage():
    """Print usage information."""
    print("Jolokia JMX Client")
    print("==================")
    print()
    print("Usage:")
    print("  python3 jolokia-client.py <username> <password> <entity-id> [endpoint-url]")
    print()
    print("Arguments:")
    print("  username     - Username for basic authentication")
    print("  password     - Password for basic authentication")
    print("  entity-id    - String ID of the entity to retrieve")
    print("  endpoint-url - Optional custom endpoint URL")
    print()
    print("Default endpoint:")
    print(f"  {DEFAULT_ENDPOINT}")
    print()
    print("Examples:")
    print("  python3 jolokia-client.py myuser mypass entity123")
    print("  python3 jolokia-client.py myuser mypass entity123 http://custom-host:9999/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)")
    print()
    print("Environment variables:")
    print("  JOLOKIA_USERNAME - Default username (can be overridden by argument)")
    print("  JOLOKIA_PASSWORD - Default password (can be overridden by argument)")
    print("  JOLOKIA_ENDPOINT - Default endpoint (can be overridden by argument)")


def call_jolokia_endpoint(username: str, password: str, entity_id: str, endpoint: str) -> str:
    """
    Call the Jolokia JMX endpoint with basic authentication.
    
    Args:
        username: Username for basic authentication
        password: Password for basic authentication
        entity_id: String ID of the entity to retrieve
        endpoint: JMX endpoint URL
        
    Returns:
        Response body as string
        
    Raises:
        Exception: If the request fails
    """
    # Build the full URL with entity ID parameter
    full_url = f"{endpoint}/{entity_id}"
    
    print("🔗 Calling Jolokia JMX endpoint...")
    print(f"📍 Endpoint: {endpoint}")
    print(f"🆔 Entity ID: {entity_id}")
    print(f"👤 Username: {username}")
    print()
    print(f"📡 Making GET request to: {full_url}")
    print()
    
    # Encode credentials for basic auth
    credentials = f"{username}:{password}"
    encoded_credentials = base64.b64encode(credentials.encode('utf-8')).decode('ascii')
    
    # Create request
    request = urllib.request.Request(full_url)
    request.add_header("Authorization", f"Basic {encoded_credentials}")
    request.add_header("Accept", "application/json")
    request.add_header("Content-Type", "application/json")
    
    try:
        # Send request
        with urllib.request.urlopen(request, timeout=TIMEOUT_SECONDS) as response:
            status_code = response.getcode()
            response_body = response.read().decode('utf-8')
            
            print(f"📊 Response status: {status_code}")
            print()
            
            if 200 <= status_code < 300:
                print("✅ Request successful!")
                print("📄 Response:")
                try:
                    # Try to pretty-print JSON
                    parsed_json = json.loads(response_body)
                    print(json.dumps(parsed_json, indent=2))
                except json.JSONDecodeError:
                    # If not JSON, print as-is
                    print(response_body)
                return response_body
            else:
                raise Exception(f"HTTP {status_code}: {response_body}")
                
    except urllib.error.HTTPError as e:
        error_body = e.read().decode('utf-8') if e.fp else str(e)
        print(f"📊 Response status: {e.code}")
        print()
        print("❌ Request failed!")
        print("📄 Error response:")
        print(error_body)
        raise Exception(f"HTTP {e.code}: {error_body}")
        
    except urllib.error.URLError as e:
        print("❌ Request failed!")
        print(f"📄 Network error: {e.reason}")
        raise Exception(f"Network error: {e.reason}")


def main():
    """Main function."""
    import os
    
    # Check if help is requested
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        print_usage()
        sys.exit(0)
    
    # Use environment variables as defaults
    username = os.environ.get("JOLOKIA_USERNAME", "")
    password = os.environ.get("JOLOKIA_PASSWORD", "")
    endpoint = os.environ.get("JOLOKIA_ENDPOINT", DEFAULT_ENDPOINT)
    
    # Parse command line arguments
    if len(sys.argv) >= 2:
        username = sys.argv[1]
    
    if len(sys.argv) >= 3:
        password = sys.argv[2]
    
    if len(sys.argv) >= 4:
        entity_id = sys.argv[3]
    else:
        entity_id = ""
    
    if len(sys.argv) >= 5:
        endpoint = sys.argv[4]
    
    # Validate required arguments
    if not username or not password or not entity_id:
        print("❌ Error: Missing required arguments")
        print()
        print_usage()
        sys.exit(1)
    
    try:
        call_jolokia_endpoint(username, password, entity_id, endpoint)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
