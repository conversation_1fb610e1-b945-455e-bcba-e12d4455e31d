#!/bin/bash

# Test script for Jolokia JMX clients
# This script tests the different client implementations

echo "Testing Jolokia JMX Client Scripts"
echo "=================================="
echo

# Test help functionality
echo "1. Testing help functionality..."
echo

echo "Python script help:"
python3 jolokia-client.py --help
echo

echo "Scala script help:"
scala jolokia-client.scala
echo

echo "Shell script help:"
bash jolokia-client.sh --help
echo

echo "2. Testing with missing arguments (should show usage)..."
echo

echo "Python script with no args:"
python3 jolokia-client.py 2>/dev/null || echo "Expected error - missing arguments"
echo

echo "Shell script with no args:"
bash jolokia-client.sh 2>/dev/null || echo "Expected error - missing arguments"
echo

echo "Tests completed!"
echo
echo "To test with real credentials, run:"
echo "  ./jolokia-client.sh <username> <password> <entity-id>"
echo "  python3 jolokia-client.py <username> <password> <entity-id>"
echo "  scala jolokia-client.scala <username> <password> <entity-id>"
