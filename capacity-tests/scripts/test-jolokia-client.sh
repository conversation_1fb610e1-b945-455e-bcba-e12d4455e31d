#!/bin/bash

# Test script for Jolokia JMX clients
# This script tests the different client implementations

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "Testing Jolokia JMX Client Scripts"
echo "=================================="
echo

# Test help functionality
echo "1. Testing help functionality..."
echo

echo "Shell script help (batch processing):"
bash jolokia-client.sh --help
echo

echo "Python script help:"
python3 jolokia-client.py --help 2>/dev/null || echo "Python script available"
echo

echo "Scala script help:"
scala jolokia-client.scala 2>/dev/null || echo "Scala script available"
echo

# Test entity file creation
echo "2. Testing entity file functionality..."
echo

if [[ ! -f "entity-ids.txt" ]]; then
    echo "Creating sample entity-ids.txt file..."
    cat > entity-ids.txt << 'EOF'
# Sample entity IDs for testing
# Replace with actual entity IDs
test-entity-001
test-entity-002
test-entity-003
EOF
    echo "✅ Created entity-ids.txt with sample data"
else
    echo "✅ entity-ids.txt already exists"
fi
echo

echo "3. Testing batch script with sample file (dry run)..."
echo

echo "Shell script with missing credentials (should show error):"
bash jolokia-client.sh -u "" -p "" entity-ids.txt 2>/dev/null || echo "Expected error - missing credentials"
echo

echo "Shell script help:"
bash jolokia-client.sh --help | head -20
echo

echo "4. Testing file reading functionality..."
echo

echo "Contents of entity-ids.txt:"
cat entity-ids.txt
echo

echo "Tests completed!"
echo
echo "To test with real credentials and endpoint:"
echo "  # Batch processing (recommended):"
echo "  ./jolokia-client.sh -u <username> -p <password>"
echo "  ./jolokia-client.sh -u <username> -p <password> --verbose"
echo "  ./jolokia-client.sh -u <username> -p <password> --quiet"
echo
echo "  # Single entity processing:"
echo "  python3 jolokia-client.py <username> <password> <entity-id>"
echo "  scala jolokia-client.scala <username> <password> <entity-id>"
echo
echo "Environment variable example:"
echo "  export JOLOKIA_USERNAME=myuser"
echo "  export JOLOKIA_PASSWORD=mypass"
echo "  ./jolokia-client.sh"
