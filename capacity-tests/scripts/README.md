# Jolokia JMX Client Scripts

This directory contains scripts to call the Jolokia JMX endpoint for retrieving entity data from the PCSA service.

## Available Scripts

### 1. <PERSON> Script (`jolokia-client.sh`) - **BATCH PROCESSING**
A bash script that reads entity IDs from a file and processes them in batch with comprehensive reporting.

**Requirements:**
- bash
- curl

**Configuration:**
Edit the script to modify these hardcoded values:
- `ENDPOINT` - J<PERSON> endpoint URL
- `USERNAME` - Username for authentication
- `PASSWORD` - Password for authentication

**Usage:**
```bash
./jolokia-client.sh <entity-file>
```

### 2. Scala Script (`jolokia-client.scala`)
A Scala script that uses Java's built-in HTTP client.

**Requirements:**
- Scala runtime
- Java 11+

**Usage:**
```bash
scala jolokia-client.scala <username> <password> <entity-id> [endpoint-url]
```

### 3. Python Script (`jolokia-client.py`)
A Python script that uses urllib for HTTP requests.

**Requirements:**
- Python 3.6+

**Usage:**
```bash
python3 jolokia-client.py <username> <password> <entity-id> [endpoint-url]
```

## Entity File Format

The shell script reads entity IDs from a text file with the following format:

```
# Comments start with # and are ignored
# Empty lines are also ignored

entity-001
entity-002
entity-003

# You can group entities with comments
prod-entity-123
prod-entity-456
```

## Arguments

**Shell Script (Batch Processing):**
- `entity-file` - File containing entity IDs (one per line)
- Credentials and endpoint are configured within the script

**Scala/Python Scripts (Single Entity):**
- `username` - Username for basic authentication
- `password` - Password for basic authentication
- `entity-id` - String ID of the entity to retrieve
- `endpoint-url` - Optional custom endpoint URL

## Default Endpoint

The scripts use this default endpoint:
```
http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)
```

## Environment Variables

You can set default values using environment variables:

- `JOLOKIA_USERNAME` - Default username
- `JOLOKIA_PASSWORD` - Default password
- `JOLOKIA_ENDPOINT` - Default endpoint URL

## Examples

### Shell Script - Batch Processing
```bash
# Process entities from file
./jolokia-client.sh entity-ids.txt

# Process entities from custom file
./jolokia-client.sh my-entities.txt

# To modify credentials or endpoint, edit the script:
# ENDPOINT="http://your-host:9999/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)"
# USERNAME="your-username"
# PASSWORD="your-password"
```

### Single Entity Scripts
```bash
# Using Python script
python3 jolokia-client.py myuser mypass entity123

# Using Scala script
scala jolokia-client.scala myuser mypass entity123
```

### Configuration
```bash
# Edit the script file to change configuration:
# vim jolokia-client.sh
#
# Modify these lines:
# ENDPOINT="your-endpoint-url"
# USERNAME="your-username"
# PASSWORD="your-password"
```

## Making Scripts Executable

To make the scripts executable:

```bash
chmod +x jolokia-client.sh
chmod +x jolokia-client.py
chmod +x jolokia-client.scala
```

## Response Format

### Shell Script - Batch Processing Output

**During Processing:**
```
📂 Reading entity IDs from: entity-ids.txt
📋 Found 5 entity IDs to process
🔐 Using username: jmxadmin
📍 Using endpoint: http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)

🚀 Starting batch processing...

[1/5] Processing: entity-001
🔗 Processing entity: entity-001
✅ Success (HTTP 200)

[2/5] Processing: entity-002
🔗 Processing entity: entity-002
❌ Failed (HTTP 404)

[3/5] Processing: entity-003
🔗 Processing entity: entity-003
✅ Success (HTTP 200)
```

**Final Summary:**
```
📊 BATCH PROCESSING SUMMARY
============================
⏱️  Duration: 15s
📈 Total entities: 5
✅ Successful: 3
❌ Failed: 2
📊 Success rate: 60%

✅ Successful entities:
   entity-001
   entity-003
   entity-005

❌ Failed entities:
   entity-002 (HTTP 404)
   entity-004 (HTTP 500)
```

### Single Entity Scripts Output
```
🔗 Calling Jolokia JMX endpoint...
📍 Endpoint: http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)
🆔 Entity ID: entity123
👤 Username: myuser

📡 Making GET request to: http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)/entity123

📊 Response status: 200

✅ Request successful!
📄 Response:
{
  "request": {
    "mbean": "RDActorState:type=Cluster",
    "operation": "get",
    "arguments": ["entity123"],
    "type": "exec"
  },
  "value": {
    // Entity data here
  },
  "timestamp": 1692708123,
  "status": 200
}
```

## Troubleshooting

### Common Issues

1. **Entity File Not Found**
   - Create the entity file in the correct location
   - Check file permissions
   - Verify the file path is correct

2. **Authentication Failed (401)**
   - Check username and password in the script configuration
   - Verify the service is running and accessible
   - Edit the script to update credentials

3. **Connection Timeout**
   - Check network connectivity
   - Verify the endpoint URL is correct
   - Check if the service is running on the specified port

4. **Entity Not Found (404)**
   - Verify the entity IDs exist in the system
   - Check the entity ID format in your file
   - Remove invalid entity IDs from the file

5. **Permission Denied**
   - Ensure the user has permission to access the JMX endpoint
   - Check if the user has the required roles

6. **Batch Processing Issues**
   - The script continues processing even if individual entities fail
   - Check the summary for failed entities
   - Review the output for specific error messages

### Debug Mode

For more verbose output, you can modify the scripts or use curl directly:

```bash
curl -v --user "username:password" \
  --header "Accept: application/json" \
  --header "Content-Type: application/json" \
  "http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)/entity123"
```

## Integration with Project

These scripts can be integrated into:
- **CI/CD pipelines** for health checks and validation
- **Monitoring scripts** for batch entity status checks
- **Development and testing workflows** for bulk data retrieval
- **Operational procedures** for system diagnostics
- **Performance testing** to validate multiple entities

### Batch Processing Benefits

- **Efficiency**: Process hundreds of entities in a single run
- **Reporting**: Comprehensive summary with success/failure rates
- **Resilience**: Continues processing even if individual entities fail
- **Flexibility**: Multiple output modes (verbose, quiet, normal)
- **Automation-friendly**: Exit codes indicate overall success/failure

## Security Notes

- Avoid hardcoding credentials in scripts
- Use environment variables for sensitive data
- Consider using secure credential storage systems
- Be cautious with logging passwords in production environments
- The entity file may contain sensitive entity IDs - protect accordingly
