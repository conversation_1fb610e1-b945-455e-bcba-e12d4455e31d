# Jolokia JMX Client Scripts

This directory contains scripts to call the Jolokia JMX endpoint for retrieving entity data from the PCSA service.

## Available Scripts

### 1. <PERSON> Script (`jolokia-client.sh`)
A bash script that uses `curl` to make HTTP requests.

**Requirements:**
- bash
- curl
- jq (optional, for JSON formatting)

**Usage:**
```bash
./jolokia-client.sh <username> <password> <entity-id> [endpoint-url]
```

### 2. Scala Script (`jolokia-client.scala`)
A Scala script that uses Java's built-in HTTP client.

**Requirements:**
- Scala runtime
- Java 11+

**Usage:**
```bash
scala jolokia-client.scala <username> <password> <entity-id> [endpoint-url]
```

### 3. Python Script (`jolokia-client.py`)
A Python script that uses urllib for HTTP requests.

**Requirements:**
- Python 3.6+

**Usage:**
```bash
python3 jolokia-client.py <username> <password> <entity-id> [endpoint-url]
```

## Arguments

All scripts accept the same arguments:

- `username` - Username for basic authentication
- `password` - Password for basic authentication  
- `entity-id` - String ID of the entity to retrieve
- `endpoint-url` - Optional custom endpoint URL

## Default Endpoint

The scripts use this default endpoint:
```
http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)
```

## Environment Variables

You can set default values using environment variables:

- `JOLOKIA_USERNAME` - Default username
- `JOLOKIA_PASSWORD` - Default password
- `JOLOKIA_ENDPOINT` - Default endpoint URL

## Examples

### Basic Usage
```bash
# Using shell script
./jolokia-client.sh myuser mypass entity123

# Using Python script
python3 jolokia-client.py myuser mypass entity123

# Using Scala script
scala jolokia-client.scala myuser mypass entity123
```

### With Custom Endpoint
```bash
./jolokia-client.sh myuser mypass entity123 http://custom-host:9999/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)
```

### Using Environment Variables
```bash
export JOLOKIA_USERNAME=myuser
export JOLOKIA_PASSWORD=mypass
./jolokia-client.sh entity123
```

## Making Scripts Executable

To make the scripts executable:

```bash
chmod +x jolokia-client.sh
chmod +x jolokia-client.py
chmod +x jolokia-client.scala
```

## Response Format

The scripts will display:
- Request details (endpoint, entity ID, username)
- HTTP response status
- Response body (formatted as JSON if possible)

### Success Response Example
```
🔗 Calling Jolokia JMX endpoint...
📍 Endpoint: http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)
🆔 Entity ID: entity123
👤 Username: myuser

📡 Making GET request to: http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)/entity123

📊 Response status: 200

✅ Request successful!
📄 Response:
{
  "request": {
    "mbean": "RDActorState:type=Cluster",
    "operation": "get",
    "arguments": ["entity123"],
    "type": "exec"
  },
  "value": {
    // Entity data here
  },
  "timestamp": 1692708123,
  "status": 200
}
```

### Error Response Example
```
📊 Response status: 401

❌ Request failed!
📄 Error response:
{
  "error_type": "java.lang.SecurityException",
  "error": "Authentication failed",
  "status": 401
}
```

## Troubleshooting

### Common Issues

1. **Authentication Failed (401)**
   - Check username and password
   - Verify the service is running and accessible

2. **Connection Timeout**
   - Check network connectivity
   - Verify the endpoint URL is correct
   - Check if the service is running on the specified port

3. **Entity Not Found**
   - Verify the entity ID exists
   - Check the entity ID format

4. **Permission Denied**
   - Ensure the user has permission to access the JMX endpoint
   - Check if the user has the required roles

### Debug Mode

For more verbose output, you can modify the scripts or use curl directly:

```bash
curl -v --user "username:password" \
  --header "Accept: application/json" \
  --header "Content-Type: application/json" \
  "http://use1-pcsardngfd01-intss.dev.fndlsb.net:9999/hawtio/jolokia/exec/RDActorState:type=Cluster/get(java.lang.String)/entity123"
```

## Integration with Project

These scripts can be integrated into:
- CI/CD pipelines for health checks
- Monitoring scripts
- Development and testing workflows
- Operational procedures

## Security Notes

- Avoid hardcoding credentials in scripts
- Use environment variables for sensitive data
- Consider using secure credential storage systems
- Be cautious with logging passwords in production environments
