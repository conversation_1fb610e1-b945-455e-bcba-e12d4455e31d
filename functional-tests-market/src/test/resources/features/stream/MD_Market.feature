@MSStreamDeltas @MSFlow @Unordered
#noinspection CucumberTableInspection,NonAsciiCharacters,SpellCheckingInspection
Feature: MD Market

  Scenario: Market is injected in MS Input Stream

    * random Int value is stored with index "marketId"
    * random Int value is stored with index "sportexId"
    * value "TennisTournament" is stored with index "name"

    # Create Market
    * value "CREATE" is stored with index "action"

    Given "Kafka" payload with headers is set to "domain/md/tennis/MD_Output_Market.json"
    When Kafka message with key "marketId" is published to "MD_OUTPUT_DELTA_TENNIS" topic
    Then consume "1" Kafka messages from "MD_OUTPUT_DELTA_TENNIS" topic
    And "Kafka" response matches
      | $.action                          | CREATE      |
      | $.market.id                       | marketId  |
      | $.market.sportexId                | sportexId |
      | $.market.name.instances.ALL.value | name      |