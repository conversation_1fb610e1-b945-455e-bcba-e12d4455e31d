package com.flutter.utils

import com.flutter.baseactor.BaseActor.{ActorCommand, ActorEvent}
import com.flutter.baseactor.behaviour.SubscribeCommands
import com.flutter.retryableactor.metadata.Metadata.KafkaMetadata
import com.flutter.baseactor.domain.state.{ActorState, SequenceNumber}

import scala.util.Random.nextInt

case class Command(data: String, shardingKey: String = "", headers: Map[String, String] = Map.empty, metadata: KafkaMetadata = KafkaMetadata.empty)
    extends ActorCommand with Serializable

case class Event(data: String, identifier: String = nextInt.toString, headers: Map[String, String] = Map.empty, metadata: KafkaMetadata = KafkaMetadata.empty)
    extends ActorEvent with Serializable

case class State(data: String, lastPublishedNo: SequenceNumber, inboundSeqNo: SequenceNumber) extends ActorState with Serializable {
  override def metadata: Map[(String, Int), Long] = Map.empty
}

case class SubscribeCommand(data: String) extends SubscribeCommands {
  override def identifier: String = "1"

  override def headers: Map[String, String] = Map()
}
