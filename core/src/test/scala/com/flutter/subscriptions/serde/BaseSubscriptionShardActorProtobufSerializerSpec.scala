package com.flutter.subscriptions.serde

import com.flutter.subscriptions.actor.generators.{GenSubscriptionShardEventsCommand, GenSubscriptionShardStateCommand}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.scalacheck.ScalaCheckPropertyChecks

class BaseSubscriptionShardActorProtobufSerializerSpec extends AnyFlatSpec with Matchers with ScalaCheckPropertyChecks {

  it should "serialize and deserialize for Subscribed event" in new SubscriptionShardActorProtobufSerializerFixture {
    forAll(genSubscribedEvent) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for Unsubscribed event" in new SubscriptionShardActorProtobufSerializerFixture {
    forAll(genUnsubscribedEvent) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for SubscriptionState" in new SubscriptionShardActorProtobufSerializerFixture {
    forAll(genSubscriptionState) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

}

trait SubscriptionShardActorProtobufSerializerFixture extends GenSubscriptionShardStateCommand with GenSubscriptionShardEventsCommand {
  val serializer = new SubscriptionShardActorProtobufSerializer()
}
