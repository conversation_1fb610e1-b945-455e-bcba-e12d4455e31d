package com.flutter.subscriptions.actor

import org.apache.pekko.Done
import org.apache.pekko.actor.Status.Failure
import org.apache.pekko.actor.{ActorRef, ActorSystem, Props}
import org.apache.pekko.pattern.{AskTimeoutException, StatusReply, ask}
import org.apache.pekko.persistence.testkit.scaladsl.{PersistenceTestKit, SnapshotTestKit}
import org.apache.pekko.persistence.testkit.{PersistenceTestKitPlugin, PersistenceTestKitSnapshotPlugin, SnapshotMeta}
import org.apache.pekko.persistence.{PersistentActor, RecoveryCompleted, SnapshotOffer}
import org.apache.pekko.testkit.TestProbe
import org.apache.pekko.util.Timeout
import com.flutter.common.BaseAkkaSpec
import com.flutter.subscriptions.actor.domain.state.{GenSubscriptionState, Subscription, SubscriptionState}
import com.flutter.subscriptions.actor.notifier.SubscribersPublisher
import com.typesafe.config.{Config, ConfigFactory, ConfigValueFactory}
import org.mockito.ArgumentMatchers.any
import org.mockito.MockitoSugar
import org.scalatest.time.SpanSugar.convertIntToGrainOfTime

import scala.collection.mutable
import scala.concurrent.{Await, ExecutionContextExecutor, Future}
import scala.language.postfixOps

class SubscriptionShardActorBehaviourSpec
    extends BaseAkkaSpec(
      ActorSystem(
        "SubscriptionShardActorBehaviourSystem",
        BaseAkkaSpec.LOCAL_CLUSTER_WITH_PERSISTENCE_DEFAULT
          .withFallback(ConfigFactory.load())
      )
    ) with GenSubscriptionState with MockitoSugar {


  final lazy val snapshotTestKit: SnapshotTestKit = SnapshotTestKit(system)
  final lazy val persistenceTestKit: PersistenceTestKit = PersistenceTestKit(system)
  lazy val config = system.settings.config

  trait SubscriptionShardActorBehaviourBuilder {

    lazy val persistenceId: String = genString.sample.get
    lazy val mockedSubscriptionNotifierPublisher: SubscribersPublisher[String] = mock[SubscribersPublisher[String]]
    lazy val victim: ActorRef = createActor
    lazy val maxSubscriptions = 10
    lazy val actorSnapshotInterval = 100

    val scenarioSubscriptionShardActorConfig = ConfigFactory.empty()
      .withValue("maxSubscriptionsPerShard", ConfigValueFactory.fromAnyRef(maxSubscriptions))
      .withValue("timeout", ConfigValueFactory.fromAnyRef(config.getDuration("subscription.actor.timeout")))
      .withValue("snapshotInterval", ConfigValueFactory.fromAnyRef(actorSnapshotInterval))

    class DummySubscriptionShardActorBehaviour(override val subscriptionShardActorConfig: Config, override protected val subscriptionNotifierPublisher: SubscribersPublisher[String])
      extends SubscriptionShardActorBehaviour[String]() with PersistentActor {

      override def journalPluginId = PersistenceTestKitPlugin.PluginId
      override def snapshotPluginId = PersistenceTestKitSnapshotPlugin.PluginId

      override protected val childActorProps: Props = Props( new DummySubscriptionShardActorBehaviour(subscriptionShardActorConfig, subscriptionNotifierPublisher))

      override def receiveRecover: Receive = subscriptionRecoveryBehaviour.orElse {
        case SnapshotOffer(_, state: SubscriptionState) =>
          this.state = state
        case RecoveryCompleted =>
      }

      override def receiveCommand: Receive = subscriptionReceiveBehaviour

      override def persistenceId: String = self.path.name
    }

    lazy val subscriptionActorProps: Props = Props(new DummySubscriptionShardActorBehaviour(scenarioSubscriptionShardActorConfig, mockedSubscriptionNotifierPublisher))

    when(mockedSubscriptionNotifierPublisher.broadcastPublisher(any(), any(), any())).thenReturn(Future.successful(Done))
    when(mockedSubscriptionNotifierPublisher.notifyPublisher(any(), any(), any())).thenReturn(Future.successful(Done))
    val mockSubscriptionIdentifier = "An Actor identifier"

    def createActor: ActorRef = {
      childActorOf(
        subscriptionActorProps,
        name = persistenceId
      )
    }
  }

  trait SubscriptionShardActorTreeBehaviourBuilder extends SubscriptionShardActorBehaviourBuilder {

    lazy val probe = TestProbe()

    override lazy val maxSubscriptions: Int = 9
    override lazy val actorSnapshotInterval: Int = 11
    lazy val subscriptions: mutable.HashSet[Subscription] = mutable.HashSet(
      Subscription("1"),
      Subscription("2"),
      Subscription("3"),
      Subscription("4"),
      Subscription("5"),
      Subscription("6"),
      Subscription("7"),
      Subscription("8"),
      Subscription("9")
    )
    val rightIdentifier = "10"
    val leftIdentifier = "0"
    val hashCodeDeciderValue: Some[Int] = Some(53)

    val snapshots: Seq[(SnapshotMeta, SubscriptionState)] = Seq(
      (
        SnapshotMeta.create(subscriptions.size),
        SubscriptionState(subscriptions, hashCodeDeciderValue)
      )
    )
    val childActorPersistenceIdRight = s"$persistenceId-R"
    val childActorPersistenceIdLeft = s"$persistenceId-L"
    snapshotTestKit.persistForRecovery(persistenceId, snapshots)

    def assertChildActorCreated(childActorPersistenceId: String) = {
      val childActorPath = s"${testActor.path}/${persistenceId}/${childActorPersistenceId}"
      probe.expectActor(childActorPath)
    }

    def assertChildActorPersistedEvent(childActorPersistenceId: String, subscribeEvent: SubscriptionEvent): SubscriptionEvent = {
      persistenceTestKit.expectNextPersisted(childActorPersistenceId, subscribeEvent)
    }

  }

  it should "persist a new subscription on Subscribe and reply Done without calling notifier on empty data" in new SubscriptionShardActorBehaviourBuilder {
    Given("An new SubscriptionShardActor")

    And("A wild Subscribe appears with None as data")
    victim ! SubscribeAndNotify(mockSubscriptionIdentifier, None, Map.empty)

    Then("Subscription is persisted and actor reply's with Done without calling notifier")
    persistenceTestKit.expectNextPersisted(persistenceId, Subscribed(mockSubscriptionIdentifier))
    expectMsgType[StatusReply[Done]]
    verifyZeroInteractions(mockedSubscriptionNotifierPublisher)
    persistenceTestKit.expectNothingPersisted(persistenceId, 100.millis)
    snapshotTestKit.expectNothingPersisted(persistenceId, 100.millis)
  }

  it should "persist a new subscription on Subscribe command and ignore duplicated ones" in new SubscriptionShardActorBehaviourBuilder {
    Given("An new SubscriptionShardActor")

    And("A wild Subscribe appears")

    victim ! SubscribeAndNotify(mockSubscriptionIdentifier, genOptionString.sample.get, Map.empty)

    Then("Subscription is persisted and actor reply's with Done")
    persistenceTestKit.expectNextPersisted(persistenceId, Subscribed(mockSubscriptionIdentifier))
    expectMsgType[StatusReply[Done]]

    And("A duplicate Subscribe appears")
    victim ! SubscribeAndNotify(mockSubscriptionIdentifier, genOptionString.sample.get, Map.empty)

    Then("New Subscribe is ignored as Subscription is already present, actor will reply Done")
    persistenceTestKit.expectNothingPersisted(persistenceId, 100.millis)
    expectMsgType[StatusReply[Done]]
    expectNoMessage()
  }

  it should "remove a subscription on Unsubscribe command and ignore a unsubscribe command of a not present Subscription" in new SubscriptionShardActorBehaviourBuilder {
    Given("An new SubscriptionShardActor started from a snapshot with a subscription")

    val snapshots: Seq[(SnapshotMeta, SubscriptionState)] = Seq(
      (SnapshotMeta.create(1), SubscriptionState(mutable.HashSet(Subscription(mockSubscriptionIdentifier)), None))
    )
    snapshotTestKit.persistForRecovery(persistenceId, snapshots)

    And("A wild Unsubscribe appears")
    victim ! Unsubscribe(mockSubscriptionIdentifier)

    Then("Unsubscribe event is persisted, actor will reply Done")
    persistenceTestKit.expectNextPersisted(persistenceId, Unsubscribed(mockSubscriptionIdentifier))
    expectMsgType[StatusReply[Done]]

    And("A duplicate Unsubscribe appears")
    victim ! Unsubscribe(mockSubscriptionIdentifier)

    Then("New Unsubscribe event is ignored, actor will reply Done")
    persistenceTestKit.expectNothingPersisted(persistenceId, 100.millis)
    expectMsgType[StatusReply[Done]]
  }

  it should "notify all subscribers on Notify command" in new SubscriptionShardActorBehaviourBuilder {
    Given("An new SubscriptionShardActor started from a snapshot with subscriptions")

    private val subscriptionIdentifier2 = "An Actor identifier 2"
    private val subscriptions: mutable.HashSet[Subscription] = mutable.HashSet(Subscription(mockSubscriptionIdentifier), Subscription(subscriptionIdentifier2))

    val snapshots: Seq[(SnapshotMeta, SubscriptionState)] = Seq(
      (
        SnapshotMeta.create(1),
        SubscriptionState(subscriptions, None)
      )
    )
    snapshotTestKit.persistForRecovery(persistenceId, snapshots)

    And("A wild Notify appears")
    val notifyMessage = "John Cena"
    victim ! NotifyAll(notifyMessage, Map.empty)

    Then("Notify will forward subscribers and message to SubscriptionNotifierPublisher and reply Done if successful")
    persistenceTestKit.expectNothingPersisted(persistenceId, 100.millis)
    verify(mockedSubscriptionNotifierPublisher, times(1)).broadcastPublisher(subscriptions.toSet, notifyMessage, Map.empty)
    verifyNoMoreInteractions(mockedSubscriptionNotifierPublisher)
    expectMsgType[StatusReply[Done]]

    And("A wild Notify appears but SubscriptionNotifierPublisher replies with failure")
    reset(mockedSubscriptionNotifierPublisher)
    when(mockedSubscriptionNotifierPublisher.broadcastPublisher(any(), any(), any())).thenReturn(Future.failed(new Throwable("I failed!")))
    victim ! NotifyAll(notifyMessage, Map.empty)

    Then("Notify will forward subscribers and message to SubscriptionNotifierPublisher and reply Done if successful")
    persistenceTestKit.expectNothingPersisted(persistenceId, 100.millis)
    verify(mockedSubscriptionNotifierPublisher, times(1)).broadcastPublisher(subscriptions.toSet, notifyMessage, Map.empty)
    verifyNoMoreInteractions(mockedSubscriptionNotifierPublisher)
    expectMsgType[Failure]
  }

  it should "recover from journal store" in new SubscriptionShardActorBehaviourBuilder {

    private val subscriptionIdentifier2 = "An Actor identifier 2"
    val notifyMessage = "John Cena"

    Given("An new SubscriptionShardActor started from persisted events")

    val events: Seq[SubscriptionEvent] = Seq(
      Unsubscribed(mockSubscriptionIdentifier),
      Subscribed(mockSubscriptionIdentifier),
      Subscribed(subscriptionIdentifier2)
    )
    persistenceTestKit.persistForRecovery(persistenceId, events)

    And("A wild Notify appears")
    victim ! NotifyAll(notifyMessage, Map.empty)

    Then("Notify will forward recovered subscribers and message to SubscriptionNotifierPublisher and reply Done if successful")
    private val subscriptions: mutable.HashSet[Subscription] = mutable.HashSet(Subscription(mockSubscriptionIdentifier), Subscription(subscriptionIdentifier2))
    persistenceTestKit.expectNothingPersisted(persistenceId, 100.millis)
    verify(mockedSubscriptionNotifierPublisher, times(1)).broadcastPublisher(subscriptions.toSet, notifyMessage, Map.empty)
    verifyNoMoreInteractions(mockedSubscriptionNotifierPublisher)
    expectMsgType[StatusReply[Done]]
  }

  it should "recover from snapshot store" in new SubscriptionShardActorBehaviourBuilder {
    Given("An new SubscriptionShardActor started from a snapshot with subscriptions")

    private val subscriptionIdentifier2 = "An Actor identifier 2"
    private val subscriptions: mutable.HashSet[Subscription] = mutable.HashSet(Subscription(mockSubscriptionIdentifier), Subscription(subscriptionIdentifier2))
    val notifyMessage = "John Cena"

    val snapshots: Seq[(SnapshotMeta, SubscriptionState)] = Seq(
      (
        SnapshotMeta.create(1),
        SubscriptionState(mutable.Set(), None)
      ),
      (
        SnapshotMeta.create(2),
        SubscriptionState(subscriptions, None)
      )
    )
    snapshotTestKit.persistForRecovery(persistenceId, snapshots)

    And("A wild Notify appears")
    victim ! NotifyAll(notifyMessage, Map.empty)

    Then("Notify will forward recovered subscribers and message to SubscriptionNotifierPublisher and reply Done if successful")
    persistenceTestKit.expectNothingPersisted(persistenceId, 100.millis)
    verify(mockedSubscriptionNotifierPublisher, times(1)).broadcastPublisher(subscriptions.toSet, notifyMessage, Map.empty)
    verifyNoMoreInteractions(mockedSubscriptionNotifierPublisher)
    expectMsgType[StatusReply[Done]]
  }

  it should "define hashCodeDeciderValue when reaching maxSubscriptions" in new SubscriptionShardActorBehaviourBuilder {
    override lazy val actorSnapshotInterval: Int = maxSubscriptions
    private val expectedHashCodeDeciderValue = 91612803

    Given("An new SubscriptionShardActor started from a snapshot with subscriptions")
    private val subscriptions: mutable.HashSet[Subscription] = mutable.HashSet(
      Subscription("1"),
      Subscription("2"),
      Subscription("3"),
      Subscription("4"),
      Subscription("5"),
      Subscription("6"),
      Subscription("7"),
      Subscription("8"),
      Subscription("9")
    )

    val snapshots: Seq[(SnapshotMeta, SubscriptionState)] = Seq(
      (
        SnapshotMeta.create(subscriptions.size),
        SubscriptionState(subscriptions, None)
      )
    )
    snapshotTestKit.persistForRecovery(persistenceId, snapshots)
    snapshotTestKit.expectNextPersisted(persistenceId, SubscriptionState(subscriptions, None))

    And("A wild Subscribe appears")
    victim ! SubscribeAndNotify(mockSubscriptionIdentifier, genOptionString.sample.get, Map.empty)
    expectMsgType[StatusReply[Done]]

    Then("A snapshot was triggered with hashCodeDeciderValue defined")
    subscriptions.add(Subscription(mockSubscriptionIdentifier))
    persistenceTestKit.expectNextPersisted(persistenceId, Subscribed(mockSubscriptionIdentifier))
    snapshotTestKit.expectNextPersisted(persistenceId, SubscriptionState(subscriptions, Some(expectedHashCodeDeciderValue)))
  }

  it should "create 2 new SubscriptionShardActor in a tree pattern after maxSubscriptions was reached" in new SubscriptionShardActorTreeBehaviourBuilder {

    Given("An new SubscriptionShardActor started from a snapshot with subscriptions")

    And("A wild Subscribe that should go right appears")
    victim ! SubscribeAndNotify(rightIdentifier, genOptionString.sample.get, Map.empty)
    expectMsgType[StatusReply[Done]]

    Then("A SubscriptionShardActor should be create under the parent on right side of tree")

    assertChildActorCreated(childActorPersistenceIdRight)
    assertChildActorPersistedEvent(childActorPersistenceIdRight, Subscribed(rightIdentifier))
    persistenceTestKit.expectNothingPersisted(childActorPersistenceIdRight, 100.millis)

    And("A wild Subscribe that should go left appears")
    victim ! SubscribeAndNotify(leftIdentifier, genOptionString.sample.get, Map.empty)
    expectMsgType[StatusReply[Done]]

    Then("A SubscriptionShardActor should be create under the parent on left side of tree")

    assertChildActorCreated(childActorPersistenceIdLeft)
    assertChildActorPersistedEvent(childActorPersistenceIdLeft, Subscribed(leftIdentifier))

    And("A wild Unsubscribe that should go right appears")
    victim ! Unsubscribe(rightIdentifier)
    assertChildActorPersistedEvent(childActorPersistenceIdRight, Unsubscribed(rightIdentifier))
    expectMsgType[StatusReply[Done]]
    persistenceTestKit.expectNothingPersisted(childActorPersistenceIdLeft, 100.millis)

  }

  it should "start with a tree of persisted actors and be able to Notify children actors" in new SubscriptionShardActorTreeBehaviourBuilder {
    Given("A tree of SubscriptionShardActor actors from persistence ")

    val eventsLeftActor: Seq[SubscriptionEvent] = Seq(
      Subscribed(leftIdentifier)
    )
    val eventsRightActor: Seq[SubscriptionEvent] = Seq()

    persistenceTestKit.persistForRecovery(childActorPersistenceIdLeft, eventsLeftActor)
    persistenceTestKit.persistForRecovery(childActorPersistenceIdRight, eventsRightActor)

    reset(mockedSubscriptionNotifierPublisher)
    implicit val executionContext: ExecutionContextExecutor = system.dispatcher
    val delayedMsNotifierResolution = 200

    val delayedFutureResolution: Future[Done] = Future {
      Thread.sleep(delayedMsNotifierResolution + 50)
      Done
    }
    when(mockedSubscriptionNotifierPublisher.broadcastPublisher(any(), any(), any())).thenReturn(delayedFutureResolution)

    And("A wild Notify appears")
    val notifyMessage = "hello"
    victim ! NotifyAll(notifyMessage, Map.empty)

    Then("No reply message should be expected before notifier futures resolve")
    expectNoMessage(delayedMsNotifierResolution milliseconds)
    assertChildActorCreated(childActorPersistenceIdLeft)
    assertChildActorCreated(childActorPersistenceIdRight)

    verify(mockedSubscriptionNotifierPublisher, atMost(3)).broadcastPublisher(subscriptions.toSet, notifyMessage, Map.empty)
    verify(mockedSubscriptionNotifierPublisher, atMost(3)).broadcastPublisher(Set(Subscription(leftIdentifier)), notifyMessage, Map.empty)
    verify(mockedSubscriptionNotifierPublisher, atMost(3)).broadcastPublisher(Set(), notifyMessage, Map.empty)
    expectMsgType[StatusReply[Done]]

  }

  behavior.of("notifyBehaviour")

  trait SubscriptionShardActorTreeTimeoutBehaviourBuilder extends SubscriptionShardActorTreeBehaviourBuilder {
    implicit val executionContext: ExecutionContextExecutor = system.dispatcher
    implicit lazy val testAskVictimTimeout: Timeout = Timeout(testAwaitTimeout - 10.milliseconds)
    lazy val testAwaitTimeout = 200.milliseconds
    lazy val nextActorTotalTimeout = 200.milliseconds
    lazy val delayReceiveCommands = mutable.Queue(1, 2, 3)

    override lazy val subscriptionActorProps: Props = Props(new DummySubscriptionShardActorTimeoutBehaviour)
    override val scenarioSubscriptionShardActorConfig: Config = ConfigFactory.empty()
      .withValue("maxSubscriptionsPerShard", ConfigValueFactory.fromAnyRef(maxSubscriptions))
      .withValue("timeout", ConfigValueFactory.fromAnyRef(s"${nextActorTotalTimeout.toMillis} millis"))
      .withValue("snapshotInterval", ConfigValueFactory.fromAnyRef(actorSnapshotInterval))

    val eventsLeftActor: Seq[SubscriptionEvent] = Seq(
      Subscribed(leftIdentifier)
    )
    val eventsRightActor: Seq[SubscriptionEvent] = Seq(
      Subscribed(rightIdentifier)
    )

    persistenceTestKit.persistForRecovery(childActorPersistenceIdLeft, eventsLeftActor)
    persistenceTestKit.persistForRecovery(childActorPersistenceIdRight, eventsRightActor)
    val notifyMessage = "hello"

    class DummySubscriptionShardActorTimeoutBehaviour extends DummySubscriptionShardActorBehaviour(subscriptionShardActorConfig = scenarioSubscriptionShardActorConfig, subscriptionNotifierPublisher = mockedSubscriptionNotifierPublisher) with PersistentActor {

      override protected val childActorProps: Props = Props(new DummySubscriptionShardActorTimeoutBehaviour())

      override def receiveCommand: Receive = {
        case cmd: Any =>
          val delay = delayReceiveCommands.dequeue()
          Thread.sleep(delay)
          super.receiveCommand(cmd)
      }
    }
  }

  it should "notify all child actors within the whole timeout for operation timeframe with success" in new SubscriptionShardActorTreeTimeoutBehaviourBuilder {
    Given("An new SubscriptionShardActor started from a snapshot with subscriptions")

    And("A wild Notify appears")

    Then("All child actors should be notified within the timeout timeframe")
    val result = Await.result(victim ? NotifyAll(notifyMessage, Map.empty), testAwaitTimeout)
    result shouldBe a[StatusReply[Done]]
    expectNoMessage(100.milliseconds)
    assertChildActorCreated(childActorPersistenceIdLeft)
    assertChildActorCreated(childActorPersistenceIdRight)
    verify(mockedSubscriptionNotifierPublisher, times(1)).broadcastPublisher(subscriptions.toSet, notifyMessage, Map.empty)
    verify(mockedSubscriptionNotifierPublisher, times(1)).broadcastPublisher(Set(Subscription(leftIdentifier)), notifyMessage, Map.empty)
    verify(mockedSubscriptionNotifierPublisher, times(1)).broadcastPublisher(Set(Subscription(rightIdentifier)), notifyMessage, Map.empty)
    verifyNoMoreInteractions(mockedSubscriptionNotifierPublisher)
  }

  it should "propagate AskTimeoutException when it occurs" in new SubscriptionShardActorTreeTimeoutBehaviourBuilder {
    override lazy val testAwaitTimeout = 300.milliseconds
    override lazy val nextActorTotalTimeout = 100.milliseconds
    override lazy val delayReceiveCommands = mutable.Queue(30, 70, 70)

    Given("An new SubscriptionShardActor started from a snapshot with subscriptions")

    And("A wild Notify appears")

    And("The total timeout is exceeded on the next actor remaining timeout calculation")

    Then("An AskTimeoutException should be thrown when the remaining timeout is exceeded")
    intercept[AskTimeoutException] {
      Await.result(victim ? NotifyAll(notifyMessage, Map.empty), testAwaitTimeout)
    }
    expectNoMessage(100.milliseconds)
    assertChildActorCreated(childActorPersistenceIdLeft)
    verify(mockedSubscriptionNotifierPublisher, atMost(2)).broadcastPublisher(subscriptions.toSet, notifyMessage, Map.empty)
  }

  it should "throw AskTimeoutException when the whole timeout for operation is exceeded" in new SubscriptionShardActorTreeTimeoutBehaviourBuilder {
    override lazy val testAwaitTimeout = 300.milliseconds
    override lazy val nextActorTotalTimeout = 100.milliseconds
    override lazy val delayReceiveCommands = mutable.Queue(110, 180, 180)

    Given("An new SubscriptionShardActor started from a snapshot with subscriptions")

    And("A wild Notify appears")

    And("The total timeout is exceeded on root actor on remaining timeout calculation")

    Then("An AskTimeoutException should be thrown when the remaining timeout is exceeded")

    val exception = intercept[AskTimeoutException] {
      Await.result(victim ? NotifyAll(notifyMessage, Map.empty), testAwaitTimeout)
    }
    exception.getMessage shouldBe "The whole timeout of notifyBehaviour exceeded, the processing is going to be aborted"
    expectNoMessage(100.milliseconds)
    verifyZeroInteractions(mockedSubscriptionNotifierPublisher)
  }

}
