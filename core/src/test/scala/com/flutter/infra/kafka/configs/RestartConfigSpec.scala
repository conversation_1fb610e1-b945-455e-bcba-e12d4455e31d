package com.flutter.infra.kafka.configs

import org.apache.pekko.stream.RestartSettings
import com.typesafe.config.{Config, ConfigFactory}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.concurrent.duration.durationToPair
import scala.jdk.DurationConverters.JavaDurationOps

class RestartConfigSpec extends RestartConfigFixture with AnyFlatSpecLike with Matchers {
  "RestartConfig#asRestartSettings" should "return a fully configured RestartSettings and withMaxRestarts" in {

    val victim = RestartConfig.asRestartSettings(restartConfigs)
    victim shouldBe a[RestartSettings]
    durationToPair(victim.minBackoff) shouldBe durationToPair(restartConfigs.getDuration("min-backoff").toScala)
    durationToPair(victim.maxBackoff) shouldBe durationToPair(restartConfigs.getDuration("max-backoff").toScala)
    durationToPair(victim.maxRestartsWithin) shouldBe durationToPair(restartConfigs.getConfig("max-restarts").getDuration("within").toScala)
    victim.maxRestarts shouldBe restartConfigs.getConfig("max-restarts").getInt("count")
  }

}
trait RestartConfigFixture  {
  val restartConfigs: Config = ConfigFactory.parseString(
    """
      |  {
      |    min-backoff = 100 milliseconds
      |    max-backoff = 200 milliseconds
      |    random-factor = 0.2
      |    max-restarts {
      |      count = 2
      |      within = 1 second
      |    }
      |  }
      |""".stripMargin).resolve()
}
