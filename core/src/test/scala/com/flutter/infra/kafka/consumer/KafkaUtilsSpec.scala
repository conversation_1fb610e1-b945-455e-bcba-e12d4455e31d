package com.flutter.infra.kafka.consumer

import org.apache.kafka.common.header.internals.{RecordHeader, RecordHeaders}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

class KafkaUtilsSpec extends AnyFlatSpecLike with Matchers {

  it should "extractHeaders from a valid list" in {

    val headers = List(
      new RecordHeader("key1", "value1".getBytes),
      new RecordHeader("key2", "value2".getBytes)
    )

    val recordHeaders = new RecordHeaders()
    headers.foreach(recordHeaders.add)

    val result = KafkaUtils.extractHeaders(recordHeaders)

    result.get("key1") shouldBe Some("value1")
    result.get("key2") shouldBe Some("value2")
  }

  it should "give an empty map when extractHeaders from an empty list" in {
    val recordHeaders = new RecordHeaders()

    val result = KafkaUtils.extractHeaders(recordHeaders)

    result.size shouldBe 0
  }
}
