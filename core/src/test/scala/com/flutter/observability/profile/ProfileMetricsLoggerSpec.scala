package com.flutter.observability.profile

import com.flutter.observability.profile.ProfileMetricsLogger.ProfileMetricsLoggerOps
import com.flutter.pcsa.common.GenCommon
import org.mockito.ArgumentMatchersSugar.any
import org.mockito.MockitoSugar.{mock, reset, verify, verifyNoMoreInteractions, when}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.slf4j.spi.LoggingEventBuilder

class ProfileMetricsLoggerSpec extends AnyFlatSpecLike with Matchers with TableDrivenPropertyChecks {
  trait InheritanceProfileMetricsLoggerFixture extends GenCommon {
    val loggingEventBuilderMock: LoggingEventBuilder = mock[LoggingEventBuilder]
    when(loggingEventBuilderMock.addKeyValue(any[String], any[String])).thenReturn(loggingEventBuilderMock)
    val headers: Map[String, AnyRef] = genMap[String, AnyRef](genString, genString).sample.get

    val victim = new ProfileMetricsLoggerOps(loggingEventBuilderMock)
  }

  it should "add profiling metrics Key Values to LoggingEventBuilder" in new InheritanceProfileMetricsLoggerFixture {

    private val result = victim.addProfilingMetricsKeyValues(headers)

    verify(loggingEventBuilderMock).addKeyValue("messageInheritanceProfile", headers.getOrElse(MESSAGE_TECHNICAL_PROFILE, ""))
    verify(loggingEventBuilderMock).addKeyValue("messageTechnicalProfile", headers.getOrElse(MESSAGE_TECHNICAL_PROFILE, ""))
    verify(loggingEventBuilderMock).addKeyValue("messageHierarchyLevelProfile", headers.getOrElse(MESSAGE_HIERARCHY_LEVEL_PROFILE, ""))
    verify(loggingEventBuilderMock).addKeyValue("messageTypeActionProfile", headers.getOrElse(MESSAGE_TYPE_ACTION_PROFILE, ""))
    verifyNoMoreInteractions(loggingEventBuilderMock)

    result shouldBe loggingEventBuilderMock

    reset(loggingEventBuilderMock)
  }

}
