package com.flutter.jmx

import com.flutter.jmx.JmxActorResolver.ActorResolver
import com.flutter.pcsa.common.datatypes.gbpid.{GbpId, GbpIdHierarchyLevel, GenGbpId, SizeNotMeetCriteria}
import org.mockito.ArgumentMatchers.any
import org.mockito.MockitoSugar
import org.scalatest.GivenWhenThen
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

class JmxActorResolverSpec extends AnyFlatSpecLike with GenGbpId with GivenWhenThen with MockitoSugar with Matchers {

 trait JmxActorResolverTrait {
   val victim = new JmxActorResolver {
     override def getResolver: ActorResolver = ???
   }
 }

  "JmxActorResolver.getEntity" should "should return empty string for empty urn" in new JmxActorResolverTrait {
    val urn= ""
    val expected = ""
    val result = victim.getEntity(urn)
    result shouldBe expected
  }

  it should "should return valid hierarchy string for a valid urn" in new JmxActorResolverTrait {
    val urn= "urn:sbk:pc:e:gpd:12345"
    val expected = "e"
    val result = victim.getEntity(urn)
    result shouldBe expected
  }

  it should "should return error for a invalid urn" in new JmxActorResolverTrait {
    val urn= "urn:sbk:pc:invalid:gpd:12345"
       victim.getEntity(urn) shouldBe ""
  }
}
