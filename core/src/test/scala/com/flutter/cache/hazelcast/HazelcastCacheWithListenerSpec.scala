package com.flutter.cache.hazelcast

import org.apache.pekko.actor.ActorSystem
import com.flutter.cache.hazelcast.localcachemanager.CacheManager
import com.flutter.pcsa.common.GenCommon
import com.hazelcast.config.Config
import com.hazelcast.core.HazelcastInstance
import com.hazelcast.replicatedmap.ReplicatedMap
import com.hazelcast.test.TestHazelcastInstanceFactory
import org.mockito.MockitoSugar
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterEach, GivenWhenThen}

import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{Await, Promise}

class HazelcastCacheWithListenerSpec extends AnyFlatSpecLike with Matchers with GivenWhenThen with MockitoSugar with BeforeAndAfterEach with GenCommon {

  var testInstance: HazelcastInstance = _

  override def beforeEach() = {
    val config = new Config()
    config.setProperty("hazelcast.logging.type", "log4j2")
    testInstance = new TestHazelcastInstanceFactory().newHazelcastInstance(config)
  }

  override def afterEach() = {
    testInstance.shutdown()
  }

  private def expectCacheManagerCall(operationToVerify: CacheManager[String] => Unit, timeout: FiniteDuration = 200.millis)(block: => Unit)(implicit cacheManagerMock: CacheManager[String]) = {
    val promise = Promise[Unit]()
    operationToVerify(doAnswer(() => {
      promise.success(())
    }).when(cacheManagerMock))

    block

    Await.result(promise.future, timeout)

    operationToVerify(verify(cacheManagerMock))
  }

  trait HazelcastCacheWithListenerBuilder {
    lazy val victim = new CustomHazelcastCacheWithListener()
    lazy val replicatedTestMapName = genString.sample.get
    val replicatedTestMap: ReplicatedMap[String, String] = testInstance.getReplicatedMap(replicatedTestMapName)
    implicit val cacheManagerMock: CacheManager[String] = mock[CacheManager[String]]
    val mockedHazelcastInstance = mock[HazelcastInstance]

    class CustomHazelcastCacheWithListener extends HazelcastCacheWithListener[String] {
      protected val cacheName: String = "mockCacheName"
      protected val system: ActorSystem = mock[ActorSystem]

      protected override def getMap(): ReplicatedMap[String, String] = replicatedTestMap

      this.changeCurrentCacheManager(cacheManagerMock)
      override protected val cacheInstance: HazelcastInstance = mockedHazelcastInstance
    }
  }

  behavior.of("add an entry to Hazelcast")

  it should "add entry to cache when an entry is added on hazelcast" in new HazelcastCacheWithListenerBuilder {
    Given("an empty cache")

    When("an entry is added to the cache")
    expectCacheManagerCall(_.add("key1"))({
      victim.put("key1", "value1")
    })
    Then("the entry should be added to cacheManager")
    verifyNoMoreInteractions(cacheManagerMock)
  }

  behavior.of("get an entry from Hazelcast")

  it should "get entry from hazelcast and touch key on cacheManager when present" in new HazelcastCacheWithListenerBuilder {
    Given("a cache with an entry")
    expectCacheManagerCall(_.add("key2")) {
      victim.put("key2", "value2")
    }

    When("a get action happens")

    Then("the hazelcast should return the Key and invoke touch on cacheManager")

    victim.get("key2").contains("value2") shouldBe true
    verify(cacheManagerMock).touch("key2")
    verifyNoMoreInteractions(cacheManagerMock)
  }


  it should "return None when no key on hazelcast and not interact with cacheManager" in new HazelcastCacheWithListenerBuilder {
    Given("an empty cache")

    When("a get action happens")

    Then("the hazelcast should return None as it doesn't exist and not interact with cacheManager")
    victim.get("nonexistentKey") shouldBe None
    verifyZeroInteractions(cacheManagerMock)
  }


  behavior.of("remove key from hazelcast")

  it should "remove entry from hazelcast and cache when present" in new HazelcastCacheWithListenerBuilder {
    Given("a cache with an entry")
    expectCacheManagerCall(_.add("key1")) {
      victim.put("key1", "value1")
    }

    When("the entry is removed from the hazelcast")

    Then("the entry is removed from cache")
    expectCacheManagerCall(_.remove("key1")) {
      victim.remove("key1")
    }
    victim.containsKey("key1") shouldBe false
  }

  behavior.of("update key on hazelcast")
  it should "updateKey on cache" in new HazelcastCacheWithListenerBuilder {
    Given("a hazelcast with an entry")
    expectCacheManagerCall(_.add("key1")) {
      victim.put("key1", "value1")
    }

    When("the hazelcast key1 is updated")


    Then("cacheManager should touch key")
    expectCacheManagerCall(_.update("key1")) {
      victim.put("key1", "value2")
    }
  }

  behavior.of("containsKey on hazelcast")

  it should "return true if key is in hazelcast" in new HazelcastCacheWithListenerBuilder {
    Given("a hazelcast with an entry")
    expectCacheManagerCall(_.add("key1")) {
      victim.put("key1", "value1")
    }

    When("checking if the key is in the cacheManager")

    Then("the hazelcast should return true")
    victim.containsKey("key1") shouldBe true
  }

  it should "return false if key is not in cacheManager" in new HazelcastCacheWithListenerBuilder {
    Given("an empty hazelcast")

    When("checking if a non-existent key is in the cacheManager")

    Then("the hazelcast should return false")
    victim.containsKey("nonexistentKey") shouldBe false
  }

  behavior.of("hazelcast map clear")
  it should "trigger clear of cacheManager" in new HazelcastCacheWithListenerBuilder {
    Given("a hazelcast with an entry")
    expectCacheManagerCall(_.add("key1")) {
      victim.put("key1", "value1")
    }

    When("the hazelcast is cleared")

    Then("cacheManager should also be cleared")
    expectCacheManagerCall(_.clear()) {
      replicatedTestMap.clear()
    }

  }

}
