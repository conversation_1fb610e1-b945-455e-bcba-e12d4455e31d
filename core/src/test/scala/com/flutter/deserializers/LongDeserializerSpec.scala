package com.flutter.deserializers

import com.flutter.infra.kafka.consumer.LongDeserializer
import org.scalatest.BeforeAndAfterEach
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import java.nio.charset.StandardCharsets

class LongDeserializerSpec extends AnyFlatSpec with Matchers with BeforeAndAfterEach {
  val victim = new LongDeserializer

  it should "deserialize key from String to Long" in {
    val longValue = String.valueOf(2L)

    val result = victim.deserialize("randomTopic", longValue.getBytes())
    String.valueOf(result) shouldBe longValue
  }

  it should "return an exception when String doesn't fit as Long" in {
    val someValue = "hi how are you?".getBytes()
    assertThrows[NumberFormatException](victim.deserialize("randomTopic", someValue))
  }
}
