package com.flutter.baseactor.notifier.creator

import com.flutter.baseactor.notifier.creator.StateDeltaSyntax.StateDelta
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper
import org.scalatest.prop.TableDrivenPropertyChecks

class StateDeltaSyntaxSpec extends AnyFlatSpecLike with TableDrivenPropertyChecks {

  it should "return None when no changes are detected" in {
    implicit def conversion(i: Int): String = String.valueOf(i)

    val victim = new StateDelta[Int](Some(1), 1)
    val result = victim.?(i => i + 1)(conversion)
    result shouldBe None
  }


  it should "return Some with result converted when previous and old are different" in {
    val table = Table(
      ("previous", "current"),
      (Some(1), 2),
      (None, 2)
    )

    forAll(table)((previous, current) => {
      implicit def conversion(i: Int): String = String.valueOf(i)
      val victim = new StateDelta[Int](previous, current)
      val result = victim.?(i => i + 1)(conversion)
      result shouldBe Some(String.valueOf(current+1))
    })

  }

}
