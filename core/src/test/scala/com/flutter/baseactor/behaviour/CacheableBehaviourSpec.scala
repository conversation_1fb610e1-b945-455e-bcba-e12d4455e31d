package com.flutter.baseactor.behaviour

import com.flutter.baseactor.BaseActor.ActorCommand
import com.flutter.baseactor.behaviour.CacheableBehaviour.CacheUpdate
import com.flutter.baseactor.domain.state.ActorState
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.testkit.TestKit
import org.mockito.ArgumentMatchers.any
import org.mockito.MockitoSugar.{mock, spy, times, verify}
import org.scalatest.flatspec.AnyFlatSpecLike

class CacheableBehaviourSpec extends TestKit(ActorSystem("TestSystem")) with AnyFlatSpecLike {

  trait CacheableBehaviourFixture {
    lazy val cacheWriterProcessorMock = mock[CacheWriterProcessor[ActorState, String]]
    lazy val stateMock = mock[ActorState]
    lazy val commandMock = mock[ActorCommand]
    lazy val cacheUpdateMock = mock[CacheUpdate]
    val cacheCommandHandlerMock: Function1[ActorState, Unit] = mock[Function1[ActorState, Unit]]
    val victimSpy = spy(victim)

    object victim extends CacheableBehaviour[ActorState, ActorCommand, String] {
      override protected val cacheProcessor: CacheWriterProcessor[ActorState, String] = cacheWriterProcessorMock
      override protected def postCacheCommandHandler(payload: Option[String]): Unit = productBehaviour(stateMock)
      override protected def cacheCommandHandler(state: ActorState) = cacheCommandHandlerMock(state)
    }
  }
  "CacheableBehaviour#productBehaviour" should "handle or delegate the command" in new CacheableBehaviourFixture {
    victim.productBehaviour(stateMock)(cacheUpdateMock)
    verify(cacheCommandHandlerMock, times(1)).apply(any)
  }
  "CacheableBehaviour#processCacheBehaviour" should "handle the command to the right handler" in new CacheableBehaviourFixture {
    victim.processCacheBehaviour(stateMock)(cacheUpdateMock)
    verify(cacheCommandHandlerMock, times(1)).apply(any)
  }
  "CacheableBehaviour#cacheCommandHandler" should "handle the CacheUpdate command" in new CacheableBehaviourFixture {
    victim.processCacheBehaviour(stateMock)(cacheUpdateMock)
    verify(cacheCommandHandlerMock, times(1)).apply(stateMock)
  }
}
