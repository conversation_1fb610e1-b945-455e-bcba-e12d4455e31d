package com.flutter.baseactor.behaviour

import org.apache.pekko.actor.{ActorRef, ActorSystem, Props}
import org.apache.pekko.persistence._
import org.apache.pekko.persistence.testkit.SnapshotMeta
import org.apache.pekko.persistence.testkit.scaladsl.{PersistenceTestKit, SnapshotTestKit}
import com.flutter.sharedplatforms.LazyLogging
import com.flutter.common.BaseAkkaSpec
import com.flutter.pcsa.common.GenCommon
import com.typesafe.config.ConfigFactory
import org.scalatest.BeforeAndAfterEach
import org.scalatest.prop.TableFor3

import java.util.Date
import scala.language.postfixOps
import scala.reflect.ClassTag.Any

class PersistenceBaseActorBehaviourSpec
    extends BaseAkkaSpec(
      ActorSystem(
        "PersistenceBaseActorBehaviourSystem",
        BaseAkkaSpec.LOCAL_CLUSTER_WITH_PERSISTENCE_DEFAULT
          .withFallback(ConfigFactory.load())
      )
    ) with GenCommon with BeforeAndAfterEach {
  final lazy val persistenceTestKit = PersistenceTestKit(system)

  override def beforeEach(): Unit = {
    persistenceTestKit.clearAll()
    persistenceTestKit.resetPolicy()
  }
  trait CustomBaseActor extends PersistentActor with PersistenceBaseActorBehaviour with LazyLogging

  trait PersistenceBaseActorBuilder {
    protected def getActorName(id: Int): String = s"persistenceBaseActor_${genString.sample.get}_$id"

    final lazy val snapshotTestKit: SnapshotTestKit = SnapshotTestKit(system)
    lazy val randomActorId: Integer = genInt.sample.get
    lazy val persistenceActorId: String = getActorName(randomActorId)
    lazy val victim: ActorRef = createActor
    lazy val originalSeqNr: Int = genInt.sample.get

    lazy val persistenceBaseActorProps: Props = Props(new CustomBaseActor {
      implicit override val persistenceId: String = persistenceActorId

      private def customBehaviour: Receive = {
        case SetDeleteInProgress(flag) =>
          isDeletionInProgress = flag
          sender() ! Accepted(System.currentTimeMillis())
        case CallDeleteStateMessageJournal(toSeqNumber: Long) =>
          deleteStateMessageJournal(toSeqNumber)
          sender() ! Accepted(System.currentTimeMillis())
        case DeleteInProgress() => sender() ! isDeletionInProgress
      }

      override def receiveRecover: Receive = {
        case Any => ()
      }

      override def receiveCommand: Receive = customBehaviour.orElse(super.handledPersistenceMessages)

    })

    def createActor: ActorRef = {
      system.actorOf(
        persistenceBaseActorProps,
        name = getActorName(randomActorId)
      )
    }

    protected[this] def checkDeleteInProgress(actorRef: ActorRef, flag: Boolean): Unit = {
      actorRef ! DeleteInProgress()
      expectMsgType[Boolean] shouldBe flag
    }

    case class SetDeleteInProgress(flag: Boolean)
    case class CallDeleteStateMessageJournal(toSeqNumber: Long)
    case class DeleteInProgress()
    case class Accepted(timeStamp: Long)

  }

  it should "deleteMessages from journal to a provided SeqNumber when deleteStateMessageJournal is called" in new PersistenceBaseActorBuilder {

    val persistedJournal: Seq[String] = Seq(
      genString.sample.get,
      genString.sample.get,
      genString.sample.get,
      genString.sample.get,
      genString.sample.get
    )
    persistenceTestKit.persistForRecovery(persistenceActorId, persistedJournal)

    Given("An PersistenceBaseActorBehaviour is created from a snapshot")

    And("deleteStateMessageJournal is triggered")

    victim ! CallDeleteStateMessageJournal(persistedJournal.size)
    expectMsgType[Accepted]

    persistenceTestKit.expectNothingPersisted(persistenceActorId)
    persistenceTestKit.persistedInStorage(persistenceActorId).size shouldBe 0
  }

  it should "not deleteMessages from journal when deleteStateMessageJournal is called but isDeletionInProgress flag is true" in new PersistenceBaseActorBuilder {

    val persistedJournal: Seq[String] = Seq(
      genString.sample.get,
      genString.sample.get,
      genString.sample.get,
      genString.sample.get,
      genString.sample.get
    )
    persistenceTestKit.persistForRecovery(persistenceActorId, persistedJournal)

    victim ! SetDeleteInProgress(true)
    expectMsgType[Accepted]

    checkDeleteInProgress(victim, flag = true)

    Given("An PersistenceBaseActorBehaviour is created from a snapshot")

    And("deleteStateMessageJournal is triggered")

    victim ! CallDeleteStateMessageJournal(5)
    expectMsgType[Accepted]

    checkDeleteInProgress(victim, flag = true)
    persistenceTestKit.persistedInStorage(persistenceActorId).size shouldBe 5
  }

  it should "with signal SaveSnapshotSuccess delete all snapshots related with actor" in new PersistenceBaseActorBuilder {

    val snapshots: Seq[(SnapshotMeta, String)] = Seq(
      (SnapshotMeta.create(originalSeqNr), genString.sample.get),
      (SnapshotMeta.create(originalSeqNr + 1), genString.sample.get)
    )
    snapshotTestKit.persistForRecovery(persistenceActorId, snapshots)

    Given("An PersistenceBaseActorBehaviour is created from a snapshot")

    And("A new snapshot will be triggered containing the latest state when SaveSnapshotSuccess is received")

    val snapshotMetadata: SnapshotMetadata = SnapshotMetadata(
      persistenceId = persistenceActorId,
      sequenceNr = originalSeqNr + 1
    )
    victim ! SaveSnapshotSuccess(snapshotMetadata)

    snapshotTestKit.expectNothingPersisted(persistenceActorId)
  }

  it should "remain in isDeletionInProgress flag if receive signal SaveSnapshotFailure" in {
    val deletionInProgressCases: Seq[Boolean] = List(true, false)

    deletionInProgressCases.foreach { isDeletionInProgress =>
      {
        new PersistenceBaseActorBuilder {

          Given("An PersistenceBaseActorBehaviour")

          // Update the deleteInProgress flag with the value we want to test
          victim ! SetDeleteInProgress(isDeletionInProgress)
          expectMsgType[Accepted]

          checkDeleteInProgress(victim, flag = isDeletionInProgress)

          And(s"Validate isDeletionInProgress flag vs expected result: $isDeletionInProgress")
          val error = new Throwable("It wasn't possible to save the snapshot")

          victim ! SaveSnapshotFailure(
            SnapshotMetadata(
              persistenceId = persistenceActorId,
              sequenceNr = genLong.sample.get,
              timestamp = new Date().getTime
            ),
            error
          )

          // Revalidate the actor flag
          checkDeleteInProgress(victim, flag = isDeletionInProgress)
        }
      }
    }
  }

  it should "set isDeletionInProgress to false if receive one of these signals DeleteSnapshotsSuccess, DeleteMessagesSuccess, DeleteMessagesFailure, DeleteSnapshotFailure" in new PersistenceBaseActorBuilder {

    Given("An actor")
    val throwable: Throwable = new Throwable(genString.sample.get)

    val cases: TableFor3[_, Boolean, Boolean] = Table(
      ("message", "isDeletionInProgress", "result"),
      (DeleteSnapshotsSuccess(SnapshotSelectionCriteria(maxSequenceNr = genLong.sample.get)), true, false),
      (DeleteSnapshotsSuccess(SnapshotSelectionCriteria(maxSequenceNr = genLong.sample.get)), false, false),
      (DeleteMessagesSuccess(genLong.sample.get), true, false),
      (DeleteMessagesSuccess(genLong.sample.get), false, false),
      (DeleteMessagesFailure(throwable, genLong.sample.get), true, false),
      (DeleteMessagesFailure(throwable, genLong.sample.get), false, false),
      (DeleteSnapshotFailure(SnapshotMetadata(persistenceActorId, genLong.sample.get, timestamp = new Date().getTime), throwable), true, false),
      (DeleteSnapshotFailure(SnapshotMetadata(persistenceActorId, genLong.sample.get, timestamp = new Date().getTime), throwable), false, false)
    )

    cases.foreach { line =>
      val (message, isDeletionInProgress, result) = line

      And(s"Validate the isDeletionInProgress flag when calling signal ${message.getClass.getSimpleName} - expected result: $result")

      victim ! SetDeleteInProgress(isDeletionInProgress)
      expectMsgType[Accepted]
      checkDeleteInProgress(victim, flag = isDeletionInProgress)

      victim ! message
      checkDeleteInProgress(victim, flag = result)
    }
  }

}
