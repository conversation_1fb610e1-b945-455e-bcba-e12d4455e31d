syntax = "proto3";

package com.flutter.baseactor.commands.proto;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  flat_package: true
};

message AcceptedProto {
  int64 timestamp = 1;
}

message FlushProto {
  string shardingKey = 1;
  map<string, string> headers = 2;
  bool isPropagated = 3;
}

message FlushResponseProto {
  string message = 1;
}

message PurgeMeProto {
  string shardingKey = 1;
  map<string, string> headers = 2;
}

message PurgeMeResponseProto {
  string shardingKey = 1;
}

message NoEventStateProto {
  string errorMsg = 1;
}

message GetEventStateProto {
  string shardingKey = 1;
}

message GetEventStateModelProto {
  string shardingKey = 1;
}

message GetStateProto {
  string shardingKey = 1;
}

message ValidateStateProto {
  string shardingKey = 1;
}
