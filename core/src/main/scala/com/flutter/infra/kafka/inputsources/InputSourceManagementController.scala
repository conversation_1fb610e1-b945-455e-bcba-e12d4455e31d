package com.flutter.infra.kafka.inputsources

import com.flutter.jmx.{JMXInputSourceManagementController, JMXInputSourceManagementControllerMBean}
import com.flutter.jmx.beans.HawtioDescriptionsMBean
import com.flutter.utils.MBeanUtils

import javax.management.{MBeanInfo, StandardMBean}

trait InputSourceManagementController extends HawtioDescriptionsMBean {

  val inputSourceManagementJmxName: String

  lazy val inputSourceManagementJmxDomainName: String = s"$inputSourceManagementJmxName:type=Controller,name=InputSourceManagement"

  def enableInputSourceManagementController(inputSources: Seq[InputSource]): Unit = {
    val controller = new JMXInputSourceManagementController(inputSources)

    val mbean = new StandardMBean(controller, classOf[JMXInputSourceManagementControllerMBean]) {
      override def getMBeanInfo: MBeanInfo =
        createMBeanInfoDescriptions(super.getMBeanInfo)
    }

    MBeanUtils.registerMBean(inputSourceManagementJmxDomainName, mbean)
  }

}
