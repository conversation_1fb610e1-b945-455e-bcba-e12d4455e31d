package com.flutter.infra.kafka.inputsources

/**
  * Trait that defines all methods related with input source
  */
trait InputSource {
  /**
    * Start the input source
    *
    * @return Status of start of the input source
    */
  def start(): InputSourceStatus

  /**
    * Get status of the input source
    *
    * @return Status of the input source
    */
  def status(): InputSourceStatus

  /**
    * Stop the input source
    *
    * @return Status of stop of the input source
    */
  def stop(): InputSourceStatus

  /**
    * Returns if input source is enabled at bootstrap
    *
    * @return Boolean of input source enabled at bootstrap
    */
  def isEnabledOnBootstrap: Boolean

  /**
    *  Get name of input source
    *
    * @return Name of input source
    */
  def inputSourceName: String
}
