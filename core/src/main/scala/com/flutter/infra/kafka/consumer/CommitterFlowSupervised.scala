package com.flutter.infra.kafka.consumer

import org.apache.pekko.NotUsed
import org.apache.pekko.kafka.ConsumerMessage.{CommittableOffset, CommittableOffsetBatch}
import org.apache.pekko.kafka.scaladsl.Committer
import org.apache.pekko.kafka.{CommitTimeoutException, CommitterSettings}
import org.apache.pekko.stream.scaladsl.Flow
import org.apache.pekko.stream.{ActorAttributes, Supervision}
import com.flutter.sharedplatforms.LazyLogging

trait CommitterFlowSupervised extends LazyLogging {
  protected def committerSettings: CommitterSettings

  private[consumer] val committerFlow = Committer
    .batchFlow(committerSettings)

  private val resumeOnCommitTimeoutStrategy: Supervision.Decider = {
    case ex: CommitTimeoutException =>
      logger.atWarn()
        .addKeyValue("operation", "batchCommitterFlowSupervised")
        .addKeyValue("exception", ex.getMessage)
        .log("Resuming Committer flow due to Commit Timeout Exception")
      Supervision.Resume
    case ex: Throwable =>
      logger.atError()
        .addKeyValue("operation", "batchCommitterFlowSupervised")
        .setCause(ex)
        .log("Restarting Committer flow due to error")
      Supervision.Restart
  }

  /**
   * Creates a supervised flow for committing Kafka offsets in batches.
   * The flow resumes on CommitTimeoutException and restarts on other exceptions.
   *
   * @return Flow[CommittableOffset, CommittableOffsetBatch, NotUsed] The supervised committer flow.
   */
  def batchCommitterFlowSupervised: Flow[CommittableOffset, CommittableOffsetBatch, NotUsed] = {
    committerFlow
      .addAttributes(ActorAttributes.supervisionStrategy(resumeOnCommitTimeoutStrategy))
  }

}
