package com.flutter.jmx.beans

import com.flutter.jmx.support.JMXUtils
import javax.management.{MBeanInfo, MBeanOperationInfo}

trait HawtioDescriptionsMBean {

  protected def createMBeanInfoDescriptions(originalMBInfo: MBeanInfo): MBeanInfo = {
    new MBeanInfo(
      originalMBInfo.getClassName,
      originalMBInfo.getDescription,
      originalMBInfo.getAttributes,
      originalMBInfo.getConstructors,
      originalMBInfo.getOperations.map(customizeCaption),
      originalMBInfo.getNotifications
    )
  }

  private def customizeCaption(op: MBeanOperationInfo): MBeanOperationInfo = {
    HawtioDescriptions.OPERATION_INFO_MAP
      .getOrElse(op.getName, OperationInfo.empty)
      .withDefaults(op)
      .toMBeanOperationInfo
  }
}

object HawtioDescriptions {
  lazy val OPERATION_INFO_MAP: Map[String, OperationInfo] = Map(
    // ActorState Operations
    "get"                     -> OperationInfo(description = "Retrieve the actor state as a JSON string",  parameters = GBP_ID),
    "purge"                   -> OperationInfo(description = "Purge an Actor with GBP Id",                 parameters = GBP_ID, HEADERS),
    "flush"                   -> OperationInfo(description = "Flush an Actor with GBP Id",                 parameters = GBP_ID, HEADERS, PROPAGATE),
    // validation
    "getValidation"           -> OperationInfo(description = "Validate an actor state with GBP Id",        parameters = GBP_ID),
    "toggleValidationLogging" -> OperationInfo(description = "Toggle validation logging value",            parameters = VALIDATION_TOGGLE),
    "validationLoggingStatus" -> OperationInfo(description = "Get the current validation logging value (true/false)"),
    // HazelCast
    "purgeCache"              -> OperationInfo(description = "Purge the market and selections from cache", parameters = MARKET_ID),
    // InputSource Operations
    "getInputSourceStatus"    -> OperationInfo(description = "Retrieve the status of an input source",     parameters = INPUT_SOURCE_NAME),
    "startIS"                 -> OperationInfo(description = "Start given input source",                   parameters = INPUT_SOURCE_NAME),
    "stopIS"                  -> OperationInfo(description = "Stop given input source",                    parameters = INPUT_SOURCE_NAME),
    "startAll"                -> OperationInfo(description = "Start all input sources"),
    "stopAll"                 -> OperationInfo(description = "Stop all input sources")
  )

  private lazy val GBP_ID = ParamInfo(
    name = "GBP Id",
    description = "Example: urn:sbk:pc:e:gpd:1731568674494",
    paramType = JAVA_STRING_TYPE
  )
  private lazy val MARKET_ID = ParamInfo(
    name = "Market Id (Not GBP Id)",
    description = "Example: 221378921",
    paramType = JAVA_STRING_TYPE
  )
  private lazy val INPUT_SOURCE_NAME = ParamInfo(
    name = "Input Source name",
    description = "Example: pbc-stream-kafka",
    paramType = JAVA_STRING_TYPE
  )
  private lazy val VALIDATION_TOGGLE = ParamInfo(
    name = "Validation logging value",
    description = "To get the current value of validation logging use the operation 'validationLoggingStatus'",
    paramType = BOOLEAN_TYPE
  )
  private lazy val HEADERS = ParamInfo(
    name = "Headers",
    description = "Add headers to be propagated down stream as Kafka Headers. " +
      "Uses the following format: 'key1:value1 key2:value2 key3:value3' (characters [':', ' '] are illegal within keys or values) " +
      "Example: 'correlationId:12345 providerName:PCSA' " +
      s"Legal header keys: ${JMXUtils.ACCEPT_HEADER_KEYS}.",
    paramType = JAVA_STRING_TYPE
  )
  private lazy val PROPAGATE = ParamInfo(
    name = "Propagate Flush",
    description = "If true, the flush will be propagated to the lower entities.",
    paramType = BOOLEAN_TYPE
  )

  private lazy val BOOLEAN_TYPE = "boolean"
  private lazy val JAVA_STRING_TYPE = "java.lang.String"
}
