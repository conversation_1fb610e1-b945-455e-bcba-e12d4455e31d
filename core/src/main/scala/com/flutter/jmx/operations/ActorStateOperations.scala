package com.flutter.jmx.operations

import com.flutter.jmx.dsl.Validate
import com.flutter.jmx.support.{JMXUtils, ParameterConversionSupport}
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import org.apache.pekko.actor.ActorRef


/**
 * This trait allows us to define common, yet explicitly internal methods that will not show up in hawtio.
 * This allows us to do common validations here, without interfering with the available endpoints.
 */
trait ActorStateOperations extends IActorStateOperations with ParameterConversionSupport {
  protected implicit val buildParameterMisuseString: List[String] => String = JMXUtils.buildParameterMisuseString
  protected val NOT_IMPLEMENTED: String = "Not implemented"

  protected val actorResolver: String => Option[ActorRef] = _ => None

  override final def get(gbpId: String): String =
    Validate(
      validateGbpIdAndFindRegionRef(gbpId, actorResolver)
    ).getErrorsOrRun(actorRegion => internalGet(GbpId(gbpId), actorRegion))

  override final def flush(gbpId: String, headerString: String, isPropagated: Boolean): String =
    Validate(
      validateGbpIdAndFindRegionRef(gbpId, actorResolver),
      toHeaders(headerString)
    ).getErrorsOrRun { case (actorRegion, headers) => internalFlush(GbpId(gbpId), actorRegion, headers, isPropagated)}

  override final def purge(gbpId: String, headerString: String): String =
    Validate(
      validateGbpIdAndFindRegionRef(gbpId, actorResolver),
      toHeaders(headerString)
    ).getErrorsOrRun { case (actorRegion, headers) => internalPurge(GbpId(gbpId), actorRegion, headers)}

  override final def getValidation(gbpId: String): String =
    Validate(
      validateGbpIdAndFindRegionRef(gbpId, actorResolver)
    ).getErrorsOrRun(actorRegion => internalGetValidation(GbpId(gbpId), actorRegion))

  override final def validationLoggingStatus(): Boolean = internalValidationLoggingStatus()
  override final def toggleValidationLogging(isLogging: Boolean): String = internalToggleValidationLogging(isLogging)

  protected def internalGet(gbpId: GbpId, sendTo: ActorRef): String
  protected def internalFlush(gbpId: GbpId, sendTo: ActorRef, headers: Map[String, String], isPropagated: Boolean): String
  protected def internalPurge(gbpId: GbpId, sendTo: ActorRef, headers: Map[String, String]): String
  protected def internalGetValidation(gbpId: GbpId, sendTo: ActorRef): String
  protected def internalValidationLoggingStatus(): Boolean
  protected def internalToggleValidationLogging(isLogging: Boolean): String
}
