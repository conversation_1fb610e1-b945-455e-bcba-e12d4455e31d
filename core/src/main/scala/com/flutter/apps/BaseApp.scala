// $COVERAGE-OFF$
package com.flutter.apps

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.kafka.ConsumerMessage.CommittableOffset
import org.apache.pekko.kafka.ProducerSettings
import cats.data.Ior
import cats.effect.IO
import com.flutter.InternalTypes.ActorResolver
import com.flutter.adapter.validator.{NoOPValidator, Validator}
import com.flutter.baseactor.BaseActor.ActorCommand
import com.flutter.implicits
import com.flutter.infra.kafka.configs.KafkaConfig
import com.flutter.infra.kafka.configs.KafkaConfig.getKafkaConfig
import com.flutter.infra.kafka.consumer.{ConsumerMetricsReporter, KafkaConsumerProcessInActor}
import com.flutter.infra.kafka.converter.Converter
import com.flutter.infra.kafka.inputsources.{InputSource, KafkaConsumerInputSourceAdapter}
import com.flutter.infra.kafka.producer.GenericKafkaProducer
import com.flutter.infra.kafka.streams.kafka.KafkaMessageValidationFailures
import com.flutter.observability.Tracer
import com.flutter.publish.MessagePublisher.Publishable
import com.flutter.publish.kafka.KafkaMessagePublisher
import com.flutter.publish.{MessagePublisher, NoOpPublisher}
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.common.serialization.{Deserializer, Serializer}

abstract class BaseApp(override val conf: Config, implicit val system: ActorSystem) extends BaseAppController {

  override var inputSources: Seq[InputSource] = Seq.empty
  def addInputSource(kafkaStreamConsumer: InputSource): Unit = {
    inputSources = inputSources :+ kafkaStreamConsumer
  }

  private def shardIdExtractorByMessageKey[K]: ((K, CommittableOffset, Ior[KafkaMessageValidationFailures, ActorCommand])) => Int = (msg: (K, CommittableOffset, Ior[KafkaMessageValidationFailures, ActorCommand])) => msg._1.toString.hashCode

  def createKafkaStreamInputSource[K, V, Command <: ActorCommand](
                                          actorShardRegion: ActorResolver[Command],
                                          consumerConfig: Config,
                                          converter: Converter[ConsumerRecord[K, V], Command],
                                          keyDeserializer: Deserializer[K],
                                          valueDeserializer: Deserializer[V],
                                          validator: Validator[Command] = new NoOPValidator[Command](),
                                          streamShardIdExtractor: ((K, CommittableOffset, Ior[KafkaMessageValidationFailures, Command])) => Int = shardIdExtractorByMessageKey,
                                          metricsReporter: ConsumerMetricsReporter[Command]
    )(implicit system: ActorSystem
    ): Option[InputSource] = {

    val kafkaClientsConfig = consumerConfig
      .withFallback(conf.getConfig("pekko.kafka.consumer"))

    val topics = KafkaConfig.getConsumerTopics(consumerConfig)

    if (topics.nonEmpty) {
      val kafkaStreamConsumer = KafkaConsumerProcessInActor(
        topics,
        kafkaClientsConfig,
        actorShardRegion,
        converter,
        keyDeserializer,
        valueDeserializer,
        validator,
        streamShardIdExtractor,
        metricsReporter
      )

      val kafkaConsumerInputSourceAdapter = new KafkaConsumerInputSourceAdapter(kafkaStreamConsumer)

      inputSources = inputSources :+ kafkaConsumerInputSourceAdapter

      Some(kafkaConsumerInputSourceAdapter)
    } else {
      Option.empty
    }
  }

  def createKafkaPublisher[T <: Publishable](produceConfig: Config)(implicit system: ActorSystem): IO[MessagePublisher[T]] = IO {
    if (produceConfig.getBoolean("enableOnBootstrap")) {
      val kafkaConfig = getKafkaConfig(produceConfig, "topics", "default-topic.topic")
      val producerConfig = produceConfig.withFallback(system.settings.config.getConfig("pekko.kafka.producer"))
      createKafkaPublisher[T]()(kafkaConfig, producerConfig, system)
    } else {
      new NoOpPublisher[T]()
    }
  }

  private[this] def createKafkaPublisher[T <: Publishable]()(implicit config: KafkaConfig, producerConfig: Config, system: ActorSystem): MessagePublisher[T] = {
    val producerSettings: ProducerSettings[String, Array[Byte]] = ProducerSettings(producerConfig, None, None)
    val kafkaProducer = producerSettings.createKafkaProducer()
    system.registerOnTermination(kafkaProducer.close())
    KafkaMessagePublisher[T](kafkaProducer, config)
  }

  def createGenericKafkaProducer[K, V](kafkaConfigs: Config, keySerializer: Serializer[K], valueSerializer: Serializer[V]): IO[GenericKafkaProducer[K, V]] =
    IO {
      val configs = kafkaConfigs
        .withFallback(conf.getConfig("pekko.kafka.producer"))
      GenericKafkaProducer(configs, keySerializer, valueSerializer)
    }

  def getInternalProducerConfigs(internalConfig: Config): Config = {
    lazy val commonKafkaClientsConf = ConfigFactory.empty().withValue("kafka-clients", internalConfig.getValue("common-kafka-clients"))

    lazy val consumerConf = internalConfig.getConfig("producer")
    lazy val topicConf = ConfigFactory.empty().withValue("topic", consumerConf.getValue("topic"))

    commonKafkaClientsConf.withFallback(
      consumerConf
        .withFallback(topicConf)
    )
  }

  def getInternalConsumerConfigs(internalConfig: Config): Config = {
    lazy val commonKafkaClientsConf = ConfigFactory.empty().withValue("kafka-clients", internalConfig.getValue("common-kafka-clients"))

    lazy val consumerConfig = internalConfig.getConfig("consumer")

    lazy val topicConf = ConfigFactory.empty().withValue("topic", consumerConfig.getValue("topic"))

    commonKafkaClientsConf.withFallback(
      consumerConfig
        .withValue("topics.0", topicConf.root())
    )
  }

  def tracer(implicit config: Config): IO[Unit] = IO(
    Tracer.init()(implicits.config.tracingConfig(config))
  )

  protected def addDefaultsToBaseActorConfigs(baseConfigs: Config, actorConfigs: Config): Config = {
    actorConfigs.withFallback(baseConfigs.getConfig("config.actor"))
  }
}
// $COVERAGE-ON$
