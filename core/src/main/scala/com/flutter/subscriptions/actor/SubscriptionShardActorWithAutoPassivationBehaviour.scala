package com.flutter.subscriptions.actor

import org.apache.pekko.actor.{Cancellable, NoSerializationVerificationNeeded}
import org.apache.pekko.persistence.{PersistentActor, RecoveryCompleted}
import com.flutter.sharedplatforms.LazyLogging

import scala.concurrent.duration.FiniteDuration
import scala.jdk.DurationConverters.JavaDurationOps

case object PassivateSubscriptionShardActor extends NoSerializationVerificationNeeded

trait SubscriptionShardActorWithAutoPassivationBehaviour[D]
  extends PersistentActor with BaseSubscriptionShardActor[D] with LazyLogging {

  private val passivationConfigs = subscriptionShardActorConfig.getConfig("passivation")
  private val onInactivityWhenEmpty: FiniteDuration = passivationConfigs.getDuration("onInactivityWhenEmpty").toScala

  private var lastSchedulePassivation: Option[Cancellable] = None

  private var isToEvaluatedPassivation = false

  override def receiveRecover: Receive = {
    case cmd @ RecoveryCompleted =>
      super.receiveRecover(cmd)
      schedulePassivateIfEmpty()
    case cmd: Any =>
      super.receiveRecover(cmd)
  }

  override def receiveCommand: Receive = {
    case PassivateSubscriptionShardActor =>
      passivateIfEmpty()
    case cmd: Any =>
      isToEvaluatedPassivation = true
      cancelScheduledPassivation()
      super.receiveCommand.apply(cmd)
      if (isToEvaluatedPassivation) {
        schedulePassivateIfEmpty()
      }
  }

  override protected def beforePersistCallback(persisted: SubscriptionEvent): Unit = {
    super.beforePersistCallback(persisted)
    isToEvaluatedPassivation = false
  }

  override protected def afterPersistCallback(persisted: SubscriptionEvent): Unit = {
    super.afterPersistCallback(persisted)
    schedulePassivateIfEmpty()
  }

  private def schedulePassivateIfEmpty(): Unit = {
    if (state.isEmptyWithNoChildren) {
      schedulePassivation()
    }
  }

  private def passivateIfEmpty(): Unit = {
    if (state.isEmptyWithNoChildren) {
      context.stop(self)
    }
  }

  private def schedulePassivation(): Unit = {
    implicit val dispatcher = context.dispatcher
    lastSchedulePassivation = Some(context.system.scheduler.scheduleOnce(onInactivityWhenEmpty, self, PassivateSubscriptionShardActor))
  }

  private def cancelScheduledPassivation(): Unit = {
    lastSchedulePassivation.foreach(_.cancel())
    lastSchedulePassivation = None
  }
}
