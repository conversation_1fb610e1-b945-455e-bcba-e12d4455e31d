package com.flutter.subscriptions.actor.domain.state.serde


import com.flutter.pcsa.actor.subscription.shard.domain.state.proto.SubscriptionShardModelProto
import com.flutter.subscriptions.actor.domain.state.Subscription
import monocle.Iso

trait SubscriptionShardModelIso {

  val subscriptionShardSubscriptionModelIso = Iso[Subscription, SubscriptionShardModelProto] { im =>
    SubscriptionShardModelProto(
      identifier = im.identifier
    )
  } { pb =>
    Subscription(
      identifier = pb.identifier
    )
  }

}
