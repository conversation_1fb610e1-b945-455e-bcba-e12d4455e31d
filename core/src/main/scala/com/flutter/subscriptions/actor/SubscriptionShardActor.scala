// $COVERAGE-OFF$
package com.flutter.subscriptions.actor

import org.apache.pekko.actor.Props
import com.flutter.subscriptions.actor.notifier.SubscribersPublisher
import com.typesafe.config.{Config, ConfigFactory}

/**
 * These commands are always local as this actor will always be a child of an entity actor, so no serialization is needed for these commands
 */

object SubscriptionShardActor {

  private val DefaultConfigs = ConfigFactory.defaultReference()

  def props[N](subscriptionShardActorConfig: Config, subscriptionNotifierPublisher: SubscribersPublisher[N]): Props = {
    Props(new SubscriptionShardActor[N](subscriptionShardActorConfig.withFallback(DefaultConfigs.getConfig("config.subscription-shard-actor")), subscriptionNotifierPublisher))
  }
}

class SubscriptionShardActor[D](override val subscriptionShardActorConfig: Config,
                                override val subscriptionNotifierPublisher: SubscribersPublisher[D])
    extends BaseSubscriptionShardActor[D] {

  override protected val childActorProps: Props = SubscriptionShardActor.props(subscriptionShardActorConfig, subscriptionNotifierPublisher)
}
// $COVERAGE-ON$
