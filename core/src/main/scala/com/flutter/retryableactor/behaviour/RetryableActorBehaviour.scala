package com.flutter.retryableactor.behaviour

import org.apache.pekko.actor.Actor
import com.flutter.baseactor.BaseActor.{Accepted, ActorCommand, ActorEvent}
import com.flutter.baseactor.behaviour.{ActorDomainBehaviour, NotifierActorBehaviour, RecoveryActorBehaviour}
import com.flutter.baseactor.domain.state.ActorState
import com.flutter.retryableactor.behaviour.strategy.RetryableStrategy

import scala.concurrent.Future


trait RetryableActorBehaviour[S <: ActorState, C <: ActorCommand, E <: ActorEvent] extends NotifierActorBehaviour[S, C, E] with ActorDomainBehaviour[S, C] with RecoveryActorBehaviour[S, E]{
  this: Actor =>

  protected val retryableStrategy: RetryableStrategy[S, C, E]

  protected def retryDetector(state: S): Receive = {
    case cmd: C if retryableStrategy.isToIgnore(state, cmd.metadata) =>
      sender() ! Accepted(System.currentTimeMillis())
  }

  protected def retryRecovery(state: S): PartialFunction[Any, S] = {
    case event: E if retryableStrategy.isToIgnore(state, event.metadata) => state
  }

  override def notify(previousState: S, currentState: S, actorEvent: E): Future[Any] = {
    val currentStateUpdated = retryableStrategy.applyMetadataToState(currentState, actorEvent)

    if (retryableStrategy.isARetry(previousState, actorEvent)) {
      val refreshActorEvent = retryableStrategy.getRefreshEvent(actorEvent)
      super.notify(previousState, currentStateUpdated, refreshActorEvent)
    } else {
      super.notify(previousState, currentStateUpdated, actorEvent)
    }
  }

  override def productBehaviour(state: S): Receive =
    retryDetector(state).orElse(super.productBehaviour(state))

  override def providedReceiveRecover(state: S): PartialFunction[Any, S] =
    retryRecovery(state).orElse(super.providedReceiveRecover(state))
}
