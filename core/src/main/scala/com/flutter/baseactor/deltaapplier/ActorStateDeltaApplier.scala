package com.flutter.baseactor.deltaapplier

import com.flutter.baseactor.BaseActor.ActorEvent
import com.flutter.baseactor.deltaapplier.eventhandler.ActorEventHandler
import com.flutter.baseactor.deltaapplier.postprocessor.ActorEventPostProcessor
import com.flutter.baseactor.domain.state.ActorState

/**
 * Trait responsible to apply a actor event on a actorstate and return the new derivated actorstate
 *
 * @tparam S Actor state
 * @tparam E Actor event
 */
class ActorStateDeltaApplier[S <: ActorState, E <: ActorEvent](eventHandlers: List[ActorEventHandler[S, E]], val postProcessors: List[ActorEventPostProcessor[S, E]]) extends DeltaApplier[S, E] {
  /**
   * Build the partial function bases on list of appliers. After this this building it will apply
   * current state and currentState.
   *
   * @return finalPartialFunction
   */
  private def buildDeltaApplierFunction: PartialFunction[(S, E), S] = {
    eventHandlers.foldLeft(PartialFunction.empty[(S, E), S]) {
      case (nextStateCalculator, eventHandler) => nextStateCalculator.orElse(eventHandler.handle())
    }
  }

  /**
   * Make sure that buildDeltaApplierFunction is executed once, because the event handlers will not change
   */
  private[baseactor] val run = buildDeltaApplierFunction

  /**
   * Method responsible to apply the actorEvent on the actorState and return the new state
   * @param currentState Current actor state
   * @param actorEvent   actor event to be applied
   * @return derivated actor state
   */
  final def apply(currentState: S, actorEvent: E): S = {
    val stateWithEventApplied = run.apply(currentState, actorEvent)

    val newState = postProcessors.foldLeft(stateWithEventApplied)({
      case (newState, processor) => processor.apply(currentState, newState, actorEvent)
    })

    newState
  }
}
