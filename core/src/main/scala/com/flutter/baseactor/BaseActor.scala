package com.flutter.baseactor

import org.apache.pekko.actor.Status
import org.apache.pekko.persistence.{PersistentActor, RecoveryCompleted, SnapshotOffer}
import com.flutter.baseactor.BaseActor.{Accepted, ActorCommand, ActorEvent, GetState}
import com.flutter.baseactor.behaviour.{ActorDomainBehaviour, NotifierActorBehaviour, PersistenceBaseActorBehaviour, RecoveryActorBehaviour}
import com.flutter.baseactor.converter.ActorCommand2ActorEvent
import com.flutter.baseactor.domain.state.ActorState
import com.flutter.baseactor.serialization.Proto
import com.flutter.retryableactor.metadata.Metadata.KafkaMetadata
import com.flutter.utils.StringUtils
import com.flutter.sharedplatforms.LazyLogging
import com.flutter.Headers
import com.ppb.platform.sb.common.scala.metrics.MetricsSupport
import com.typesafe.config.Config

import scala.util.{Failure, Success}

object BaseActor extends LazyLogging {

  //region actor command
  trait ActorCommand extends Headers with Proto {
    def shardingKey: String
    val metadata: KafkaMetadata
  }

  //other commands
  trait Message extends Proto
  trait JmxCommand extends Message {
    def shardingKey: String
  }

  //region actor event
  trait ActorEvent extends Headers {
    val metadata: KafkaMetadata
  }

  case class Accepted(timeStamp: Long) extends Message

  case class GetState(shardingKey: String) extends JmxCommand
  case class ValidateState(shardingKey: String) extends JmxCommand

  //endregion

  //endregion

}

trait BaseActor[S <: ActorState, C <: ActorCommand, E <: ActorEvent]
    extends PersistentActor with ActorDomainBehaviour[S, C] with PersistenceBaseActorBehaviour
    with LazyLogging with MetricsSupport {
  this: RecoveryActorBehaviour[S, E] with NotifierActorBehaviour[S, C, E] =>

  import context._

  protected val actorConfig: Config
  protected val NO_STATE: S
  protected val actorCommand2Event: ActorCommand2ActorEvent[C, E]

  override val ActorEventTagName: String = "ActorEvent"

  private val snapshotInterval: Int = actorConfig.getInt("snapshot-interval")
  private val initTime: Long = System.currentTimeMillis()

  private val EventHandlerMetricName = "EventHandler"
  private val DeltaApplierSyncProcessingTimeMetricName = "DeltaApplierSyncProcessingTime"

  private val SuccessTagName = "Success"
  private val YesTagValue = "Yes"
  private val NoTagValue = "No"

  protected val validationLoggingFunction: Option[(S, S) => Unit] = None

  implicit override val persistenceId: String = self.path.name

  final override def receiveCommand: Receive = behaviour(NO_STATE)

  final override def unhandled(message: Any): Unit =
    handledPersistenceMessages
      .apply(message)

  final override def saveSnapshot(snapshot: Any): Unit =
    snapshot match {
      case NO_STATE => logger.atDebug.log(s"[$persistenceId] Stopping with no state - not snapshotting")
      case _        => super.saveSnapshot(snapshot)
    }

  override def receiveRecover: Receive = {
    var state: S = NO_STATE
    val partialFunction: Receive = {
      case cmd: Any =>
        state = defaultReceiveRecovery(state)
          .orElse(providedReceiveRecover(state))(cmd)
    }
    partialFunction
  }

  final protected def behaviour(state: S): Receive = productBehaviour(state)

  override def productBehaviour(state: S): Receive = {
    common(state).orElse(processBaseActorBehaviour(state))
  }

  private def processBaseActorBehaviour(state: S): Receive = {
    case cmd: C =>
      validateOrTransformCommand(state, cmd) match {
        case Some(validCommand) => processCommand(state)(validCommand)
        case None =>
          logger
            .atError()
            .addKeyValue("cmd", cmd)
            .log("operation=validateOrTransformCommand, msg='all operations were removed, command will not execute.'")
          sender() ! Accepted(System.currentTimeMillis())
      }
  }

  def validateOrTransformCommand(state:S, cmd: C): Option[C] = {
    Some(cmd)
  }

  private def processCommand(state: S): Receive = {
    case cmd: C =>
      val toEvent: C => E = cmd => actorCommand2Event.toActorEvent(cmd)

      val currentSender = sender()

      doPersist(cmd, domainEventPersistedWithSuccess)(toEvent)({ persistedEvent =>
        {
          val stateDerivation = metrics
            .time(DeltaApplierSyncProcessingTimeMetricName, StringUtils.getClassName(getClass), extraTags = Map(ActorEventTagName -> getActorEventName(persistedEvent)))(
              metrics.time(DeltaApplierSyncProcessingTimeMetricName, StringUtils.getClassName(getClass))(stateDeltaApplier.apply(state, persistedEvent))
            )

          validationLoggingFunction.foreach(_.apply(state, stateDerivation))

          if (snapshotSequenceNr % snapshotInterval == 0) saveSnapshot(stateDerivation)

          val notifier = this.notify(state, stateDerivation, persistedEvent)

          notifier.onComplete({
            case Success(_) =>
              metrics.count(
                EventHandlerMetricName,
                StringUtils.getClassName(getClass),
                extraTags = Map(ActorEventTagName -> getActorEventName(persistedEvent), SuccessTagName -> YesTagValue),
                1
              )
              logger.atDebug.log(s"Actor event was successfully processed")
              currentSender ! Accepted(System.currentTimeMillis())
            case Failure(exception) =>
              metrics.count(
                EventHandlerMetricName,
                StringUtils.getClassName(getClass),
                extraTags = Map(ActorEventTagName -> getActorEventName(persistedEvent), SuccessTagName -> NoTagValue),
                1
              )
              logger.atError
                .addKeyValue("correlationId", persistedEvent.headers.getOrElse("CORRELATION_ID", ""))
                .log(s"There was an issue processing the Actor Event. Exception: $exception")

              currentSender ! Status.Failure(exception)
          })

          become(behaviour(stateDerivation))
        }
      })
  }

  final protected def doPersist[C <: ActorCommand](
      command: C,
      persistedSuccessHandler: E => Unit
    )(generateEventFromCommand: C => E
    )(afterPersist: E => Unit
    ): Unit = {
    val updatedDE = generateEventFromCommand(command)

    persist(updatedDE) { persisted =>
      persistedSuccessHandler(persisted)
      afterPersist.apply(persisted)
    }
  }

  private def defaultReceiveRecovery(currentState: S): PartialFunction[Any, S] = {
    {
      case SnapshotOffer(meta, state: S) =>
        state match {
          case s: ActorState =>
            logger.atDebug.log(
              s"[$persistenceId] Snapshot offer -> snapshot sequence_nr=${meta.sequenceNr} state inboundSeqNo=${s.inboundSeqNo.value} state lastPublishedNo=${s.lastPublishedNo.value}"
            )
          case _ => ()
        }
        state

      case RecoveryCompleted =>
        val duration = System.currentTimeMillis() - initTime

        currentState match {
          case NO_STATE =>
            logger.atDebug.log(
              s"[$persistenceId] onRecoveryCompleted -> duration=$duration NoState recovered inboundSeqNo=${currentState.inboundSeqNo.value} lastPublishedNo=${currentState.lastPublishedNo.value}"
            )
            become(behaviour(NO_STATE))
            NO_STATE
          case actorState: S =>
            logger.atDebug.log(
              s"[$persistenceId] onRecoveryCompleted -> duration=$duration inboundSeqNo=${actorState.inboundSeqNo.value} lastPublishedNo=${actorState.lastPublishedNo.value}"
            )
            become(behaviour(actorState))
            actorState

        }
    }
  }

  //TODO this common is wrong and should be removed to a behaviour of get state
  private def common(state: S): Receive = {
    case _: GetState =>
      logger.atDebug.log(s"[$persistenceId] GetState -> sending current state")
      sender() ! state
      become(behaviour(state))
  }

  private def domainEventPersistedWithSuccess(actorEvent: E): Unit = {
    logger.atDebug
      .addKeyValue("operation", "actor" + ActorEventTagName + "Handler")
      .addKeyValue("domainEvent", actorEvent)
      .log("Domain event persisted with success")
  }

  override def postStop(): Unit = {
    logger
      .atInfo()
      .addKeyValue("actorName", self.path.name)
      .log(s"Actor ${StringUtils.getClassName(this.getClass)} has been terminated")
    super.postStop()
  }
}