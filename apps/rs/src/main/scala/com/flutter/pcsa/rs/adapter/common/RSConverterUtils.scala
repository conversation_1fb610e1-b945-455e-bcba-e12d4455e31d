package com.flutter.pcsa.rs.adapter.common

import com.flutter.pcsa.common.Global.GLOBAL
import com.flutter.pcsa.common.datatypes.dto.PatchableField
import com.flutter.pcsa.common.domain.dto.PatchableFieldUtils.toPatchableField
import com.flutter.product.catalogue.common.{BoolField, DoubleField, IntField, LongField, StringField}

object RSConverterUtils {

  private def extractValue[Type](instances: Map[String, Type])(implicit instance: String): Option[Type] = {
    instances.get(instance).orElse(instances.get(GLOBAL))
  }

  implicit def rdDoubleFieldToRSCommand(maybeField: Option[DoubleField])(implicit instance: String): PatchableField[Option[Double]] = {
    maybeField.map(field => extractValue(field.instances).flatMap(_.value))
  }

  implicit def rdIntFieldToRSCommand(maybeField: Option[IntField])(implicit instance: String): PatchableField[Option[Int]] = {
    maybeField.map(field => extractValue(field.instances).flatMap(_.value))
  }

  implicit def rdLongFieldToRSCommand(maybeField: Option[LongField])(implicit instance: String): PatchableField[Option[Long]] = {
    maybeField.map(field => extractValue(field.instances).flatMap(_.value))
  }

  implicit def rdStringFieldToRSCommand(maybeField: Option[StringField])(implicit instance: String): PatchableField[Option[String]] = {
    maybeField.map(field => extractValue(field.instances).flatMap(_.value))
  }

  implicit def rdBoolFieldToRSCommand(maybeField: Option[BoolField])(implicit instance: String): PatchableField[Option[Boolean]] = {
    maybeField.map(field => extractValue(field.instances).flatMap(_.value))
  }

}
