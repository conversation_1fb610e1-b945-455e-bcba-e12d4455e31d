package com.flutter.pcsa.rs.actor.event.domain.state.serde

import com.flutter.pcsa.common.datatypes
import com.flutter.pcsa.common.serde.EnumerationsIso.entityIdentifiersIso
import com.flutter.pcsa.rs.actor.event.domain.state
import com.flutter.pcsa.rs.actor.event.domain.state.RSEventModel
import com.flutter.pcsa.rs.actor.event.domain.state.proto.RSEventModelProto
import monocle.Iso

trait RSEventModelIso {

  val rsEventModelIso: Iso[RSEventModel, RSEventModelProto] = Iso[RSEventModel, RSEventModelProto] { im =>
    RSEventModelProto(
      identifiers = entityIdentifiersIso.get(im.entityIdentifiers.identifiers),
      leastMaxBet = im.leastMaxBet,
      mostMaxBet = im.mostMaxBet,
      layToLose = im.layToLose,
      inPlayBettingDelay = im.inPlayBettingDelay,
      sort = im.sort
    )
  } { pb =>
    state.RSEventModel(
      entityIdentifiers = datatypes.EntityIdentifiers(entityIdentifiersIso.reverseGet(Map.from(pb.identifiers))),
      leastMaxBet = pb.leastMaxBet,
      mostMaxBet = pb.mostMaxBet,
      layToLose = pb.layToLose,
      inPlayBettingDelay = pb.inPlayBettingDelay,
      sort = pb.sort
    )
  }
}
