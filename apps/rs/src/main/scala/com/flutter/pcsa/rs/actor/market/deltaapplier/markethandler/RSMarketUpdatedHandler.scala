package com.flutter.pcsa.rs.actor.market.deltaapplier.markethandler


import com.flutter.baseactor.deltaapplier.eventhandler.ActorEventHandler
import com.flutter.baseactor.domain.state.SequenceNumber
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.eventupdated.EntityIdentifiersMerger
import com.flutter.pcsa.rs.actor.market.RSMarketActor.{RSMarketActorEvent, RSMarketUpdated}
import com.flutter.pcsa.rs.actor.market.domain.dto.RSSelectionDTO
import com.flutter.pcsa.rs.actor.market.domain.state._
import com.flutter.pcsa.rs.common.deltaapplier.eventhandler.RSEventHandlerUtils.StateFieldDeltaApplier

object RSMarketUpdatedHandler extends ActorEventHandler[RSMarketActorState, RSMarketActorEvent] with EntityIdentifiersMerger {
  override def handle(): Handler = {
    case (RSMarketNoState, market: RSMarketUpdated) =>
      RSMarketState(
        SequenceNumber(0),
        SequenceNumber(0) + 1,
        applyRSMarketUpdate(RSMarketAggregationModel.empty, market)
      )
    case (RSMarketState(lastPublishedNo, inboundSeqNo, aggregateRiskMarket, _), market: RSMarketUpdated) =>
      RSMarketState(
        lastPublishedNo,
        inboundSeqNo + 1,
        applyRSMarketUpdate(aggregateRiskMarket, market)
      )

  }

  private def applyRSMarketUpdate(rsMarketAggregationModel: RSMarketAggregationModel, market: RSMarketUpdated): RSMarketAggregationModel = {
    val state = rsMarketAggregationModel.marketModel
    val marketUpdate = market.aggregate.market
    rsMarketAggregationModel.copy(
      marketModel = state.copy(
        entityIdentifiers = merge(state.entityIdentifiers, marketUpdate.entityIdentifiers),
        eventID = state.eventID.orElse(marketUpdate.eventID),
        minAccumulator = state.minAccumulator |+| marketUpdate.minAccumulator,
        maxAccumulator = state.maxAccumulator |+| marketUpdate.maxAccumulator,
        forecastStakeFactor = state.forecastStakeFactor |+| marketUpdate.forecastStakeFactor,
        tricastStakeFactor = state.tricastStakeFactor |+| marketUpdate.tricastStakeFactor,
        layToLose = state.layToLose |+| marketUpdate.layToLose,
        placeLayToLose = state.placeLayToLose |+| marketUpdate.placeLayToLose,
        leastMaxBet = state.leastMaxBet |+| marketUpdate.leastMaxBet,
        mostMaxBet = state.mostMaxBet |+| marketUpdate.mostMaxBet,
        inPlayIgnoreTimeConfig = state.inPlayIgnoreTimeConfig |+| marketUpdate.inPlayIgnoreTimeConfig,
        inPlayBettingDelay = state.inPlayBettingDelay |+| marketUpdate.inPlayBettingDelay,
        guaranteedMinimumLayToLose = state.guaranteedMinimumLayToLose |+| marketUpdate.guaranteedMinimumLayToLose
      ),
      selections = rsMarketAggregationModel.selections ++
        market.aggregate.selections
          .map({ case (id, update) =>
            val gbpId = GbpId(id)
            gbpId -> applyRSSelectionUpdate(rsMarketAggregationModel.selections.get(gbpId), update)
          })
    )
  }

  private def applyRSSelectionUpdate(stateSelection: Option[RSSelectionModel], selectionUpdate: RSSelectionDTO): RSSelectionModel = {
    val state = stateSelection.getOrElse(RSSelectionModel.empty)

    state.copy(
      entityIdentifiers = merge(state.entityIdentifiers, selectionUpdate.entityIdentifiers),
      multipleKey = state.multipleKey |+| selectionUpdate.multipleKey,
      lpMaxBet = state.lpMaxBet |+| selectionUpdate.lpMaxBet,
      lpMaxPlace = state.lpMaxPlace |+| selectionUpdate.lpMaxPlace,
      forecastStakeLimit = state.forecastStakeLimit |+| selectionUpdate.forecastStakeLimit,
      tricastStakeLimit = state.tricastStakeLimit |+| selectionUpdate.tricastStakeLimit,
      riskInfo = state.riskInfo |+| selectionUpdate.riskInfo
    )
  }
}
