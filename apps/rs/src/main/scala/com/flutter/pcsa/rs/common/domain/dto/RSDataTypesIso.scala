package com.flutter.pcsa.rs.common.domain.dto

import com.flutter.pcsa.common.datatypes.dto.PatchableField
import com.flutter.pcsa.common.dto.proto.{OptionalBoolProto, OptionalDoubleProto, OptionalInt32Proto, OptionalInt64Proto, OptionalStringProto}
import com.flutter.pcsa.common.domain.dto.PatchableFieldUtils.{fromPatchableField, toPatchableField}

object RSDataTypesIso {

  implicit def optionalDoubleProtoToScala(value: Option[OptionalDoubleProto]): PatchableField[Option[Double]] = {
    value.map(_.value)
  }

  implicit def optionalDoubleProtoToScalaReverse(value: PatchableField[Option[Double]]): Option[OptionalDoubleProto] = {
    fromPatchableField(value).map(OptionalDoubleProto(_))
  }

  implicit def optionalInt32ProtoToScala(value: Option[OptionalInt32Proto]): PatchableField[Option[Int]] = {
    value.map(_.value)
  }

  implicit def optionalInt32ProtoToScalaReverse(value: PatchableField[Option[Int]]): Option[OptionalInt32Proto] = {
    fromPatchableField(value).map(OptionalInt32Proto(_))
  }

  implicit def optionalInt64ProtoToScala(value: Option[OptionalInt64Proto]): PatchableField[Option[Long]] = {
    value.map(_.value)
  }

  implicit def optionalInt64ProtoToScalaReverse(value: PatchableField[Option[Long]]): Option[OptionalInt64Proto] = {
    fromPatchableField(value).map(OptionalInt64Proto(_))
  }

  implicit def optionalStringProtoToScala(value: Option[OptionalStringProto]): PatchableField[Option[String]] = {
    value.map(_.value)
  }

  implicit def optionalStringProtoToScalaReverse(value: PatchableField[Option[String]]): Option[OptionalStringProto] = {
    fromPatchableField(value).map(OptionalStringProto(_))
  }

  implicit def optionalBoolProtoToScala(value: Option[OptionalBoolProto]): PatchableField[Option[Boolean]] = {
    value.map(_.value)
  }

  implicit def optionalBoolProtoToScalaReverse(value: PatchableField[Option[Boolean]]): Option[OptionalBoolProto] = {
    fromPatchableField(value).map(OptionalBoolProto(_))
  }

}
