package com.flutter.pcsa.rs.actor.event.publish

import com.flutter.HOST_NAME
import com.flutter.baseactor.BaseActor.ActorEvent
import com.flutter.headers.ACTOR_EVENT
import com.flutter.pcsa.rs.RS
import com.flutter.product.catalogue.risk.stream.outbound.RiskStreamInstruction
import com.flutter.publish.{Headers, HeadersExtractor}

trait RSHeaders extends HeadersExtractor[RiskStreamInstruction] {

  override def getCurrentTime: java.lang.Long = java.lang.Long.valueOf(System.currentTimeMillis())

  def extractHeaders(actorHeader: Map[String, AnyRef], sequenceNumber: Int, internalSequenceNumber: Int, payload: RiskStreamInstruction, actorEvent: ActorEvent): Map[String, AnyRef] =
    Map(
      Headers.EVENT_ID -> String.valueOf(payload.getEvent.id),
      Headers.SEQUENCE_NUMBER -> java.lang.Integer.valueOf(sequenceNumber),
      Headers.PCSA_INTERNAL_SEQUENCE_NUMBER -> java.lang.Integer.valueOf(internalSequenceNumber),
      Headers.SEQUENCER_HOST -> HOST_NAME,
      Headers.ACTION -> payload.action.toString,
      Headers.SERVICE_IN -> actorHeader.getOrElse(Headers.SERVICE_IN, getCurrentTime),
      Headers.SERVICE_OUT -> getCurrentTime,
      ACTOR_EVENT(RS.ServiceName) -> actorEvent.getClass.getSimpleName
    )
}
