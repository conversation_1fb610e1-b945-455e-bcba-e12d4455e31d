package com.flutter.pcsa.rs.actor.event.notifier.converters

import com.flutter.pcsa.rs.actor.event.domain.state.RSEventAggregationModel
import com.flutter.product.catalogue.risk.stream.outbound.RiskStreamInstruction
import com.flutter.product.catalogue.risk.stream.outbound.RiskStreamInstruction.Action.{CREATE, UPDATE, REFRESH}

trait RSEventAggregationModel2RiskStreamInstructionOps extends RSEventModel2RiskStreamInstructionOps {
  implicit class EventAggregationModel2InstructionOps(agg: RSEventAggregationModel) {

    private lazy val asRiskStreamInstruction: RiskStreamInstruction = RiskStreamInstruction(
      entity = agg.eventModel.asRiskStreamInstructionEventEntity()
    )

    lazy val asCreateRiskStreamInstruction: RiskStreamInstruction = asRiskStreamInstruction.withAction(CREATE)
    lazy val asUpdateRiskStreamInstruction: RiskStreamInstruction = asRiskStreamInstruction.withAction(UPDATE)
    lazy val asRefreshRiskStreamInstruction: RiskStreamInstruction = asRiskStreamInstruction.withAction(REFRESH)
  }
}
