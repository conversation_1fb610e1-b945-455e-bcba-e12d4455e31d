package com.flutter.pcsa.rs.actor.market.notifier

import com.flutter.baseactor.behaviour.CacheWriterProcessor
import com.flutter.baseactor.notifier.ActorStateNotifier
import com.flutter.pcsa.rs.actor.market.RSMarketActor.{RSMarketActorEvent, RSMarketUpdated}
import com.flutter.pcsa.rs.actor.market.domain.state.{RSMarketActorState, RSMarketState}
import com.flutter.pcsa.rs.cache.model.RSMarketCache
import com.flutter.pcsa.common.syntax.SetContainsSyntax.ContainsExtension
import com.flutter.pcsa.rs.common.actor.domain.dto.RSActionDTO.{FLUSH, REFRESH}

import scala.concurrent.Future

class RSMarketCacheStateNotifier(cache: CacheWriterProcessor[RSMarketActorState, RSMarketCache]) extends ActorStateNotifier[RSMarketActorState, RSMarketActorEvent] {

  /**
   * Method responsible to calculate changes between actor states and publish a notification to a stream (if exists)
   *
   * @param previousState Previous actor state
   * @param state         Next actor state
   * @return Future that should only be completed when publish is confirmed
   */
  override def notify(previousState: RSMarketActorState, state: RSMarketActorState, actorEvent: RSMarketActorEvent): Future[Unit] = {
    (previousState, state, actorEvent) match {
      case (_, _, event: RSMarketUpdated) if event.action in (REFRESH, FLUSH) =>
        cache.put(state)
        Future.successful(())
      case (RSMarketState(_, _, previousAggregateRiskMarket, _), RSMarketState(_, _, currentAggregateRiskMarket, _), _) if previousAggregateRiskMarket == currentAggregateRiskMarket =>
        Future.successful(())
      case (_, _, _) =>
        cache.put(state)
        Future.successful(())
    }
  }
}
