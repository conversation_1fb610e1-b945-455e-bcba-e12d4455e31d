package com.flutter.pcsa.rs.actor.market.domain.state.serde

import cats.implicits.catsSyntaxOptionId
import com.flutter.baseactor.domain.state.SequenceNumber
import com.flutter.pcsa.rs.actor.event.domain.state.proto.RSMarketStateProto
import com.flutter.pcsa.rs.actor.market.domain.state.RSMarketState
import com.flutter.retryableactor.MetadataProtocolsIso
import monocle.Iso

object RSMarketStateIso extends RSMarketAggregationModelIso with MetadataProtocolsIso {

  val rsMarketStateIso: Iso[RSMarketState, RSMarketStateProto] = Iso[RSMarketState, RSMarketStateProto] { im =>
    RSMarketStateProto(
      metadata = stateMetadataIso.get(im.metadata),
      lastPublishedNo = im.lastPublishedNo.value,
      inboundSeqNo = im.inboundSeqNo.value,
      aggregateRiskMarket = rsMarketAggregationModelIso.get(im.aggregateRiskMarket).some
    )
  } { pb =>
    RSMarketState(
      metadata = stateMetadataIso.reverseGet(pb.metadata),
      lastPublishedNo = SequenceNumber(pb.lastPublishedNo),
      inboundSeqNo = SequenceNumber(pb.inboundSeqNo),
      aggregateRiskMarket = rsMarketAggregationModelIso.reverseGet(pb.getAggregateRiskMarket)
    )
  }

}
