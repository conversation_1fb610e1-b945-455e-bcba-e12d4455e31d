syntax = "proto3";

package com.flutter.pcsa.rs.actor.common.domain.dto.proto;


import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "common-dto/StringModelTypes.proto";
import "common-dto/DoubleModelTypes.proto";
import "common-dto/Int32ModelTypes.proto";

option (scalapb.options) = {
  flat_package: true
};

message RSSelectionDTOProto {
  map<string, string> identifiers = 1;
  com.flutter.pcsa.common.dto.proto.OptionalStringProto multipleKey = 2;
  com.flutter.pcsa.common.dto.proto.OptionalDoubleProto lpMaxBet = 7;
  com.flutter.pcsa.common.dto.proto.OptionalDoubleProto lpMaxPlace = 8;
  com.flutter.pcsa.common.dto.proto.OptionalInt32Proto forecastStakeLimit = 11;
  com.flutter.pcsa.common.dto.proto.OptionalInt32Proto tricastStakeLimit = 12;
  com.flutter.pcsa.common.dto.proto.OptionalStringProto riskInfo = 13;
}
