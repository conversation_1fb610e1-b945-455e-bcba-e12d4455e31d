syntax = "proto3";

package com.flutter.pcsa.rs.actor.event.domain.state.proto;

import "event-actor-state-model/RSEventAggregationModelProto.proto";
import "KafkaMetadata.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  flat_package: true
};


message RSEventStateProto {
  int32 lastPublishedNo = 1;
  int32 inboundSeqNo = 2;
  RSEventAggregationModelProto aggregateRiskEvent = 3;
  repeated com.flutter.pcsa.actor.common.metadata.proto.KafkaMetadataProto metadata = 4;
}