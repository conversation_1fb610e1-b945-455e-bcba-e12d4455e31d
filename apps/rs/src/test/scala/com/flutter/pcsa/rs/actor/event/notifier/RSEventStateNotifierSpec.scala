package com.flutter.pcsa.rs.actor.event.notifier

import com.flutter.pcsa.common.GenCommon
import com.flutter.pcsa.common.datatypes.EntityIdentifiers.idsAsMap
import com.flutter.pcsa.common.datatypes.{EntityIdentifiers, Platform}
import com.flutter.pcsa.rs.actor.event.RSEventActor.{RSEventActorEvent, RSRefreshActorEvent}
import com.flutter.pcsa.rs.actor.event.domain.state.{RSEventActorState, RSEventNoState, RSEventState}
import com.flutter.pcsa.rs.actor.event.fixtures.RSEventActorStateFixture
import com.flutter.pcsa.rs.common.actor.domain.dto.RSActionDTO
import com.flutter.product.catalogue.risk.stream.outbound.RiskStreamInstruction
import com.flutter.publish.{HeaderPublisher, MessagePublisher}
import com.typesafe.config.{Config, ConfigFactory}
import org.mockito.ArgumentMatchers.any
import org.mockito.{ArgumentCaptor, MockitoSugar}
import org.scalacheck.Gen
import org.scalatest.concurrent.Futures.{PatienceConfig, scaled}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.must.Matchers
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{Await, Future}
import scala.jdk.DurationConverters.JavaDurationOps
import scala.language.postfixOps
import scala.util.{Failure, Success}

class RSEventStateNotifierSpec extends AnyFlatSpecLike with GenCommon with MockitoSugar with Matchers {

  private val eventMsg = "entity must be Event"

  "RSEventStateNotifier" should "calculate changes on CREATION 1 notification of Event with CREATE action" in new RSEventStateNotifierBuilder {
    when(mockHeaderPublisher.publish(any[List[MessagePublisher.Publishable]])).thenReturn(Future.successful(()))

    private val actorEvent = computeEvent(headers, eventEntityIdentifiers)
    private val newState = computeEventState(EventGBPId)

    private val result = victim.notify(RSEventNoState, newState, actorEvent)

    verify(mockHeaderPublisher, times(1)).publish(publishableCaptor.capture())

    private val publishInstructions = publishableCaptor.getValue

    assertResult(1)(publishInstructions.size)

    publishInstructions.headOption match {
      case Some(kafkaPublish: MessagePublisher.KafkaPublish) =>
        assertInstruction(
          kafkaPublish,
          1,
          EventGBPId,
          headers,
          entity => assert(entity.isEvent, eventMsg)
        )
      case _ => fail("Expected KafkaPublish instance")
    }

    awaitResultWithSuccess(result)
  }

  it should "calculate changes and publish when state changes" in new RSEventStateNotifierBuilder {
    when(mockHeaderPublisher.publish(any[List[MessagePublisher.Publishable]]))
      .thenReturn(Future.successful(()))

    private val actorEvent = computeEvent(headers, eventEntityIdentifiers)
    private val newState = computeEventState(EventGBPId)

    private val previousState = computeEventState(EventGBPId)

    private val result = victim.notify(previousState, newState, actorEvent)

    verify(mockHeaderPublisher, times(1)).publish(publishableCaptor.capture())

    private val publishInstructions = publishableCaptor.getValue

    assertResult(1)(publishInstructions.size)

    publishInstructions.headOption match {
      case Some(kafkaPublish: MessagePublisher.KafkaPublish) =>
        assertInstruction(
          kafkaPublish,
          1,
          EventGBPId,
          headers,
          entity => assert(entity.isEvent, eventMsg)
        )
      case _ => fail("Expected KafkaPublish instance")
    }

    awaitResultWithSuccess(result)
  }

  it should "throw exception if fails to publish instructions" in new RSEventStateNotifierBuilder {
    private val actorEvent = computeEvent(headers, eventEntityIdentifiers)
    private val state = computeEventState(EventGBPId)

    when(mockHeaderPublisher.publish(any[List[MessagePublisher.Publishable]]))
      .thenReturn(Future.failed(new RuntimeException("Publish failed")))

    assertThrows[RuntimeException] {
      Await.result(victim.notify(RSEventNoState, state, actorEvent), 5.seconds)
    }
  }

  "RSEventStateNotifier" should "publish a refresh event when state is refreshed" in new RSEventStateNotifierBuilder {
    when(mockHeaderPublisher.publish(any[List[MessagePublisher.Publishable]]))
      .thenReturn(Future.successful(()))

    private val actorEvent = computeEventRefresh(headers)
    private val newState = computeEventState(EventGBPId)

    private val result = victim.notify(RSEventNoState, newState, actorEvent)

    verify(mockHeaderPublisher, times(1)).publish(publishableCaptor.capture())

    private val publishInstructions = publishableCaptor.getValue

    assertResult(1)(publishInstructions.size)

    publishInstructions.headOption match {
      case Some(kafkaPublish: MessagePublisher.KafkaPublish) =>
        assertInstruction(
          kafkaPublish,
          1,
          EventGBPId,
          headers,
          entity => assert(entity.isEvent, "entity must be Event")
        )
      case _ => fail("Expected KafkaPublish instance")
    }
  }

  it should "not publish messages when notifier is disabled" in new RSEventStateNotifierBuilder {
    lazy val disabledNotifier: RSEventStateNotifier = new RSEventStateNotifier(mockHeaderPublisher, false)

    private val actorEvent = computeEvent(headers, eventEntityIdentifiers)
    private val state = computeEventState(EventGBPId)

    when(mockHeaderPublisher.publish(List())).thenReturn(Future.successful(()))

    private val result = disabledNotifier.notify(state, state, actorEvent)

    verify(mockHeaderPublisher, times(0)).publish(List())

    awaitResultWithSuccess(result)
  }

  it should "not publish messages when state is RSMarketNoState" in new RSEventStateNotifierBuilder {

    when(mockHeaderPublisher.publish(any[List[MessagePublisher.Publishable]]))
      .thenReturn(Future.successful(()))

    private val actorEvent = computeEvent(headers, eventEntityIdentifiers)
    private val previousState = RSEventNoState
    private val newState = RSEventNoState

    private val result = victim.notify(previousState, newState, actorEvent)

    verify(mockHeaderPublisher, times(0)).publish(any[List[MessagePublisher.Publishable]])

    awaitResultWithSuccess(result)
  }

  it should "publish regardless of state difference if action is REFRESH or FLUSH" in new RSEventStateNotifierBuilder {

    when(mockHeaderPublisher.publish(any[List[MessagePublisher.Publishable]]))
      .thenReturn(Future.successful(()))

    private val actorEventRefresh = computeEvent(headers, eventEntityIdentifiers).copy(action = RSActionDTO.REFRESH)
    private val actorEventFlush = computeEvent(headers, eventEntityIdentifiers).copy(action = RSActionDTO.FLUSH)
    private val state = computeEventState(EventGBPId)

    private val result1 = victim.notify(state, state, actorEventRefresh)
    private val result2 = victim.notify(state, state, actorEventFlush)

    verify(mockHeaderPublisher, times(2)).publish(any[List[MessagePublisher.Publishable]])

    awaitResultWithSuccess(result1)
    awaitResultWithSuccess(result2)
  }

  trait RSEventStateNotifierBuilder extends RSEventActorStateFixture with MockitoSugar {
    lazy val mockHeaderPublisher: HeaderPublisher = mock[HeaderPublisher]
    lazy val publishableCaptor: ArgumentCaptor[List[MessagePublisher.Publishable]] = ArgumentCaptor.forClass(classOf[List[MessagePublisher.Publishable]])
    lazy val config: Config = ConfigFactory.load("reference.conf")
    lazy val timeout: FiniteDuration = config.getDuration("notifier.event-state-timeout").toScala
    lazy val eventId: String = genLongId.sample.get.toString
    lazy val EventGBPId: String = genLongId.sample.get.toString
    lazy val rampSportId: String = genLongId.sample.get.toString
    lazy val eventEntityIdentifiers: EntityIdentifiers = EntityIdentifiers(Map(Platform.Gbp -> EventGBPId))
    lazy val headers: Map[String, String] = idsAsMap(eventEntityIdentifiers) + ("RAMP_SPORT" -> rampSportId)
    lazy val victim: RSEventStateNotifier = new RSEventStateNotifier(mockHeaderPublisher, true)

    def assertSuccess(expectNumberOfPublishMessages: Int, expectedRampSportId: Option[String], result: Future[Unit]): Unit = {
      when(mockHeaderPublisher.publish(any[List[MessagePublisher.Publishable]])).thenReturn(Future.successful(()))

      val publishInstructions: List[MessagePublisher.Publishable] = publishableCaptor.getValue

      assertResult(expectNumberOfPublishMessages)(publishInstructions.size)
      publishInstructions.foreach {
        case kafkaPublish: MessagePublisher.KafkaPublish =>
          assertInstruction(
            kafkaPublish,
            1,
            EventGBPId,
            headers,
            entity => assert(entity.isMarket, eventMsg)
          )
        case _ => fail("Unexpected publishable type")
      }

      awaitResultWithSuccess(result)
    }

    def assertInstruction(
        result: MessagePublisher.KafkaPublish,
        expectedSequenceNumber: Int,
        expectedId: String,
        expectedHeaders: Map[String, String],
        typeValidation: RiskStreamInstruction.Entity => Unit
      ): Unit = {
      assertResult(expectedSequenceNumber)(result.publishSeqNo.value)
      assertResult(expectedId)(result.key)
      assertResult(expectedHeaders)(result.actorHeaders)
      typeValidation.apply(RiskStreamInstruction.parseFrom(result.serializedPayload).entity)
    }

    def assertNoPublish(result: Future[Unit]): Unit = {
      verify(mockHeaderPublisher, times(0)).publish(any[List[MessagePublisher.Publishable]])
      awaitResultWithSuccess(result)
    }

    def awaitResultWithSuccess(result: Future[Unit]): Unit = {
      result.onComplete {
        case Success(_)  =>
        case Failure(ex) => fail(s"Failure to complete future, exception=$ex")
      }

      Await.ready(result, timeout)
      assert(result.isCompleted)
    }

    def awaitResultFailure(result: Future[Unit], expectedException: Throwable): Unit = {
      ScalaFutures.whenReady(result.failed) { e =>
        e shouldBe expectedException
      }
      assert(result.isCompleted)
    }
  }
}
