package com.flutter.pcsa.rs.actor.market.deltaapplier

import com.flutter.pcsa.rs.actor.event.deltaapplier.postprocessor.RSEventMetadataPostProcessor
import com.flutter.pcsa.rs.serialization.generators.GenRSEvent
import com.flutter.retryableactor.metadata.MetadataUtils
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

class RSEventMetadataPostProcessorSpec extends AnyFlatSpecLike with GenRSEvent {

  trait RSEventMetadataPostProcessorFixture {
    val victim = new RSEventMetadataPostProcessor()

    val metadata = genKafkaMetadata.sample.get
    val metadataKey = MetadataUtils.getKafkaMetadataKey(metadata)

    val noRSEventState = NO_STATE
    val validRSEventState = genRSEventState.sample.get
    val randomNewValidRSEventState = genRSEventState.sample.get.copy(metadata = Map(metadataKey -> metadata.offset))

    val rsEventUpdated = genRSEventUpdated.sample.get
  }

  it should "Apply an RSEventActorEvent to an EventActorNoState" in new RSEventMetadataPostProcessorFixture {
    val result = victim.apply(noRSEventState, noRSEventState, rsEventUpdated)
    result shouldBe noRSEventState
  }

  it should "Apply an RSEventActorEvent to an EventActorState where the previous state was a NoState" in new RSEventMetadataPostProcessorFixture {
    val result = victim.apply(noRSEventState, validRSEventState, rsEventUpdated)
    val key = MetadataUtils.getKafkaMetadataKey(rsEventUpdated.metadata)
    result.metadata.get(key) shouldBe Some(rsEventUpdated.metadata.offset)
  }

  it should "Apply an RSEventActorEvent to an RSEventActorState where the previous state was a valid RSEventActorState containing a Metadata value" in new RSEventMetadataPostProcessorFixture {
    val result = victim.apply(validRSEventState, randomNewValidRSEventState, rsEventUpdated)
    val key = MetadataUtils.getKafkaMetadataKey(rsEventUpdated.metadata)

    result.metadata.size shouldBe 2
    result.metadata.get(metadataKey) shouldBe Some(metadata.offset)
    result.metadata.get(key) shouldBe Some(rsEventUpdated.metadata.offset)
  }
}
