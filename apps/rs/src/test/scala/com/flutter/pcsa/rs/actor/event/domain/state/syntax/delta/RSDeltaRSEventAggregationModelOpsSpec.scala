package com.flutter.pcsa.rs.actor.event.domain.state.syntax.delta

import com.flutter.pcsa.common.datatypes.{EntityIdentifiers, Platform}
import com.flutter.pcsa.rs.actor.event.domain.state.{RSEventAggregationModel, RSEventModel}
import com.flutter.pcsa.rs.actor.event.domain.state.generators.GenRSEventAggregationModel
import com.flutter.pcsa.rs.actor.event.domain.state.implicits.deltaSyntax.deltaRSEventAggregationModel
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class RSDeltaRSEventAggregationModelOpsSpec extends AnyFlatSpec with Matchers with GenRSEventAggregationModel {

  "RSDeltaEventAggregationModel" should "retrieve delta between RSEventAggregationModels " in {
    val newInPlayValue = genOptionalInt.sample.get

    ///arrange
    val previousState = getRSEventAggregationModelFromGen
    val state = previousState.copy(
      eventModel = previousState.eventModel.copy(
        inPlayBettingDelay = newInPlayValue
      )
    )

    val emptyRSEventModel = RSEventModel.empty
    val expected = RSEventAggregationModel.empty.copy(
      eventModel = emptyRSEventModel.copy(
        entityIdentifiers = EntityIdentifiers(previousState.eventModel.entityIdentifiers.identifiers.filter(_._1 == Platform.Gbp)),
        inPlayBettingDelay = newInPlayValue
      )
    )
    //act
    val actual = state |-| previousState
    //assert
    actual should be(expected)
  }

  def getRSEventAggregationModelFromGen: RSEventAggregationModel = genRSEventAggregationModel.sample.get
}
