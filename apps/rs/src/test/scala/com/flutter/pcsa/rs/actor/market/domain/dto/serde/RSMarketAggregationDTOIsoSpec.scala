package com.flutter.pcsa.rs.actor.market.domain.dto.serde

import com.flutter.pcsa.common.datatypes.dto.ToUpdateField
import com.flutter.pcsa.rs.actor.event.serde.EventProtocolsIso.rsMarketAggregationDTOIso
import com.flutter.pcsa.rs.actor.market.domain.dto.{RSMarketAggregationDTO, RSMarketDTO}
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.{TableDrivenPropertyChecks, TableFor1}
import org.scalatest.propspec.AnyPropSpec
import org.scalatestplus.scalacheck.ScalaCheckDrivenPropertyChecks

class RSMarketAggregationDTOIsoSpec extends AnyPropSpec with ScalaCheckDrivenPropertyChecks with TableDrivenPropertyChecks with Matchers {
  property("Event data should be isomorphic") {
    val cases: TableFor1[RSMarketAggregationDTO] = Table(
      "givenModel",
      RSMarketAggregationDTO.empty,
      RSMarketAggregationDTO.empty.copy(RSMarketDTO.emptyMarketRiskEntity.copy(maxAccumulator = ToUpdateField(Some(10))))

    )

    forAll(cases) { givenModel =>
      val pb = rsMarketAggregationDTOIso.get(givenModel)
      val result = rsMarketAggregationDTOIso.reverseGet(pb)

      givenModel shouldBe result
    }
  }


}
