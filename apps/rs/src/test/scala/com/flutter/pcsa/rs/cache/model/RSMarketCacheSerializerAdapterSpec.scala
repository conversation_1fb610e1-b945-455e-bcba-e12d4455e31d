package com.flutter.pcsa.rs.cache.model

import com.flutter.pcsa.rs.serialization.generators.GenRSMarketCache
import com.hazelcast.internal.serialization.impl.{DefaultSerializationServiceBuilder, ObjectDataInputStream, ObjectDataOutputStream}
import org.apache.commons.lang3.builder.EqualsBuilder
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import java.io.{ByteArrayInputStream, ByteArrayOutputStream}

class RSMarketCacheSerializerAdapterSpec extends AnyFlatSpec with Matchers with GenRSMarketCache {

  val emptyVictim = RSMarketCacheSerializerAdapter()
  val populatedVictim = RSMarketCacheSerializerAdapter(genRSMarketCache.sample)


  "RSMarketCacheSerializerAdapter" should "Serialize and Deserialize Adapters with Empty RSMarketCaches" in {
    val serializationService = new DefaultSerializationServiceBuilder().build()

    val outputStream = new ByteArrayOutputStream()
    val output = new ObjectDataOutputStream(outputStream, serializationService)

    emptyVictim.writeData(output)

    val inputStream = new ByteArrayInputStream(outputStream.toByteArray)
    val input = new ObjectDataInputStream(inputStream, serializationService)

    val deserializedVictim = RSMarketCacheSerializerAdapter()
    deserializedVictim.readData(input)

    EqualsBuilder.reflectionEquals(deserializedVictim, emptyVictim)
    deserializedVictim.getClassId shouldBe 1
    deserializedVictim.getFactoryId shouldBe 1
  }

  it should "Serialize and Deserialize Adapters with RSMarketCaches" in {

    val serializationService = new DefaultSerializationServiceBuilder().build()

    val outputStream = new ByteArrayOutputStream()
    val output = new ObjectDataOutputStream(outputStream, serializationService)

    populatedVictim.writeData(output)

    val inputStream = new ByteArrayInputStream(outputStream.toByteArray)
    val input = new ObjectDataInputStream(inputStream, serializationService)

    val toDeserialize = RSMarketCacheSerializerAdapter()
    toDeserialize.readData(input)

    EqualsBuilder.reflectionEquals(toDeserialize, populatedVictim)

    toDeserialize.getClassId shouldBe 1
    toDeserialize.getFactoryId shouldBe 1
  }
}
