package com.flutter.pcsa.rs.cache.hazelcast

import com.typesafe.config.{Config, ConfigFactory}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class HazelcastProviderSpec extends AnyFlatSpec with Matchers{
  private lazy val cacheConfig: Config = ConfigFactory.parseString(
    """
      |http {
      | askTimeout = 1000
      | cacheClusterName = "localhost"
      | cacheClusterAddress = "localhost"
      | cacheClusterEnabled = true
      | cacheMapName = "default"
      | cacheClusterNodeList = "node1;node2;node3"
      | cacheTTLDurationSec = 180
      |}
      |""".stripMargin)

  "HazelcastInstance" should "return a hazelcast instance" in {
    val victim = HazelcastProvider(cacheConfig)
    val comparingInstance = HazelcastProvider(cacheConfig)

    victim.getName shouldBe "localhost"

    victim.getConfig.getClusterName shouldBe "localhost"
    victim.getConfig.getMapConfig(cacheConfig.getString("http.cacheMapName")).getName shouldBe "default"
    victim.getConfig.getMapConfig(cacheConfig.getString("http.cacheMapName")).getTimeToLiveSeconds shouldBe 0

    victim shouldEqual comparingInstance
  }
}
