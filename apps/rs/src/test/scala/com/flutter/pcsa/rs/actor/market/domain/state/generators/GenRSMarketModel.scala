package com.flutter.pcsa.rs.actor.market.domain.state.generators

import com.flutter.pcsa.common.datatypes.{GenPlatform, Platform}
import com.flutter.pcsa.rs.actor.market.domain.state.RSMarketModel
import org.scalacheck.Gen

trait GenRSMarketModel extends GenPlatform{

  lazy val genRSMarketModel: Gen[RSMarketModel] = for {
    entityIdentifiers <- genEntityIdentifiersWithHL("Market")
    eventID <- Gen.some(genGbpUrnLongFormatWithHL("Event"))
    minAccumulator <- genOptionalInt
    maxAccumulator <- genOptionalInt
    forecastStakeFactor <- genOptionalLong
    tricastStakeFactor <- genOptionalLong
    layToLose <- genOptionalDouble
    placeLayToLose <- genOptionalDouble
    leastMaxBet <- genOptionalDouble
    mostMaxBet <- genOptionalDouble
    inPlayIgnoreTimeConfig <- genOptionalBoolean
    inPlayBettingDelay <- genOptionalInt
    guaranteedMinimumLayToLose <- genOptionalDouble
  } yield RSMarketModel(
    entityIdentifiers,
    eventID,
    minAccumulator,
    maxAccumulator,
    forecastStakeFactor,
    tricastStakeFactor,
    layToLose,
    placeLayToLose,
    leastMaxBet,
    mostMaxBet,
    inPlayIgnoreTimeConfig,
    inPlayBettingDelay,
    guaranteedMinimumLayToLose
  )
}
