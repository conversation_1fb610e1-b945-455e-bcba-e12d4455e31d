package com.flutter.pcsa.rs.cache

import com.flutter.cache.hazelcast.TTLHazelcastCache
import com.flutter.pcsa.common.datatypes.EntityIdentifiers
import com.flutter.pcsa.rs.actor.market.domain.state.generators.GenRSSelectionModel
import com.flutter.pcsa.rs.actor.market.domain.state.{RSMarketActorState, RSMarketAggregationModel, RSMarketModel, RSMarketState}
import com.flutter.pcsa.rs.cache.converters.{RSMarketCacheKeyConverter, RSMarketCachePayloadConverter}
import com.flutter.pcsa.rs.cache.model.{RSMarketCache, RSMarketCacheSerializerAdapter}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{doReturn, when}
import org.mockito.MockitoSugar.mock
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class RSMarketCacheProcessorSpec extends AnyFlatSpec with Matchers with GenRSSelectionModel {

  lazy val mockedCache = mock[TTLHazelcastCache[RSMarketCacheSerializerAdapter]]
  lazy val mockedKeyConverter = mock[RSMarketCacheKeyConverter]
  val mockedPayloadConverter = mock[RSMarketCachePayloadConverter]
  val victim = new RSMarketCacheProcessor(Some(mockedCache), mockedKeyConverter, mockedPayloadConverter)

  "RSMarketCacheProcessor" should "have an enabled cache" in {
    victim.isEnabled shouldBe true
  }

  "RSMarketCacheProcessor" should "add and retrieve a value" in {

    val mockedRSMarketState = mock[RSMarketState]
    val mockedMarketCacheModel = createMockedRSMarketCache
    val mockedRSMarketCacheSerializer = createMockedRSMarketCacheSerializerAdapter(mockedMarketCacheModel)

    when(mockedKeyConverter.convert(any[RSMarketState]())).thenReturn(Some("4334"))
    when(mockedPayloadConverter.convert(any[RSMarketActorState])).thenReturn(mockedMarketCacheModel)
    when(mockedCache.get("4334")).thenReturn(Some(mockedRSMarketCacheSerializer))
    when(mockedCache.containsKey("4334")).thenReturn(true)
    victim.put(mockedRSMarketState)

    victim.isEnabled shouldBe true

    victim.containsKey("4334") shouldBe true

    val result = victim.get("4334")

    result.get.leastMaxBet shouldBe 4.52
    result.get.mostMaxBet shouldBe 5.57
    result.get.layToLose shouldBe 4.52
    result.get.inPlayIgnoreTimeConfig shouldBe false
    result.get.leastMaxBet shouldBe 4.52
  }

  "RSMarketCacheProcessor" should "add, retrieve and remove a value" in {

    val mockedRSMarketState = mock[RSMarketState]
    val mockedMarketCacheModel = createMockedRSMarketCache
    val mockedRSMarketCacheSerializer = createMockedRSMarketCacheSerializerAdapter(mockedMarketCacheModel)
    val CURRENT_MARKET_ID = "4334"

    when(mockedKeyConverter.convert(any[RSMarketState]())).thenReturn(Some("4334"))
    when(mockedPayloadConverter.convert(any[RSMarketActorState])).thenReturn(mockedMarketCacheModel)
    when(mockedCache.get(CURRENT_MARKET_ID)).thenReturn(Some(mockedRSMarketCacheSerializer))
    when(mockedCache.remove(CURRENT_MARKET_ID)).thenReturn(mockedRSMarketCacheSerializer)
    when(mockedCache.containsKey(CURRENT_MARKET_ID)).thenReturn(true)
    victim.put(mockedRSMarketState)

    victim.isEnabled shouldBe true

    victim.containsKey("4334") shouldBe true

    val result = victim.get("4334")

    result.get.leastMaxBet shouldBe 4.52
    result.get.mostMaxBet shouldBe 5.57
    result.get.layToLose shouldBe 4.52
    result.get.inPlayIgnoreTimeConfig shouldBe false
    result.get.leastMaxBet shouldBe 4.52

    val removedResult = victim.remove("4334")

    removedResult.get.leastMaxBet shouldBe 4.52
    removedResult.get.mostMaxBet shouldBe 5.57
    removedResult.get.layToLose shouldBe 4.52
    removedResult.get.inPlayIgnoreTimeConfig shouldBe false
    removedResult.get.leastMaxBet shouldBe 4.52
  }

  it should "Return a None if no data is found in cache" in {
    when(mockedKeyConverter.convert(any[RSMarketState]())).thenReturn(None)

    victim.isEnabled shouldBe true
    victim.containsKey("4335") shouldBe false

    val result = victim.get("4335")

    result shouldBe None
  }

  def createMockedRSMarketCacheSerializerAdapter(mockedRSMarketCache: Option[RSMarketCache]): RSMarketCacheSerializerAdapter = {
    val mockedRSMarketCacheSerializerAdapter = mock[RSMarketCacheSerializerAdapter]
    doReturn(mockedRSMarketCache).when(mockedRSMarketCacheSerializerAdapter).marketCache
    mockedRSMarketCacheSerializerAdapter
  }

  def createMockedRSMarketCache: Option[RSMarketCache] = {

    val mockedMarketCache = mock[RSMarketCache]
    doReturn(4.52D).when(mockedMarketCache).leastMaxBet
    doReturn(5.57D).when(mockedMarketCache).mostMaxBet
    doReturn(4.52D).when(mockedMarketCache).layToLose
    doReturn(false).when(mockedMarketCache).inPlayIgnoreTimeConfig
    doReturn(4.52D).when(mockedMarketCache).leastMaxBet

    Some(mockedMarketCache)
  }

  def createMockedRSMarketState: RSMarketState = {
    val entityIdentifiers = mock[EntityIdentifiers]
    doReturn(Option("4334")).when(entityIdentifiers).getGbpId

    val marketModel = mock[RSMarketModel]
    doReturn(entityIdentifiers).when(marketModel).entityIdentifiers

    val aggregateModel = mock[RSMarketAggregationModel]
    doReturn(marketModel).when(aggregateModel).marketModel

    val state = mock[RSMarketState]
    doReturn(aggregateModel).when(state).aggregateRiskMarket
    state
  }

}
