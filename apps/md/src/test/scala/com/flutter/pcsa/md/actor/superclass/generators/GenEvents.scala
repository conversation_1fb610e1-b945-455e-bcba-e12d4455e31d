package com.flutter.pcsa.md.actor.superclass.generators

import com.flutter.baseactor.GenActorState
import com.flutter.pcsa.md.actor.superclass.MDSuperclassActor.MDSuperclassFeedCommandApplied
import com.flutter.pcsa.md.actor.superclass.domain.dto.generators.GenMDSuperclassDTO
import com.flutter.pcsa.md.common.actor.domain.dto.MDFeedActionDTO
import com.flutter.pcsa.md.common.domain.dto.generators.GenMDFeedActionDTO
import com.flutter.retryableactor.GenActorMetadata
import org.scalacheck.Gen

trait GenEvents extends GenActorState with GenMDFeedActionDTO with GenMDSuperclassDTO with GenActorMetadata {
  lazy val genMDSuperclassFeedCommandApplied: Gen[MDSuperclassFeedCommandApplied] = genMDSuperclassFeedCommandApplied()

  def genMDSuperclassFeedCommandApplied(
                                              genHeaders: Gen[Map[String, String]] = genMap(genString, genString),
                                              genAction: Gen[MDFeedActionDTO.Value] = genMDFeedActionDTO): Gen[MDSuperclassFeedCommandApplied] =
    for {
      metadata <- genKafkaMetadata
      headers <- genHeaders
      action <- genAction
      superclass <- genMDSuperclassDTO
    } yield MDSuperclassFeedCommandApplied(
      metadata = metadata,
      headers = headers,
      action = action,
      superclass = superclass
    )
}
