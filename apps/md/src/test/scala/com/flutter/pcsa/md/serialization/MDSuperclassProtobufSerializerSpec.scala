package com.flutter.pcsa.md.serialization

import com.flutter.pcsa.md.actor.superclass.generators.{GenCommands, GenEvents}
import com.flutter.pcsa.md.actor.superclass.state.generators.GenMDSuperclassState
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.scalacheck.ScalaCheckPropertyChecks

import java.io.NotSerializableException

class MDSuperclassProtobufSerializerSpec extends AnyFlatSpec with Matchers with ScalaCheckPropertyChecks  {

  it should "serialize and deserialize for MDSuperclassFeedCommand" in new MDSuperclassProtobufSerializerFixture {
    forAll(genMDSuperclassFeedCommand) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for MDSuperclassFeedCommandAppliedEvent" in new MDSuperclassProtobufSerializerFixture {
    forAll(genMDSuperclassFeedCommandApplied) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for MDSuperclassState" in new MDSuperclassProtobufSerializerFixture {
    forAll(genMDSuperclassState) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "fail serializing non Proto type" in new MDSuperclassProtobufSerializerFixture {
    assertThrows[NotSerializableException] {
      serializer.toBinary(NonSerializableProto("testing"))
    }
  }

}
trait MDSuperclassProtobufSerializerFixture extends GenEvents with GenCommands with GenMDSuperclassState {
  val serializer = new MDSuperclassProtobufSerializer()
  case class NonSerializableProto(data: String)
}
