package com.flutter.pcsa.md.actor.eventtype.subscription

import com.flutter.pcsa.md.subscription.notifications.contract.proto.MDSubscriptionNotificationInstruction
import com.flutter.pcsa.md.subscription.notifications.contract.proto.MDSubscriptionNotificationInstruction.Notification
import com.flutter.subscriptions.actor.domain.state.Subscription
import org.mockito.MockitoSugar.mock
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.OptionValues._


class MDEventTypeSubscriptionNotification2InstructionSpec extends AnyFlatSpecLike with Matchers with TableDrivenPropertyChecks {

  val notificationMock: Notification = mock[Notification]
  val identifier1 = "1"
  val identifier2 = "2"
  val subscription1: Subscription = Subscription(identifier1)
  val subscription2: Subscription = Subscription(identifier2)
  val victim = new MDEventTypeSubscriptionNotification2Instruction[Notification]

  it should "convert with success" in {
    val table = Table(
      ("subscriptions", "expectation"),
      (Set.empty[Subscription], List.empty[String]),
      (Set(subscription1, subscription2), List(identifier1, identifier2))
    )

    forAll(table) { (subscriptions, expectation) =>
      val result = victim.convert((subscriptions, notificationMock))
      result.left shouldBe None
      val instruction = result.right.value
      instruction shouldBe a[MDSubscriptionNotificationInstruction]
      instruction.subscribers shouldBe expectation
      instruction.notification shouldBe notificationMock
      instruction.schemaVersion shouldBe victim.SCHEMA_VERSION
    }
  }
}
