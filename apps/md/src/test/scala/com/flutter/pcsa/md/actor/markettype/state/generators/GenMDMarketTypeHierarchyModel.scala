package com.flutter.pcsa.md.actor.markettype.state.generators

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.state.model.model.HierarchyLinkMetadata
import com.flutter.pcsa.common.state.model.model.generators.GenHierarchyLinkMetadata
import com.flutter.pcsa.md.actor.markettype.domain.state.MDMarketTypeHierarchyModel
import org.scalacheck.Gen

trait GenMDMarketTypeHierarchyModel extends GenHierarchyLinkMetadata {

  lazy val genMDMarketTypeHierarchyModel: Gen[MDMarketTypeHierarchyModel] = genMarketTypeHierarchyModel()

  lazy val emptyMDMarketTypeHierarchyModel: MDMarketTypeHierarchyModel = MDMarketTypeHierarchyModel(
    subclass = HierarchyLinkMetadata(GbpId.empty, feedCreated = false),
    superclass = HierarchyLinkMetadata(GbpId.empty, feedCreated = false)
  )

  def genMarketTypeHierarchyModel(): Gen[MDMarketTypeHierarchyModel] =
      for {
        subclass <- genHierarchyLinkMetadataSubclass
        superclass <- genHierarchyLinkMetadataSuperclass
    } yield MDMarketTypeHierarchyModel(
        subclass = subclass,
        superclass = superclass
      )

}
