package com.flutter.pcsa.md.actor.superclass.domain.dto

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.md.actor.superclass.domain.dto.generators.GenMDSuperclassDTO
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

class MDSuperclassDTOSpec extends AnyFlatSpecLike with Matchers with GenMDSuperclassDTO {

  "MDSuperclassDTO" should "provide an empty entity" in {
    val result = MDSuperclassDTO.empty
    result.identifier shouldBe GbpId.empty
  }

  "MDSuperclassDTO" should "provide a fulfilled entity" in {
    val result = genMDSuperclassDTO.sample.get
    result != MDSuperclassDTO.empty shouldBe true
    result.identifier != GbpId.empty shouldBe true
  }
}
