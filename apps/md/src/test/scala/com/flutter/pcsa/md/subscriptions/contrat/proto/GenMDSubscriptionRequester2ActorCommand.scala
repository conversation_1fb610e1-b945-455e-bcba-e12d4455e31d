package com.flutter.pcsa.md.subscriptions.contrat.proto

import com.flutter.pcsa.common.GenCommon
import com.flutter.pcsa.common.datatypes.gbpid.{GbpIdHierarchyLevel, GenGbpId}
import com.flutter.pcsa.md.subscriptions.contract.proto.MDSubscriptionInstruction
import com.flutter.pcsa.md.subscriptions.contract.proto.MDSubscriptionInstruction.Command.{SubscribeEventType, SubscribeMarketTypeLink, SubscribeSubclass, SubscribeSuperclass, UnsubscribeEventType, UnsubscribeMarketTypeLink, UnsubscribeSubclass, UnsubscribeSuperclass}
import org.scalacheck.Gen

trait GenMDSubscriptionRequester2ActorCommand extends GenCommon with GenGbpId {
  lazy val genMDSubscribeEventTypeProto: Gen[MDSubscriptionInstruction.MDSubscribeEventType] = for {
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
  } yield MDSubscriptionInstruction.MDSubscribeEventType(
    eventTypeId = eventTypeId.asLong,
    marketTypeLinkId = marketTypeLinkId.asLong
  )

  lazy val genSubscribeEventType: Gen[SubscribeEventType] = for {
    value <- genMDSubscribeEventTypeProto
  } yield SubscribeEventType(value)

  lazy val genMDUnsubscribeEventTypeProto: Gen[MDSubscriptionInstruction.MDUnsubscribeEventType] = for {
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
  } yield MDSubscriptionInstruction.MDUnsubscribeEventType(
    eventTypeId = eventTypeId.asLong,
    marketTypeLinkId = marketTypeLinkId.asLong
  )

  lazy val genUnsubscribeEventType: Gen[UnsubscribeEventType] = for {
    value <- genMDUnsubscribeEventTypeProto
  } yield UnsubscribeEventType(value)

  lazy val genMDSubscribeMarketTypeLinkProto: Gen[MDSubscriptionInstruction.MDSubscribeMarketTypeLink] = for {
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    marketId <- genString
  } yield MDSubscriptionInstruction.MDSubscribeMarketTypeLink(
    marketTypeLinkId = marketTypeLinkId.asLong,
    marketId = marketId
  )

  lazy val genSubscribeMarketTypeLink: Gen[SubscribeMarketTypeLink] = for {
    value <- genMDSubscribeMarketTypeLinkProto
  } yield SubscribeMarketTypeLink(value)

  lazy val genMDUnsubscribeMarketTypeLinkProto: Gen[MDSubscriptionInstruction.MDUnsubscribeMarketTypeLink] = for {
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    marketId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Market)
  } yield MDSubscriptionInstruction.MDUnsubscribeMarketTypeLink(
    marketTypeLinkId = marketTypeLinkId.asLong,
    marketId = marketId.asLong
  )

  lazy val genUnsubscribeMarketTypeLink: Gen[UnsubscribeMarketTypeLink] = for {
    value <- genMDUnsubscribeMarketTypeLinkProto
  } yield UnsubscribeMarketTypeLink(value)

  lazy val genMDSubscribeSubclassProto: Gen[MDSubscriptionInstruction.MDSubscribeSubclass] = for {
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    subclassId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Subclass)
  } yield MDSubscriptionInstruction.MDSubscribeSubclass(
    eventTypeId = eventTypeId.asLong,
    subclassId = subclassId.asLong
  )

  lazy val genSubscribeSubclass: Gen[SubscribeSubclass] = for {
    value <- genMDSubscribeSubclassProto
  } yield SubscribeSubclass(value)

  lazy val genMDUnsubscribeSubclassProto: Gen[MDSubscriptionInstruction.MDUnsubscribeSubclass] = for {
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    subclassId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Subclass)
  } yield MDSubscriptionInstruction.MDUnsubscribeSubclass(
    eventTypeId = eventTypeId.asLong,
    subclassId = subclassId.asLong
  )

  lazy val genUnsubscribeSubclass: Gen[UnsubscribeSubclass] = for {
    value <- genMDUnsubscribeSubclassProto
  } yield UnsubscribeSubclass(value)

  lazy val genMDSubscribeSuperclassProto: Gen[MDSubscriptionInstruction.MDSubscribeSuperclass] = for {
    superclassId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Superclass)
    subclassId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Subclass)
  } yield MDSubscriptionInstruction.MDSubscribeSuperclass(
    superclassId = superclassId.asLong,
    subclassId = subclassId.asLong
  )

  lazy val genSubscribeSuperclass: Gen[SubscribeSuperclass] = for {
    value <- genMDSubscribeSuperclassProto
  } yield SubscribeSuperclass(value)

  lazy val genMDUnsubscribeSuperclassProto: Gen[MDSubscriptionInstruction.MDUnsubscribeSuperclass] = for {
    superclassId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Superclass)
    subclassId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Subclass)
  } yield MDSubscriptionInstruction.MDUnsubscribeSuperclass(
    superclassId = superclassId.asLong,
    subclassId = subclassId.asLong
  )

  lazy val genUnsubscribeSuperclass: Gen[UnsubscribeSuperclass] = for {
    value <- genMDUnsubscribeSuperclassProto
  } yield UnsubscribeSuperclass(value)
}
