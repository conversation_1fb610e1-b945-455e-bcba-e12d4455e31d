package com.flutter.pcsa.md.actor.markettype.behaviour

import com.flutter.baseactor.BaseActor.ValidateState
import com.flutter.common.BaseAkkaSpec
import com.flutter.pcsa.common.GenCommon
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.md.actor.markettype.state.generators.GenMDMarketTypeState
import com.flutter.pcsa.md.actor.markettype.domain.state.{MDMarketTypeActorState, MDMarketTypeLinkModel, MDMarketTypeNoState, MDMarketTypeState}
import com.flutter.pcsa.md.common.actor.behaviour.{EntityDomainValidation, ValidationResponse}
import com.flutter.pcsa.md.common.actor.validator.EntityDomainValidationDescription
import com.typesafe.config.ConfigFactory
import org.apache.pekko.actor.{Actor, ActorRef, ActorSystem, Props}
import org.apache.pekko.testkit.TestProbe
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.verifyNoInteractions


class MDMarketTypeActorDomainValidationBehaviourSpec extends BaseAkkaSpec(
  ActorSystem("MDMarketTypeActorDomainValidationBehaviourSpec", BaseAkkaSpec.LOCAL_CLUSTER_DEFAULT.withFallback(ConfigFactory.load()))
) with GenCommon with GenMDMarketTypeState {

  trait MDMarketTypeActorDomainValidationBehaviourFixture {

    private lazy val actorEventId: Int = genInt.sample.get
    lazy val persistenceId: String = s"DomainValidatingBehaviour_${genLong.sample.get + 1}_$actorEventId"
    lazy val persistenceIdV2: String = s"DomainValidatingBehaviour_${genLong.sample.get + 1}_$actorEventId"

    lazy val randomState: MDMarketTypeState = genMDMarketTypeState.sample.get

    lazy val validState: MDMarketTypeState = randomState.copy(
      marketTypeAggregation = randomState.marketTypeAggregation.copy(
        marketType = randomState.marketTypeAggregation.marketType.copy(feedCreated = true, hierarchyCreated = true)))

    lazy val invalidStateNoCreate: MDMarketTypeState = randomState.copy(
      marketTypeAggregation = randomState.marketTypeAggregation.copy(
        marketType = randomState.marketTypeAggregation.marketType.copy(feedCreated = false, hierarchyCreated = true)))

    lazy val invalidStateNoHierarchy: MDMarketTypeState = randomState.copy(
      marketTypeAggregation = randomState.marketTypeAggregation.copy(
        marketType = randomState.marketTypeAggregation.marketType.copy(feedCreated = true, hierarchyCreated = false)))

    lazy val testProbe: TestProbe = TestProbe()

    class CustomActor extends MDMarketTypeActorDomainValidationBehaviour {
      this : Actor =>
      override protected val validations: List[EntityDomainValidation[MDMarketTypeState]] = List[EntityDomainValidation[MDMarketTypeState]](
        EntityDomainValidation[MDMarketTypeState](_.marketTypeAggregation.marketType.feedCreated,
          MarketType => EntityDomainValidationDescription.CREATE_FROM_FEED_MISSING(MarketType.marketTypeAggregation.marketType.identifier.asLong)),
        EntityDomainValidation[MDMarketTypeState](_.marketTypeAggregation.marketType.hierarchyCreated,
          MarketType => EntityDomainValidationDescription.HIERARCHY_NOTIFICATION_MISSING(MarketType.marketTypeAggregation.marketType.identifier.asLong))
      )

      override protected val marketTypeLinkValidations: List[EntityDomainValidation[EntityMetadata[MDMarketTypeLinkModel]]] = List()
      override def receive: Receive = productBehaviour(MDMarketTypeNoState)
    }

    lazy val createValidationResponseMock: (String, List[String], Map[(String, Int), Long]) => ValidationResponse = mock[(String, List[String], Map[(String, Int), Long]) => ValidationResponse]
    class CustomActorWithProvidedState(state: MDMarketTypeActorState) extends MDMarketTypeActorDomainValidationBehaviour {
      this: Actor=>

      override protected val validations: List[EntityDomainValidation[MDMarketTypeState]] = List[EntityDomainValidation[MDMarketTypeState]](
        EntityDomainValidation[MDMarketTypeState](_.marketTypeAggregation.marketType.feedCreated,
          E => EntityDomainValidationDescription.CREATE_FROM_FEED_MISSING(E.marketTypeAggregation.marketType.model.identifier.asLong)),
        EntityDomainValidation[MDMarketTypeState](_.marketTypeAggregation.marketType.hierarchyCreated,
          E => EntityDomainValidationDescription.HIERARCHY_NOTIFICATION_MISSING(E.marketTypeAggregation.marketType.model.identifier.asLong))
      )

      override def receive: Receive = productBehaviour(state)

      def proxyToGetValidationResponse(state: MDMarketTypeActorState): Option[(String, List[String])] = this.getValidationResponse(state)
      override protected def createValidationResponse(entityIdentifier: String, result: List[String], metadata: Map[(String, Int), Long]) = createValidationResponseMock(entityIdentifier,result, metadata)
    }

    lazy val victim: ActorRef = createActor

    lazy val victimWithValidState: ActorRef = createActorWithValidState

    private def createActor: ActorRef = {
      childActorOf(Props(new CustomActorWithProvidedState(MDMarketTypeNoState)
        with Actor), name = persistenceId)
    }

    private def createActorWithValidState: ActorRef = {
      childActorOf(Props(new CustomActorWithProvidedState(validState)
        with Actor), name = persistenceIdV2)
    }
  }

  "ValidationBehaviour" should "run validations after receiving a ValidateState command with a NO_STATE" in new MDMarketTypeActorDomainValidationBehaviourFixture {
    val command: ValidateState = ValidateState(persistenceId)

    testProbe.send(victim, command)

    testProbe.expectMsg(ValidationResponse("No State to validate",persistenceId,None,None,None,List.empty))
  }

  it should "run validations after receiving a ValidateState command with a Valid state" in new MDMarketTypeActorDomainValidationBehaviourFixture {
    val command: ValidateState = ValidateState(persistenceIdV2)
    val entityId: String = validState.marketTypeAggregation.marketType.identifier.asLong
    val topic: Option[String] = validState.metadata.headOption.map { case ((t, _), _) => t }
    val partition: Option[Int] = validState.metadata.headOption.map { case ((_, p), _) => p }
    val offset: Option[Long] = validState.metadata.headOption.map { case ((_, _), o) => o }
    val errors: Seq[Nothing] = List.empty
    val expectedValidationResponse: ValidationResponse = ValidationResponse("Validation passed",entityId,topic,partition,offset,errors)
    when(createValidationResponseMock.apply(any,any,any)).thenReturn(expectedValidationResponse)

    testProbe.send(victimWithValidState, command)

    testProbe.expectMsg(expectedValidationResponse)
  }

  it should "get validations from a validated state" in new MDMarketTypeActorDomainValidationBehaviourFixture {
    lazy val victim1 = new CustomActorWithProvidedState(MDMarketTypeNoState) with Actor
    lazy val actorVictim: ActorRef = childActorOf(Props(victim1), name = persistenceId)
    val command: ValidateState = ValidateState(persistenceId)
    lazy val expectedId: String = validState.marketTypeAggregation.marketType.identifier.asLong
    lazy val expectedErrors: Seq[Nothing] = Seq.empty
    when(createValidationResponseMock.apply(any,any,any)).thenReturn(ValidationResponse("","",None,None,None,expectedErrors))

    testProbe.sendSync(actorVictim, command)
    val result: Option[(String, List[String])] = victim1.proxyToGetValidationResponse(validState)
    verify(createValidationResponseMock, times(1)).apply(any,any,any)
    result.map {
      case (str, errors) =>
        str shouldBe expectedId
        errors shouldBe expectedErrors
    }
  }
  it should "get validations from a empty state" in new MDMarketTypeActorDomainValidationBehaviourFixture {
    lazy val victim1 = new CustomActorWithProvidedState(MDMarketTypeNoState) with Actor
    lazy val actorVictim: ActorRef = childActorOf(Props(victim1), name = persistenceId)
    val command: ValidateState = ValidateState(persistenceId)
    lazy val expectedId: String = validState.marketTypeAggregation.marketType.identifier.asLong
    lazy val expectedErrors: Seq[Nothing] = Seq.empty
    when(createValidationResponseMock.apply(any,any,any)).thenReturn(ValidationResponse("","",None,None,None,expectedErrors))

    testProbe.sendSync(actorVictim, command)

    val result: Option[(String, List[String])] = victim1.proxyToGetValidationResponse(MDMarketTypeNoState)
    verifyNoInteractions(createValidationResponseMock)
    result.map {
      case (str, errors) =>
        str shouldBe expectedId
        errors shouldBe expectedErrors
    }
  }
}
