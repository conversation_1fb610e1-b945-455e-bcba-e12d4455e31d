package com.flutter.pcsa.md.actor.markettype.domain.dto.generators

import com.flutter.pcsa.common.datatypes.gbpid.{GbpId, GbpIdHierarchyLevel}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import com.flutter.pcsa.common.datatypes.{EntityIdentifiers, GenPlatform, Platform}
import com.flutter.pcsa.md.actor.markettype.domain.dto.MDMarketTypeDTO

class MDMarketTypeDTOSpec extends AnyFlatSpec with Matchers with GenPlatform {

  "MDMarketTypeDTO.empty" should "return an empty MDMarketTypeDTO" in {
    val emptyDTO = MDMarketTypeDTO.empty
    emptyDTO.identifier shouldBe GbpId.empty
    emptyDTO.name shouldBe None
    emptyDTO.marketSort shouldBe None
    emptyDTO.subclassId shouldBe None
    emptyDTO.entityIdentifiers shouldBe EntityIdentifiers(Map.empty)
  }

  "MDMarketTypeDTO.isEmpty" should "return true for an empty MDMarketTypeDTO" in {
    val emptyDTO = MDMarketTypeDTO.empty
    emptyDTO.isEmpty shouldBe true
  }

  it should "return false for a non-empty MDMarketTypeDTO" in {
    val nonEmptyDTO = MDMarketTypeDTO(
      identifier = genGbpIdLongFormatWithHL("Market").sample.get,
      name = Some("Name"),
      marketSort = Some("sort"),
      subclassId = genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Subclass).sample,
      entityIdentifiers = EntityIdentifiers(Map(Platform.Gbp -> "value"))
    )
    nonEmptyDTO.isEmpty shouldBe false
  }

}
