package com.flutter.pcsa.md.serialization

import com.flutter.pcsa.md.actor.subclass.generators.{GenCommands, GenEvents}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.scalacheck.ScalaCheckPropertyChecks
import com.flutter.pcsa.md.actor.subclass.state.generators.GenMDSubclassState

import java.io.NotSerializableException

class MDSubclassProtobufSerializerSpec extends AnyFlatSpec with Matchers with ScalaCheckPropertyChecks with GenEvents with GenMDSubclassState{

  it should "serialize and deserialize for MDSubclassFeedCommand" in new MDSubclassProtobufSerializerFixture {
    forAll(genMDSubclassFeedCommand) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for MDSubclassFeedCommandAppliedEvent" in new MDSubclassProtobufSerializerFixture {
    forAll(genMDSubclassFeedCommandApplied) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for MDSubclassState" in new MDSubclassProtobufSerializerFixture {
    forAll(genMDSubclassState) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "fail serializing non Proto type" in new MDSubclassProtobufSerializerFixture {
    assertThrows[NotSerializableException] {
      serializer.toBinary(NonSerializableProto("testing"))
    }
  }

}

trait MDSubclassProtobufSerializerFixture extends GenCommands  {
  val serializer = new MDSubclassProtobufSerializer()

  case class NonSerializableProto(data: String)
}
