package com.flutter.pcsa.md.adapter.gth.convert

import cats.data.Ior
import com.flutter.adapter.{InboundKafkaMetadata, InboundMetadata}
import com.flutter.infra.kafka.streams.kafka.{KafkaMessageInterpreterException, KafkaMessageValidationFailures}
import com.flutter.pcsa.common.datatypes.gbpid.{GbpId, GbpIdHierarchyLevel}
import com.flutter.pcsa.md.actor.subclass.MDSubclassActor.MDSubclassFeedCommand
import com.flutter.pcsa.md.actor.subclass.domain.dto.MDSubclassDTO
import com.flutter.pcsa.md.actor.subclass.domain.dto.generators.GenMDSubclassDTO
import com.flutter.pcsa.md.adapter.generators.GenGTHInstructions
import com.flutter.pcsa.md.adapter.gth.GTHMessage
import com.flutter.pcsa.md.common.actor.domain.dto.MDFeedActionDTO
import com.flutter.retryableactor.GenActorMetadata
import com.flutter.retryableactor.metadata.Metadata.KafkaMetadata
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.mockito.MockitoSugar.mock
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.must.Matchers
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

class MDGTH2SubclassFeedCommandSpec extends AnyFlatSpecLike with Matchers {

  trait MDGTH2SubclassFeedCommandFixture extends MDGTH2FeedCommand with MDGTH2SubclassFeedCommand with GenActorMetadata with GenGTHInstructions with GenMDSubclassDTO {

    lazy val getFeedActionDTOIorMock: InboundMetadata[GTHMessage] => Ior[KafkaMessageValidationFailures, MDFeedActionDTO.Value] = mock[InboundMetadata[GTHMessage] => Ior[KafkaMessageValidationFailures, MDFeedActionDTO.Value]]
    when(getFeedActionDTOIorMock.apply(any[InboundMetadata[GTHMessage]])).thenReturn(Ior.right(MDFeedActionDTO.UPDATE))
    lazy val toSubclassDTOMock: GTHSubclassInstruction => Ior[KafkaMessageValidationFailures, MDSubclassDTO] = mock[GTHSubclassInstruction => Ior[KafkaMessageValidationFailures, MDSubclassDTO]]
    when(toSubclassDTOMock.apply(any[GTHSubclassInstruction])).thenReturn(Ior.right(genMDSubclassDTO.sample.get))

    override def getFeedActionDTOIor(inbound: InboundMetadata[GTHMessage]): Ior[KafkaMessageValidationFailures, MDFeedActionDTO.Value] = getFeedActionDTOIorMock(inbound)

    override def toSubclassDTO(gthSubclass: GTHSubclassInstruction): Ior[KafkaMessageValidationFailures, MDSubclassDTO] = toSubclassDTOMock(gthSubclass)

    lazy val inboundMock: InboundMetadata[GTHMessage] = mock[InboundMetadata[GTHMessage]]
    lazy val gthMessageMock: GTHMessage = mock[GTHMessage]
    lazy val gthSubclassInstruction: GTHSubclassInstruction = genGTHSubclassInstruction.sample.get
    when(gthMessageMock.payload).thenReturn(gthSubclassInstruction)
    when(gthMessageMock.headers).thenReturn(Map.empty)
    lazy val inboundMetadataMock: InboundKafkaMetadata = genInboundKafkaMetadata.sample.get
    when(inboundMock.metadata).thenReturn(inboundMetadataMock)
    when(inboundMock.instruction).thenReturn(gthMessageMock)

    val victim: (InboundMetadata[GTHMessage], GTHSubclassInstruction) => Ior[KafkaMessageValidationFailures, MDSubclassFeedCommand] = innerSubclassConverter
  }


  it should "fail when getFeedActionDTOIor returns KafkaMessageValidationFailure and everything else works as intended" in new MDGTH2SubclassFeedCommandFixture {
    when(getFeedActionDTOIorMock.apply(any[InboundMetadata[GTHMessage]])).thenReturn(Ior.left(KafkaMessageInterpreterException(List("error"))))

    val result: Ior[KafkaMessageValidationFailures, MDSubclassFeedCommand] = victim(inboundMock, gthSubclassInstruction)

    result.isLeft shouldBe true
  }

  it should "fail when toSubclassDTO returns KafkaMessageValidationFailure and everything else works as intended" in new MDGTH2SubclassFeedCommandFixture {
    when(toSubclassDTOMock.apply(any[GTHSubclassInstruction])).thenReturn(Ior.left(KafkaMessageInterpreterException(List("error"))))

    val result: Ior[KafkaMessageValidationFailures, MDSubclassFeedCommand] = victim(inboundMock, gthSubclassInstruction)

    result.isLeft shouldBe true
  }

  it should "correctly convert to ActorCommand when everything works correctly" in new MDGTH2SubclassFeedCommandFixture {
    val providerId = 1
    val gbpIdentifierString = s"urn:sbk:pc:sbc:gpd:$providerId"
    val gbpIdentifier: GbpId = GbpId(gbpIdentifierString)
    val mdFeedActionDTO: MDFeedActionDTO.Value = MDFeedActionDTO.UPDATE
    val generatedDTO: MDSubclassDTO = genMDSubclassDTO.sample.get.copy(identifier = gbpIdentifier)

    when(getFeedActionDTOIorMock.apply(inboundMock)).thenReturn(Ior.right(mdFeedActionDTO))
    when(toSubclassDTOMock.apply(any[GTHSubclassInstruction])).thenReturn(Ior.right(generatedDTO))

    val result: Ior[KafkaMessageValidationFailures, MDSubclassFeedCommand] = victim(inboundMock, gthSubclassInstruction)

    result.isRight shouldBe true
    val right: MDSubclassFeedCommand = result.right.get
    right.subclass shouldBe generatedDTO
    right.action shouldBe mdFeedActionDTO
    right.metadata shouldBe KafkaMetadata(
      inboundMock.metadata.topic, inboundMock.metadata.partition, inboundMock.metadata.offset
    )
    right.headers shouldBe inboundMock.instruction.headers
  }
}
