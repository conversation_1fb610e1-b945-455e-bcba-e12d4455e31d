package com.flutter.pcsa.md.adapter.gth.convert

import com.flutter.pcsa.md.adapter.generators.GenGTHInstructions
import com.flutter.pcsa.md.adapter.gth.GTHMessage
import com.flutter.retryableactor.GenActorMetadata
import cats.data.Ior
import com.flutter.adapter.{InboundKafkaMetadata, InboundMetadata}
import com.flutter.infra.kafka.streams.kafka.{KafkaMessageInterpreterException, KafkaMessageValidationFailures}
import com.flutter.pcsa.common.datatypes.gbpid.{GbpId, GbpIdHierarchyLevel}
import com.flutter.pcsa.md.actor.superclass.MDSuperclassActor.MDSuperclassFeedCommand
import com.flutter.pcsa.md.actor.superclass.domain.dto.MDSuperclassDTO
import com.flutter.pcsa.md.actor.superclass.domain.dto.generators.GenMDSuperclassDTO
import com.flutter.pcsa.md.common.actor.domain.dto.MDFeedActionDTO
import com.flutter.retryableactor.metadata.Metadata.KafkaMetadata
import org.mockito.ArgumentMatchers.any
import org.mockito.MockitoSugar.{mock, when}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.must.Matchers
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

class MDGTH2SuperclassFeedCommandSpec extends AnyFlatSpecLike with Matchers {

  trait MDGTH2SuperclassFeedCommandFixture extends MDGTH2FeedCommand with MDGTH2SuperclassFeedCommand with GenActorMetadata with GenGTHInstructions with GenMDSuperclassDTO {
    
    lazy val getFeedActionDTOIorMock: InboundMetadata[GTHMessage] => Ior[KafkaMessageValidationFailures, MDFeedActionDTO.Value] = mock[InboundMetadata[GTHMessage] => Ior[KafkaMessageValidationFailures, MDFeedActionDTO.Value]]
    when(getFeedActionDTOIorMock.apply(any[InboundMetadata[GTHMessage]])).thenReturn(Ior.right(MDFeedActionDTO.UPDATE))
    lazy val toSuperclassDTOMock: GTHSuperclassInstruction => Ior[KafkaMessageValidationFailures, MDSuperclassDTO] = mock[GTHSuperclassInstruction => Ior[KafkaMessageValidationFailures, MDSuperclassDTO]]
    when(toSuperclassDTOMock.apply(any[GTHSuperclassInstruction])).thenReturn(Ior.right(genMDSuperclassDTO.sample.get))
    
    override def getFeedActionDTOIor(inbound: InboundMetadata[GTHMessage]): Ior[KafkaMessageValidationFailures, MDFeedActionDTO.Value] = getFeedActionDTOIorMock(inbound)
    override def toSuperclassDTO(gthSuperclass: GTHSuperclassInstruction): Ior[KafkaMessageValidationFailures, MDSuperclassDTO] = toSuperclassDTOMock(gthSuperclass)

    lazy val inboundMock: InboundMetadata[GTHMessage] = mock[InboundMetadata[GTHMessage]]
    lazy val gthMessageMock: GTHMessage = mock[GTHMessage]
    lazy val gthSuperclassInstruction: GTHSuperclassInstruction = genGTHSuperclassInstruction.sample.get
    when(gthMessageMock.payload).thenReturn(gthSuperclassInstruction)
    when(gthMessageMock.headers).thenReturn(Map.empty)
    lazy val inboundMetadataMock: InboundKafkaMetadata = genInboundKafkaMetadata.sample.get
    when(inboundMock.metadata).thenReturn(inboundMetadataMock)
    when(inboundMock.instruction).thenReturn(gthMessageMock)

    val victim: (InboundMetadata[GTHMessage], GTHSuperclassInstruction) => Ior[KafkaMessageValidationFailures, MDSuperclassFeedCommand] = innerSuperclassConverter
  }

  it should "fail when getFeedActionDTOIor returns KafkaMessageValidationFailure and everything else works as intended" in new MDGTH2SuperclassFeedCommandFixture {
    when(getFeedActionDTOIorMock.apply(any[InboundMetadata[GTHMessage]])).thenReturn(Ior.left(KafkaMessageInterpreterException(List("error"))))

    val result: Ior[KafkaMessageValidationFailures, MDSuperclassFeedCommand] = victim(inboundMock, gthSuperclassInstruction)

    result.isLeft shouldBe true
  }

  it should "fail when toSuperclassDTO returns KafkaMessageValidationFailure and everything else works as intended" in new MDGTH2SuperclassFeedCommandFixture {
    when(toSuperclassDTOMock.apply(any[GTHSuperclassInstruction])).thenReturn(Ior.left(KafkaMessageInterpreterException(List("error"))))

    val result: Ior[KafkaMessageValidationFailures, MDSuperclassFeedCommand] = victim(inboundMock, gthSuperclassInstruction)

    result.isLeft shouldBe true
  }

  it should "correctly convert to ActorCommand when everything works correctly" in new MDGTH2SuperclassFeedCommandFixture {
    val providerId = 1
    val gbpIdentifierString = s"urn:sbk:pc:spc:gpd:$providerId"
    val gbpIdentifier: GbpId = GbpId(gbpIdentifierString)
    val mdFeedActionDTO: MDFeedActionDTO.Value = MDFeedActionDTO.UPDATE
    val generatedDTO: MDSuperclassDTO = genMDSuperclassDTO.sample.get.copy(identifier = gbpIdentifier)
    
    when(getFeedActionDTOIorMock.apply(inboundMock)).thenReturn(Ior.right(mdFeedActionDTO))
    when(toSuperclassDTOMock.apply(any[GTHSuperclassInstruction])).thenReturn(Ior.right(generatedDTO))

    val result: Ior[KafkaMessageValidationFailures, MDSuperclassFeedCommand] = victim(inboundMock, gthSuperclassInstruction)

    result.isRight shouldBe true
    val right: MDSuperclassFeedCommand = result.right.get
    right.superclass shouldBe generatedDTO
    right.action shouldBe MDFeedActionDTO.UPDATE
    right.metadata shouldBe KafkaMetadata(
      inboundMock.metadata.topic, inboundMock.metadata.partition, inboundMock.metadata.offset
    )
    right.headers shouldBe inboundMock.instruction.headers
  }
}
