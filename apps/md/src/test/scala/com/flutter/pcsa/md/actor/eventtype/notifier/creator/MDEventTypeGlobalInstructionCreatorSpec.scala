package com.flutter.pcsa.md.actor.eventtype.notifier.creator


import com.flutter.pcsa.common.datatypes.Platform
import com.flutter.pcsa.common.state.model.datatypes.GenField
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.md.actor.eventtype.MDEventTypeActor
import com.flutter.pcsa.md.actor.eventtype.MDEventTypeActor.MDRefreshActorEventType
import com.flutter.pcsa.md.actor.eventtype.domain.state.{MDEventTypeHierarchyModel, MDEventTypeModel}
import com.flutter.pcsa.md.actor.eventtype.domain.state.generators.GenMDEventTypeAggregationModel
import com.flutter.pcsa.md.actor.eventtype.generators.GenEvents
import com.flutter.pcsa.md.actor.eventtype.generators.databuilders.EventTypeOnlyFixture
import com.flutter.pcsa.md.common.actor.domain.dto.MDFeedActionDTO
import com.flutter.product.catalogue.global.market.GlobalCatalogueInstruction.Action
import com.flutter.product.catalogue.global.market.EventType
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper
import org.scalatest.prop.{TableDrivenPropertyChecks, TableFor2, TableFor3, TableFor5}
import com.flutter.pcsa.md.common.actor.notifier.converters.implicits.modelConverters.asStringField

class MDEventTypeGlobalInstructionCreatorSpec extends AnyFlatSpecLike with TableDrivenPropertyChecks {

  trait MDEventTypeGlobalInstructionCreatorFixtures extends EventTypeOnlyFixture with GenEvents with GenMDEventTypeAggregationModel with GenField {

    lazy val feedEventUpdate: MDEventTypeActor.MDEventTypeFeedCommandApplied = genMDEventTypeFeedCommandApplied(genAction = MDFeedActionDTO.UPDATE).sample.get
    lazy val feedEventCreate: MDEventTypeActor.MDEventTypeFeedCommandApplied = genMDEventTypeFeedCommandApplied(genAction = MDFeedActionDTO.CREATE).sample.get
    lazy val feedEventRefresh: MDEventTypeActor.MDEventTypeFeedCommandApplied = genMDEventTypeFeedCommandApplied(genAction = MDFeedActionDTO.REFRESH).sample.get

    lazy val EventTypeHierarchy: MDEventTypeHierarchyModel = genMDEventTypeHierarchyModel.sample.get

    def instructionBuilder(metadata: EntityMetadata[MDEventTypeModel]): EventType = {
      EventType(
        id = metadata.identifier.asLong,
        name = Some(asStringField(metadata.model.name)),
        subclassId = EventTypeHierarchy.subclass.identifier.asLong,
        sportexCompetitionId = metadata.model.entityIdentifiers.identifiers.getOrElse(Platform.Sportex, ""),
        sportexCompetitionEntryId = metadata.model.entityIdentifiers.identifiers.getOrElse(Platform.Sportex, "")
      )
    }
  }

  trait MDEventTypeGlobalInstructionCreatorFixture extends MDEventTypeGlobalInstructionCreatorFixtures {
    lazy val victim = new MDEventTypeGlobalInstructionCreator()
  }

  new MDEventTypeGlobalInstructionCreatorFixture {
    val table: TableFor5[String, Option[EntityMetadata[MDEventTypeModel]], EntityMetadata[MDEventTypeModel], MDEventTypeActor.MDEventTypeActorEvent, Action.Recognized] = Table(
      ("description", "previousState", "currentState", "event", "expectedAction"),
      // TODO review with GBP
      //when it is invalid and turns valid it is always a create
      //      (
      //        "set action to create when there wasn't previous state and new state is valid when feed event is an update",
      //        None,
      //        validEventType,
      //        feedEventUpdate,
      //        Action.CREATE
      //      )
      (
        "GIVEN there is no previous state WHEN feed event is CREATE and new state is valid THEN set action to CREATE",
        None,
        validEventType,
        feedEventCreate,
        Action.CREATE
      ),
      (
        "GIVEN there is no previous state WHEN feed event is REFRESH and new state is valid THEN set action to CREATE",
        None,
        validEventType,
        feedEventRefresh,
        Action.CREATE
      ),
      (
        "GIVEN previous state is invalid WHEN feed event is UPDATE and new state is valid THEN set action to CREATE",
        Some(invalidEventType),
        validEventType,
        feedEventUpdate,
        Action.CREATE
      ),
      (
        "GIVEN previous state is invalid WHEN feed event is CREATE and new state is valid THEN set action to CREATE",
        Some(invalidEventType),
        validEventType,
        feedEventCreate,
        Action.CREATE
      ),
      (
        "GIVEN previous state is invalid WHEN feed event is REFRESH and new state is valid THEN set action to CREATE",
        Some(invalidEventType),
        validEventType,
        feedEventRefresh,
        Action.CREATE
      ),
      (
        "GIVEN previous state is valid WHEN feed event is REFRESH and new state is valid THEN set action to REFRESH",
        Some(validEventType),
        createSameEventTypeManipulated(validEventType),
        feedEventRefresh,
        Action.REFRESH
      ),
      (
        "GIVEN previous state is valid WHEN refresh actor event and new state is valid THEN set action to REFRESH",
        Some(validEventType),
        createSameEventTypeManipulated(validEventType),
        MDRefreshActorEventType(),
        Action.REFRESH
      )
    )

    forAll(table)((description, previousState, currentState, event, expectedAction) => {

      it should description in {
        val result = victim.createInstruction(previousState, currentState, EventTypeHierarchy, event)

        result.get.action shouldBe expectedAction
      }
    })

  }

  trait MDEventTypeGlobalInstructionCreatorFixtureForSnapshot extends MDEventTypeGlobalInstructionCreatorFixture {
    lazy val currentState: EntityMetadata[MDEventTypeModel] = createSameEventTypeManipulated(validEventType)
  }
  
  behavior.of("When current state is valid and is a refresh")

  new MDEventTypeGlobalInstructionCreatorFixtureForSnapshot {

    val table: TableFor2[String, Option[EntityMetadata[MDEventTypeModel]]] = Table(
      ("description", "previousState"),
      ("ignore a previous valid state and calculate delta based on current state only as a snapshot", Some(validEventType)),
      ("ignore a previous invalid state and calculate delta based on current state only as a snapshot", Some(invalidEventType)),
      ("calculate delta based on current state only as a snapshot when no previous state", None)
    )

    forAll(table)((description, previousState) => {

      it should description in {
        val result = victim.createInstruction(previousState, currentState, EventTypeHierarchy, feedEventRefresh)

        val globalRiskInstruction = result.get
        globalRiskInstruction.entity.isEventType shouldBe true
        globalRiskInstruction.getEventType shouldBe instructionBuilder(currentState)
      }
    })
  }
  
  new MDEventTypeGlobalInstructionCreatorFixtureForSnapshot {

    val events: TableFor2[String, MDEventTypeActor.MDEventTypeFeedCommandApplied] = Table(
      ("description", "event"),
      ("Invalid to valid transitions when feed create", feedEventCreate),
      ("Invalid to valid transitions when feed update", feedEventUpdate),
      ("Invalid to valid transitions when feed refresh", feedEventRefresh)
    )

    val table: TableFor2[String, Option[EntityMetadata[MDEventTypeModel]]] = Table(
      ("description", "previousState"),
      ("ignore a previous invalid state and calculate delta based on current state only as a snapshot", Some(invalidEventType)),
      ("calculate delta based on current state only as a snapshot when no previous state", None)
    )

    forAll(events) { (behaviourDescription, event) =>
      behavior.of(behaviourDescription)

      forAll(table)((description, previousState) => {

        it should description in {
          val result = victim.createInstruction(previousState, currentState, EventTypeHierarchy, event)

          val globalRiskInstruction = result.get
          globalRiskInstruction.entity.isEventType shouldBe true
          globalRiskInstruction.getEventType shouldBe instructionBuilder(currentState)

        }
      })
    }
  }

  new MDEventTypeGlobalInstructionCreatorFixtureForSnapshot {

    val events: TableFor2[String, MDEventTypeActor.MDEventTypeFeedCommandApplied] = Table(
      ("description", "event"),
      ("Invalid to Invalid transitions when feed create", feedEventCreate),
      ("Invalid to Invalid transitions when feed update", feedEventUpdate),
      ("Invalid to Invalid transitions when feed refresh", feedEventRefresh)
    )

    val table: TableFor3[String, Option[EntityMetadata[MDEventTypeModel]], EntityMetadata[MDEventTypeModel]] = Table(
      ("description", "previousState", "currentState"),
      (
        "Not publish instruction when previous state is invalid and current state is invalid",
        Some(invalidEventType),
        createSameEventTypeManipulated(invalidEventType)
      ),
      ("Not publish instruction when previous state does not exists and current state is invalid", None, createSameEventTypeManipulated(invalidEventType)),
      (
        "Not publish instruction when previous state is valid and current state is invalid",
        Some(validEventType),
        createSameEventTypeManipulated(invalidEventType)
      )
    )

    forAll(events) { (behaviourDescription, event) =>
      behavior.of(behaviourDescription)

      forAll(table)((description, previousState, currentState) => {

        it should description in {
          val result = victim.createInstruction(previousState, currentState, EventTypeHierarchy, event)

          result shouldBe None
        }
      })
    }
  }

  new MDEventTypeGlobalInstructionCreatorFixture {

    val events: TableFor2[String, MDEventTypeActor.MDEventTypeFeedCommandApplied] = Table(
      ("description", "event"),
      ("Not publish instruction when previous and current state are valid & contains same data when create event was received", feedEventCreate),
      ("Not publish instruction when previous and current state are valid & contains same data when update event was received", feedEventUpdate)
    )

    forAll(events)((description, event) => {
      it should description in {
        val result = victim.createInstruction(Some(validEventType), validEventType, EventTypeHierarchy, event)

        result shouldBe None
      }
    })
  }

}
