package com.flutter.pcsa.md.actor.markettype.generators

import com.flutter.headers.{ROOT_SOURCE_TIMESTAMP, SOURCE_TIMESTAMP}
import com.flutter.pcsa.common.GenCommon
import com.flutter.pcsa.common.datatypes.gbpid.GbpIdHierarchyLevel
import com.flutter.pcsa.md.MD
import com.flutter.pcsa.md.actor.eventtype.domain.subscription.GenMDEventTypeUpdateNotification
import com.flutter.pcsa.md.actor.markettype.MDMarketTypeActor.{MDInheritanceUpdateMarketTypeLink, MDMarketTypeFeedCommand, MDMarketTypeLinkFeedCommand, MDMarketTypeLinkInheritanceUpdated, MDSubscribeMarketTypeLink, MDUnsubscribeMarketTypeLink}
import com.flutter.pcsa.md.actor.markettype.domain.dto.generators.{GenMDMarketTypeDTO, GenMDMarketTypeHierarchyDTO, GenMDMarketTypeLinkDTO}
import com.flutter.pcsa.md.common.domain.dto.generators.GenMDFeedActionDTO
import com.flutter.retryableactor.GenActorMetadata
import org.scalacheck.Gen

trait GenCommands
  extends GenCommon
    with GenMDFeedActionDTO
    with GenMDMarketTypeDTO
    with GenActorMetadata
    with GenMDMarketTypeHierarchyDTO
    with GenMDMarketTypeLinkDTO
    with GenMDEventTypeUpdateNotification {

  lazy val genMDMarketTypeFeedCommand: Gen[MDMarketTypeFeedCommand] = for {
    metadata <- genKafkaMetadata
    headers <- genMap(genString, genString)
    marketType <- genMDMarketTypeDTO
    action <- genMDFeedActionDTO
    marketTypeHierarchy <- genMDMarketTypeHierarchyDTO
    rootSourceTimestamp <- genLong
    sourceTimestamp <- genLong
  } yield {
    MDMarketTypeFeedCommand(
      metadata = metadata,
      headers = headers ++
        Map(
          ROOT_SOURCE_TIMESTAMP -> rootSourceTimestamp.toString,
          SOURCE_TIMESTAMP(MD.ServiceName) -> sourceTimestamp.toString
        ),
      marketType = marketType,
      action = action,
      marketTypeHierarchy = marketTypeHierarchy
    )
  }

  lazy val genMDMarketTypeLinkFeedCommand: Gen[MDMarketTypeLinkFeedCommand] = for {
    metadata <- genKafkaMetadata
    headers <- genMap(genString, genString)
    marketTypeLink <- genMDMarketTypeLinkDTO
    action <- genMDFeedActionDTO
    marketTypeHierarchy <- genMDMarketTypeHierarchyDTO
    rootSourceTimestamp <- genLong
    sourceTimestamp <- genLong
  } yield {
    MDMarketTypeLinkFeedCommand(
      metadata = metadata,
      headers = headers ++
        Map(
          ROOT_SOURCE_TIMESTAMP -> rootSourceTimestamp.toString,
          SOURCE_TIMESTAMP(MD.ServiceName) -> sourceTimestamp.toString
        ),
      marketTypeLink = marketTypeLink,
      action = action,
      marketTypeLinkHierarchy = marketTypeHierarchy
    )
  }

  lazy val genMDInheritanceUpdateMarketTypeLink: Gen[MDInheritanceUpdateMarketTypeLink] = for {
    metadata <- genKafkaMetadata
    identifier <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    headers <- genMap(genString, genString)
    notification <- genMDEventTypeUpdateNotification
  } yield {
    MDInheritanceUpdateMarketTypeLink(
      metadata = metadata,
      identifier = identifier.asLong,
      notification = notification,
      headers = headers
    )
  }

  lazy val genMDSubscribeMarketTypeLink: Gen[MDSubscribeMarketTypeLink] = for {
    marketId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Market)
    marketTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketType)
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    headers <- genMap(genString, genString)
    metadata <- genKafkaMetadata
  } yield {
    MDSubscribeMarketTypeLink(
      marketId = marketId.toString,
      marketTypeId = marketTypeId.toString,
      marketTypeLinkId = marketTypeLinkId.toString,
      headers = headers,
      metadata = metadata
    )
  }

  lazy val genMDUnsubscribeMarketTypeLink: Gen[MDUnsubscribeMarketTypeLink] = for {
    marketId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Market)
    marketTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketType)
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    headers <- genMap(genString, genString)
    metadata <- genKafkaMetadata
  } yield {
    MDUnsubscribeMarketTypeLink(
      marketId = marketId.toString,
      marketTypeId = marketTypeId.toString,
      marketTypeLinkId = marketTypeLinkId.toString,
      headers = headers,
      metadata = metadata
    )
  }

}
