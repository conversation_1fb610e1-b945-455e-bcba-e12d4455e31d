package com.flutter.pcsa.md.common.actor.notifier.converters

import com.flutter.pcsa.common.state.model.datatypes.{GenOverridableAndInheritableField, GenOverridableFeedField}
import com.flutter.product.catalogue.market.domain.common.TimestampField
import org.scalatest.flatspec.{AnyFlatSpec, AnyFlatSpecLike}
import org.scalatest.matchers.must.Matchers
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper
import org.scalatestplus.scalacheck.ScalaCheckDrivenPropertyChecks
import java.time.LocalDateTime

class OverridableFeedField2InstructionOpsSpec extends AnyFlatSpec
  with ScalaCheckDrivenPropertyChecks
  with Matchers
  with GenOverridableFeedField {

  object Ops extends OverridableFeedField2InstructionOps

  "asStringField" should "convert OverridableFeedField[String] to StringField" in {
    val field = genOverridableFeedStringField.sample.get
    val result = Ops.asStringField(field)

    result.base("ALL").value.get shouldBe field.value.get
    field.overrides.foreach {
      case (k, v) => result.instances(k).value shouldBe v.value
    }
  }

  "asIntField" should "convert OverridableFeedField[Int] to IntField" in {
    val field = genOverridableFeedIntField.sample.get
    val result = Ops.asIntField(field)

    result.base("ALL").value.get shouldBe field.value.get
    field.overrides.foreach {
      case (k, v) => result.instances(k).value shouldBe v.value
    }
  }

  "asDoubleField" should "convert OverridableFeedField[Double] to DoubleField" in {
    val field = genOverridableFeedDoubleField.sample.get
    val result = Ops.asDoubleField(field)

    result.base("ALL").value.get shouldBe field.value.get
    field.overrides.foreach {
      case (k, v) => result.instances(k).value shouldBe v.value
    }
  }

  "asLongField" should "convert OverridableFeedField[Long] to LongField" in {
    val field = genOverridableFeedLongField.sample.get
    val result = Ops.asLongField(field)

    result.base("ALL").value.get shouldBe field.value.get
    field.overrides.foreach {
      case (k, v) => result.instances(k).value shouldBe v.value
    }
  }

  "asBoolField" should "convert OverridableFeedField[Boolean] to BoolField" in {
    val field = genOverridableFeedBooleanField.sample.get
    val result = Ops.asBoolField(field)

    result.base("ALL").value.get shouldBe field.value.get
    field.overrides.foreach {
      case (k, v) => result.instances(k).value shouldBe v.value
    }
  }

  "asTimestampField" should "convert OverridableFeedField[LocalDateTime] to TimestampField" in {
    val field = genOverridableFeedLocalDateTimeField.sample.get
    val result = Ops.asTimestampField(field)

    // Verify base value conversion
    field.value.foreach { baseValue =>
      result.base("ALL").value.foreach { convertedTimestamp =>
        val expectedInstant = baseValue.toInstant(java.time.ZoneOffset.UTC)
        convertedTimestamp.seconds shouldBe expectedInstant.getEpochSecond
        convertedTimestamp.nanos shouldBe expectedInstant.getNano
      }
    }

    // Verify instance overrides conversion
    field.overrides.foreach {
      case (k, v) =>
        v.value.foreach { overrideValue =>
          result.instances(k).value.foreach { convertedTimestamp =>
            val expectedInstant = overrideValue.toInstant(java.time.ZoneOffset.UTC)
            convertedTimestamp.seconds shouldBe expectedInstant.getEpochSecond
            convertedTimestamp.nanos shouldBe expectedInstant.getNano
          }
        }
    }
  }
}
