package com.flutter.pcsa.md.infra.kafka.consumer

import cats.data.Ior
import com.flutter.InternalTypes.ActorResolver
import com.flutter.adapter.validator.Validator
import com.flutter.baseactor.BaseActor.{Accepted, ActorCommand}
import com.flutter.common.BaseAkkaSpec
import com.flutter.infra.kafka.consumer.ConsumerMetricsReporter
import com.flutter.infra.kafka.converter.Converter
import com.flutter.infra.kafka.streams.kafka.{KafkaMessageIgnoreException, KafkaMessageInterpreterException, KafkaMessageValidationFailures}
import com.flutter.retryableactor.metadata.Metadata.KafkaMetadata
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.common.serialization.Deserializer
import org.apache.pekko.actor.{ActorRef, ActorSystem}
import org.apache.pekko.kafka.ConsumerMessage.CommittableOffset
import org.apache.pekko.testkit.TestActor.KeepRunning
import org.apache.pekko.testkit.TestProbe
import org.mockito.ArgumentMatchers

import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.SpanSugar.convertIntToGrainOfTime

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.{Await, Future}
import scala.language.postfixOps
import scala.util.{Failure, Success}

class KafkaConsumerMultiProcessorInActorSpec
    extends BaseAkkaSpec(
      ActorSystem(
        "KafkaConsumerMultiProcessorInActorSpec",
        BaseAkkaSpec.LOCAL_CLUSTER_DEFAULT
          .withFallback(ConfigFactory.load())
      )
    ) with ScalaFutures {

  trait KafkaConsumerMultiProcessorInActorBuilder {

    case class DummyActorCommand(data: String) extends ActorCommand {
      override def shardingKey: String = "1"
      override def headers: Map[String, String] = Map.empty
      override val metadata: KafkaMetadata = KafkaMetadata.empty
    }

    lazy val streamName = "dummyMultiProcessorStream"

    lazy val converterMock: Converter[ConsumerRecord[String, String], List[DummyActorCommand]] = 
      mock[Converter[ConsumerRecord[String, String], List[DummyActorCommand]]]
    lazy val configMock = mock[Config]
    lazy val kafkaConfigMock = mock[Config]
    lazy val consumerMetricsReporterMock = mock[ConsumerMetricsReporter[ActorCommand]]
    lazy val validatorMock = mock[Validator[List[DummyActorCommand]]]

    lazy val kafkaStreamConfig = ConfigFactory.parseString(s"""
                                                             | {
                                                             |    send-to-actor-ask-timeout = 5.minutes
                                                             |    processing = {
                                                             |      partition-number = 2
                                                             |      buffer-size = 1
                                                             |    }
                                                             |    batch-offsets = {
                                                             |      batch = 1
                                                             |      batch-interval = 1.second
                                                             |    }
                                                             |    name = $streamName
                                                             | }
                                                             |""".stripMargin)

    // Mock config setup with proper Config mock that supports root() method
    val mockKafkaClientsConfig = ConfigFactory.parseString("""
      kafka-clients {
        group.id = "test-group"
        security.protocol = "PLAINTEXT"
      }
      enableConsumerOnBootstrap = true
    """)

    doReturn("test-group").when(configMock).getString("kafka-clients.group.id")
    doReturn(true).when(configMock).hasPath("enableConsumerOnBootstrap")
    doReturn(true).when(configMock).getBoolean("enableConsumerOnBootstrap")
    doReturn(true).when(configMock).hasPath(ArgumentMatchers.eq("stream-config"))
    doReturn(kafkaStreamConfig).when(configMock).getConfig(ArgumentMatchers.eq("stream-config"))
    
    // Mock kafka clients config
    doReturn(mockKafkaClientsConfig.getConfig("kafka-clients")).when(configMock).getConfig("kafka-clients")
    doReturn("PLAINTEXT").when(kafkaConfigMock).getString("security.protocol")

    val command1 = DummyActorCommand("Hello1")
    val command2 = DummyActorCommand("Hello2")
    val commands = List(command1, command2)
    val mockActorResolver = mock[ActorResolver[ActorCommand]]

    val consumer = KafkaConsumerMultiProcessorInActor[String, String, List[DummyActorCommand]](
      Set("test-topic"),
      mockKafkaClientsConfig.withFallback(ConfigFactory.parseString("""
        stream-config {
          send-to-actor-ask-timeout = 5.minutes
          processing = {
            partition-number = 2
            buffer-size = 1
          }
          batch-offsets = {
            batch = 1
            batch-interval = 1.second
          }
          name = "dummyMultiProcessorStream"
        }
      """)),
      mockActorResolver,
      converterMock,
      mock[Deserializer[String]],
      mock[Deserializer[String]],
      validatorMock,
      mock[((String, CommittableOffset, Ior[KafkaMessageValidationFailures, List[DummyActorCommand]])) => Int],
      consumerMetricsReporterMock
    )
  }

  def awaitResultWithSuccess(result: Future[Accepted]): Unit = {
    result.onComplete {
      case Success(_)  =>
      case Failure(ex) => fail(s"Failure to complete future, exception=$ex")
    }

    Await.ready(result, 200.milliseconds)
    result.isCompleted shouldBe true
  }

  behavior of "KafkaConsumerMultiProcessorInActor"

  it should "return successful Future when all commands pass validation" in new KafkaConsumerMultiProcessorInActorBuilder {

    Given("a validator that returns true for all individual commands")
    doReturn(true).when(validatorMock).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    And("actor resolvers that respond with Accepted for each command")
    val testProbe1 = TestProbe()
    val testProbe2 = TestProbe()
    
    testProbe1.setAutoPilot((sender: ActorRef, msg: Any) => {
      msg match {
        case message: DummyActorCommand if message == command1 =>
          sender ! Accepted(System.currentTimeMillis())
          KeepRunning
        case _ => fail(s"Unexpected message: $msg")
      }
    })
    
    testProbe2.setAutoPilot((sender: ActorRef, msg: Any) => {
      msg match {
        case message: DummyActorCommand if message == command2 =>
          sender ! Accepted(System.currentTimeMillis())
          KeepRunning
        case _ => fail(s"Unexpected message: $msg")
      }
    })

    doReturn(testProbe1.ref).when(mockActorResolver).apply(ArgumentMatchers.eq(command1))
    doReturn(testProbe2.ref).when(mockActorResolver).apply(ArgumentMatchers.eq(command2))

    doAnswer((cmd: ActorCommand, result: Future[Accepted], stream: String) => result)
      .when(consumerMetricsReporterMock).aroundValidProcessing(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.eq(streamName))

    When("processMessage is called with the commands")
    val result = consumer.processMessage(commands)

    Then("both actors should receive their respective commands")
    testProbe1.expectMsg(command1)
    testProbe2.expectMsg(command2)

    And("no additional messages should be sent to the actors")
    testProbe1.expectNoMessage()
    testProbe2.expectNoMessage()

    And("the result should be a successful Future")
    awaitResultWithSuccess(result)

    And("the validator should be called for each command")
    verify(validatorMock, times(2)).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    And("the actor resolver should be called for each command")
    verify(mockActorResolver).apply(ArgumentMatchers.eq(command1))
    verify(mockActorResolver).apply(ArgumentMatchers.eq(command2))

    And("metrics should be reported for each valid command")
    verify(consumerMetricsReporterMock).aroundValidProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.any(), ArgumentMatchers.eq(streamName))
    verify(consumerMetricsReporterMock).aroundValidProcessing(ArgumentMatchers.eq(command2), ArgumentMatchers.any(), ArgumentMatchers.eq(streamName))

    verifyNoMoreInteractions(validatorMock, mockActorResolver, consumerMetricsReporterMock)
  }

  it should "return successful Future when some commands fail validation" in new KafkaConsumerMultiProcessorInActorBuilder {

    Given("a validator that returns true for first command and false for second")
    // Setup validator to return true for command1 and false for command2
    doAnswer { (invocation: org.mockito.invocation.InvocationOnMock) =>
      val commands = invocation.getArgument[List[DummyActorCommand]](0)
      commands.head == command1
    }.when(validatorMock).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    And("an actor resolver that should only be called for the valid command")
    val testProbe = TestProbe()
    testProbe.setAutoPilot((sender: ActorRef, msg: Any) => {
      msg match {
        case message: DummyActorCommand if message == command1 =>
          sender ! Accepted(System.currentTimeMillis())
          KeepRunning
        case _ => fail(s"Unexpected message: $msg")
      }
    })

    doReturn(testProbe.ref).when(mockActorResolver).apply(ArgumentMatchers.eq(command1))

    doAnswer((cmd: ActorCommand, result: Future[Accepted], stream: String) => result)
      .when(consumerMetricsReporterMock).aroundValidProcessing(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.eq(streamName))

    When("processMessage is called with the commands")
    val result = consumer.processMessage(commands)

    Then("only the valid command should be sent to the actor")
    testProbe.expectMsg(command1)
    testProbe.expectNoMessage()

    And("the result should be a successful Future")
    awaitResultWithSuccess(result)

    And("the validator should be called for each command")
    verify(validatorMock, times(2)).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    And("the actor resolver should only be called for the valid command")
    verify(mockActorResolver).apply(ArgumentMatchers.eq(command1))

    And("metrics should be reported for the valid command")
    verify(consumerMetricsReporterMock).aroundValidProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.any(), ArgumentMatchers.eq(streamName))

    And("metrics should be reported for the invalid command")
    verify(consumerMetricsReporterMock).aroundInvalidProcessing(ArgumentMatchers.eq(command2), ArgumentMatchers.eq(streamName))

    verifyNoMoreInteractions(validatorMock, mockActorResolver, consumerMetricsReporterMock)
  }

  it should "return successful Future when all commands fail validation" in new KafkaConsumerMultiProcessorInActorBuilder {

    Given("a validator that returns false for all commands")
    doReturn(false).when(validatorMock).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    When("processMessage is called with the commands")
    val result = consumer.processMessage(commands)

    Then("the result should be a successful Future without sending any commands to actors")
    awaitResultWithSuccess(result)

    And("the validator should be called for each command")
    verify(validatorMock, times(2)).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    And("there should be no interactions with the actor resolver")
    verifyZeroInteractions(mockActorResolver)

    And("metrics should be reported for all invalid commands")
    verify(consumerMetricsReporterMock).aroundInvalidProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.eq(streamName))
    verify(consumerMetricsReporterMock).aroundInvalidProcessing(ArgumentMatchers.eq(command2), ArgumentMatchers.eq(streamName))

    verifyNoMoreInteractions(validatorMock, mockActorResolver, consumerMetricsReporterMock)
  }

  it should "return successful Future with correct timestamp when all commands fail validation" in new KafkaConsumerMultiProcessorInActorBuilder {

    Given("a validator that returns false for all commands")
    doReturn(false).when(validatorMock).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    val beforeTimeMillis = System.currentTimeMillis()

    When("processMessage is called and a result is awaited")
    val result: Future[Accepted] = consumer.processMessage(commands)

    whenReady(result, timeout(200.milliseconds)) { acceptedResult =>

      val afterTimeMillis = System.currentTimeMillis()

      Then("the returned Accepted should contain a timestamp within the valid time range")
      acceptedResult shouldBe an[Accepted]
      acceptedResult.timeStamp should be >= beforeTimeMillis
      acceptedResult.timeStamp should be <= afterTimeMillis
    }

    And("the validator should be called for each command")
    verify(validatorMock, times(2)).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    And("metrics should be reported for all invalid commands")
    verify(consumerMetricsReporterMock).aroundInvalidProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.eq(streamName))
    verify(consumerMetricsReporterMock).aroundInvalidProcessing(ArgumentMatchers.eq(command2), ArgumentMatchers.eq(streamName))

    verifyNoMoreInteractions(validatorMock, mockActorResolver, consumerMetricsReporterMock)
  }

  it should "propagate exception when validator throws an exception" in new KafkaConsumerMultiProcessorInActorBuilder {

    val errorMessage = "Validation Error"

    Given("a validator that throws an exception")
    doThrow(new Exception(errorMessage)).when(validatorMock).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    When("processMessage is called")
    Then("the exception should be propagated")
    val thrownException = intercept[Exception] {
      consumer.processMessage(commands)
    }

    And("the exception message should match the expected message")
    thrownException.getMessage shouldEqual errorMessage

    And("the exception type should be verified")
    thrownException shouldBe a[Exception]
  }

  it should "propagate exception when actor resolver throws an exception" in new KafkaConsumerMultiProcessorInActorBuilder {

    val errorMessage = "Actor Resolver Error"

    Given("a validator that returns true for all commands")
    doReturn(true).when(validatorMock).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    And("an actor resolver that throws an exception for the first command")
    doThrow(new Exception(errorMessage)).when(mockActorResolver).apply(ArgumentMatchers.eq(command1))

    When("processMessage is called")
    Then("the exception should be propagated")
    val thrownException = intercept[Exception] {
      consumer.processMessage(commands)
    }

    And("the exception message should match the expected message")
    thrownException.getMessage shouldEqual errorMessage

    And("the exception type should be verified")
    thrownException shouldBe a[Exception]
  }

  it should "handle empty command list" in new KafkaConsumerMultiProcessorInActorBuilder {

    val emptyCommands = List.empty[DummyActorCommand]

    When("processMessage is called with empty commands")
    val result = consumer.processMessage(emptyCommands)

    Then("the result should be a successful Future")
    awaitResultWithSuccess(result)

    And("there should be no interactions with the validator")
    verifyZeroInteractions(validatorMock)

    And("there should be no interactions with the actor resolver")
    verifyZeroInteractions(mockActorResolver)

    verifyNoMoreInteractions(validatorMock, mockActorResolver, consumerMetricsReporterMock)
  }

  it should "handle single command in list" in new KafkaConsumerMultiProcessorInActorBuilder {

    val singleCommand = List(command1)

    Given("a validator that returns true for the single command")
    doReturn(true).when(validatorMock).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    And("an actor resolver that responds with Accepted for the command")
    val testProbe = TestProbe()
    testProbe.setAutoPilot((sender: ActorRef, msg: Any) => {
      msg match {
        case message: DummyActorCommand if message == command1 =>
          sender ! Accepted(System.currentTimeMillis())
          KeepRunning
        case _ => fail(s"Unexpected message: $msg")
      }
    })

    doReturn(testProbe.ref).when(mockActorResolver).apply(ArgumentMatchers.eq(command1))

    doAnswer((cmd: ActorCommand, result: Future[Accepted], stream: String) => result)
      .when(consumerMetricsReporterMock).aroundValidProcessing(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.eq(streamName))

    When("processMessage is called with single command")
    val result = consumer.processMessage(singleCommand)

    Then("the actor should receive the command")
    testProbe.expectMsg(command1)
    testProbe.expectNoMessage()

    And("the result should be a successful Future")
    awaitResultWithSuccess(result)

    And("the validator should be called once")
    verify(validatorMock, times(1)).isValid(ArgumentMatchers.any[List[DummyActorCommand]]())

    And("the actor resolver should be called once")
    verify(mockActorResolver).apply(ArgumentMatchers.eq(command1))

    And("metrics should be reported for the valid command")
    verify(consumerMetricsReporterMock).aroundValidProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.any(), ArgumentMatchers.eq(streamName))

    verifyNoMoreInteractions(validatorMock, mockActorResolver, consumerMetricsReporterMock)
  }

  it should "return correct group ID from configuration" in new KafkaConsumerMultiProcessorInActorBuilder {

    When("accessing the group ID")
    val groupId = consumer.groupId

    Then("it should return the configured group ID")
    groupId shouldEqual "test-group"
  }

  it should "return correct enableOnBootstrap flag when path exists and is true" in new KafkaConsumerMultiProcessorInActorBuilder {

    When("checking if enabled on bootstrap")
    val isEnabled = consumer.isEnabledOnBootstrap

    Then("it should return true")
    isEnabled shouldBe true
  }

  it should "return processor future unchanged when FlowMessage contains Ior.Left (validation failures)" in new KafkaConsumerMultiProcessorInActorBuilder {

    Given("a flow record with validation failures")
    val validationFailures = KafkaMessageInterpreterException(List("Validation error"))
    val flowMessage = Ior.left[KafkaMessageValidationFailures, List[DummyActorCommand]](validationFailures)
    val committableOffset = mock[CommittableOffset]
    val flowRecord = ("test-key", committableOffset, flowMessage)
    
    And("a processor future")
    val expectedOffset = mock[CommittableOffset]
    val processorFuture = Future.successful(expectedOffset)

    When("aroundProcessing is called")
    val result = consumer.aroundProcessing(flowRecord, processorFuture)

    Then("the processor future should be returned unchanged")
    whenReady(result, timeout(200.milliseconds)) { actualOffset =>
      actualOffset shouldEqual expectedOffset
    }

    And("no metrics should be reported")
    verifyZeroInteractions(consumerMetricsReporterMock)
  }

  it should "call aroundAllProcessing and return CommittableOffset when FlowMessage contains Ior.Right (valid commands)" in new KafkaConsumerMultiProcessorInActorBuilder {

    Given("a flow record with valid commands")
    val flowMessage = Ior.right[KafkaMessageValidationFailures, List[DummyActorCommand]](commands)
    val committableOffset = mock[CommittableOffset]
    val flowRecord = ("test-key", committableOffset, flowMessage)

    And("a processor future")
    val processorFuture = Future.successful(mock[CommittableOffset])

    And("metrics reporter configured to return the processor future")
    val expectedMetricsFuture = Future.successful(mock[CommittableOffset])
    doReturn(expectedMetricsFuture).when(consumerMetricsReporterMock)
      .aroundAllProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))
    doReturn(expectedMetricsFuture).when(consumerMetricsReporterMock)
      .aroundAllProcessing(ArgumentMatchers.eq(command2), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))

    When("aroundProcessing is called")
    val result = consumer.aroundProcessing(flowRecord, processorFuture)

    Then("the result should complete successfully")
    whenReady(result, timeout(200.milliseconds)) { actualOffset =>
      actualOffset shouldEqual committableOffset
    }

    And("aroundAllProcessing should be called for each command")
    verify(consumerMetricsReporterMock).aroundAllProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))
    verify(consumerMetricsReporterMock).aroundAllProcessing(ArgumentMatchers.eq(command2), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))

    verifyNoMoreInteractions(consumerMetricsReporterMock)
  }

  it should "call aroundAllProcessing and return CommittableOffset when FlowMessage contains Ior.Both (warnings and commands)" in new KafkaConsumerMultiProcessorInActorBuilder {

    Given("a flow record with validation warnings and valid commands")
    val validationWarnings = KafkaMessageIgnoreException(List("Warning message"))
    val flowMessage = Ior.both[KafkaMessageValidationFailures, List[DummyActorCommand]](validationWarnings, commands)
    val committableOffset = mock[CommittableOffset]
    val flowRecord = ("test-key", committableOffset, flowMessage)

    And("a processor future")
    val processorFuture = Future.successful(mock[CommittableOffset])

    And("metrics reporter configured to return the processor future")
    val expectedMetricsFuture = Future.successful(mock[CommittableOffset])
    doReturn(expectedMetricsFuture).when(consumerMetricsReporterMock)
      .aroundAllProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))
    doReturn(expectedMetricsFuture).when(consumerMetricsReporterMock)
      .aroundAllProcessing(ArgumentMatchers.eq(command2), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))

    When("aroundProcessing is called")
    val result = consumer.aroundProcessing(flowRecord, processorFuture)

    Then("the result should complete successfully")
    whenReady(result, timeout(200.milliseconds)) { actualOffset =>
      actualOffset shouldEqual committableOffset
    }

    And("aroundAllProcessing should be called for each command")
    verify(consumerMetricsReporterMock).aroundAllProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))
    verify(consumerMetricsReporterMock).aroundAllProcessing(ArgumentMatchers.eq(command2), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))

    verifyNoMoreInteractions(consumerMetricsReporterMock)
  }

  it should "handle empty command list in Ior.Right case" in new KafkaConsumerMultiProcessorInActorBuilder {

    Given("a flow record with empty commands list")
    val emptyCommands = List.empty[DummyActorCommand]
    val flowMessage = Ior.right[KafkaMessageValidationFailures, List[DummyActorCommand]](emptyCommands)
    val committableOffset = mock[CommittableOffset]
    val flowRecord = ("test-key", committableOffset, flowMessage)

    And("a processor future")
    val processorFuture = Future.successful(mock[CommittableOffset])

    When("aroundProcessing is called")
    val result = consumer.aroundProcessing(flowRecord, processorFuture)

    Then("the result should complete successfully")
    whenReady(result, timeout(200.milliseconds)) { actualOffset =>
      actualOffset shouldEqual committableOffset
    }

    And("no metrics should be reported")
    verifyZeroInteractions(consumerMetricsReporterMock)
  }

  it should "propagate failure from metrics reporter in aroundAllProcessing" in new KafkaConsumerMultiProcessorInActorBuilder {

    Given("a flow record with valid commands")
    val flowMessage = Ior.right[KafkaMessageValidationFailures, List[DummyActorCommand]](List(command1))
    val committableOffset = mock[CommittableOffset]
    val flowRecord = ("test-key", committableOffset, flowMessage)

    And("a processor future")
    val processorFuture = Future.successful(mock[CommittableOffset])

    And("metrics reporter that throws an exception")
    val expectedError = new RuntimeException("Metrics processing failed")
    doReturn(Future.failed(expectedError)).when(consumerMetricsReporterMock)
      .aroundAllProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))

    When("aroundProcessing is called")
    val result = consumer.aroundProcessing(flowRecord, processorFuture)

    Then("the exception should be propagated")
    whenReady(result.failed, timeout(200.milliseconds)) { exception =>
      exception shouldEqual expectedError
    }

    And("aroundAllProcessing should be called")
    verify(consumerMetricsReporterMock).aroundAllProcessing(ArgumentMatchers.eq(command1), ArgumentMatchers.eq(processorFuture), ArgumentMatchers.eq(streamName))

    verifyNoMoreInteractions(consumerMetricsReporterMock)
  }

  behavior of "getConsumerSettings"

  it should "return ConsumerSettings with PLAINTEXT security protocol" in new KafkaConsumerMultiProcessorInActorBuilder {
      Given("a kafka config with PLAINTEXT security protocol")
      val kafkaClientsConfig = ConfigFactory.parseString("""
        kafka-clients {
          group.id = "test-group"
          security.protocol = "PLAINTEXT"
        }
      """).withFallback(ConfigFactory.defaultReference().getConfig("pekko.kafka.consumer"))

      val plaintextConsumer = KafkaConsumerMultiProcessorInActor[String, String, List[DummyActorCommand]](
        Set("test-topic"),
        kafkaClientsConfig.withFallback(ConfigFactory.parseString("""
          stream-config {
            send-to-actor-ask-timeout = 5.minutes
            processing {
              partition-number = 2
              buffer-size = 1
            }
            batch-offsets {
              batch = 1
              batch-interval = 1.second
            }
            name = "dummyMultiProcessorStream"
          }
        """)),
        mockActorResolver,
        converterMock,
        mock[Deserializer[String]],
        mock[Deserializer[String]],
        validatorMock,
        mock[((String, CommittableOffset, Ior[KafkaMessageValidationFailures, List[DummyActorCommand]])) => Int],
        consumerMetricsReporterMock
      )

      When("getConsumerSettings is called")
      val keyDeserializer = mock[Deserializer[String]]
      val valueDeserializer = mock[Deserializer[String]]
      val result = plaintextConsumer.getConsumerSettings(keyDeserializer, valueDeserializer)

      Then("it should return ConsumerSettings without additional properties")
      result shouldBe a[org.apache.pekko.kafka.ConsumerSettings[_, _]]
      // The result should be a basic ConsumerSettings without additional security properties
    }

    it should "return ConsumerSettings with SASL_SSL security protocol and all required properties" in new KafkaConsumerMultiProcessorInActorBuilder {
      Given("a kafka config with SASL_SSL security protocol and all required properties")
      val kafkaClientsConfig = ConfigFactory.parseString("""
        kafka-clients {
          group.id = "test-group"
          security.protocol = "SASL_SSL"
          sasl.mechanism = "AWS_MSK_IAM"
          sasl.jaas.config = "software.amazon.msk.auth.iam.IAMLoginModule required;"
          sasl.client.callback.handler.class = "software.amazon.msk.auth.iam.IAMClientCallbackHandler"
        }
      """).withFallback(ConfigFactory.defaultReference().getConfig("pekko.kafka.consumer"))

      val saslConsumer = KafkaConsumerMultiProcessorInActor[String, String, List[DummyActorCommand]](
        Set("test-topic"),
        kafkaClientsConfig.withFallback(ConfigFactory.parseString("""
          stream-config {
            send-to-actor-ask-timeout = 5.minutes
            processing {
              partition-number = 2
              buffer-size = 1
            }
            batch-offsets {
              batch = 1
              batch-interval = 1.second
            }
            name = "dummyMultiProcessorStream"
          }
        """)),
        mockActorResolver,
        converterMock,
        mock[Deserializer[String]],
        mock[Deserializer[String]],
        validatorMock,
        mock[((String, CommittableOffset, Ior[KafkaMessageValidationFailures, List[DummyActorCommand]])) => Int],
        consumerMetricsReporterMock
      )

      When("getConsumerSettings is called")
      val keyDeserializer = mock[Deserializer[String]]
      val valueDeserializer = mock[Deserializer[String]]
      val result = saslConsumer.getConsumerSettings(keyDeserializer, valueDeserializer)

      Then("it should return ConsumerSettings with SASL_SSL properties")
      result shouldBe a[org.apache.pekko.kafka.ConsumerSettings[_, _]]
      // The result should include the security protocol and SASL properties
    }

    it should "throw IllegalArgumentException for unknown security protocol" in new KafkaConsumerMultiProcessorInActorBuilder {
      Given("a kafka config with an unknown security protocol")
      val kafkaClientsConfig = ConfigFactory.parseString("""
        kafka-clients {
          group.id = "test-group"
          security.protocol = "UNKNOWN_PROTOCOL"
        }
      """).withFallback(ConfigFactory.defaultReference().getConfig("pekko.kafka.consumer"))

      val unknownProtocolConsumer = KafkaConsumerMultiProcessorInActor[String, String, List[DummyActorCommand]](
        Set("test-topic"),
        kafkaClientsConfig.withFallback(ConfigFactory.parseString("""
          stream-config {
            send-to-actor-ask-timeout = 5.minutes
            processing {
              partition-number = 2
              buffer-size = 1
            }
            batch-offsets {
              batch = 1
              batch-interval = 1.second
            }
            name = "dummyMultiProcessorStream"
          }
        """)),
        mockActorResolver,
        converterMock,
        mock[Deserializer[String]],
        mock[Deserializer[String]],
        validatorMock,
        mock[((String, CommittableOffset, Ior[KafkaMessageValidationFailures, List[DummyActorCommand]])) => Int],
        consumerMetricsReporterMock
      )

      When("getConsumerSettings is called")
      val keyDeserializer = mock[Deserializer[String]]
      val valueDeserializer = mock[Deserializer[String]]

      Then("it should throw IllegalArgumentException")
      val exception = intercept[IllegalArgumentException] {
        unknownProtocolConsumer.getConsumerSettings(keyDeserializer, valueDeserializer)
      }

      And("the exception message should include the unknown protocol")
      exception.getMessage should include("Unknown security protocol UNKNOWN_PROTOCOL")
    }

}