package com.flutter.pcsa.md.actor.subclass.domain.dto

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.md.actor.subclass.domain.dto.generators.GenMDSubclassDTO
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

class MDSubclassDTOSpec extends AnyFlatSpecLike with Matchers with GenMDSubclassDTO {

  "MDSubclassDTO" should "provide an empty entity" in {
    val result = MDSubclassDTO.empty

    result.identifier shouldBe GbpId.empty
    result.superclassId shouldBe GbpId.empty
  }

  "MDSubclassDTO" should "provide a fulfilled entity" in {
    val result = genMDSubclassDTO.sample.get

    result != MDSubclassDTO.empty shouldBe true
    result.identifier != GbpId.empty shouldBe true
  }
}
