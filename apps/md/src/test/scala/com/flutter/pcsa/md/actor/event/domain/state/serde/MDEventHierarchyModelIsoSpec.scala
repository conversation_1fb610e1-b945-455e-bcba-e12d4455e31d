package com.flutter.pcsa.md.actor.event.domain.state.serde

import com.flutter.pcsa.md.actor.event.domain.dto.generators.GenMDEventHierarchyModel
import org.scalatest.matchers.should.Matchers
import org.scalatest.propspec.AnyPropSpec
import org.scalatestplus.scalacheck.ScalaCheckDrivenPropertyChecks

class MDEventHierarchyModelIsoSpec extends AnyPropSpec with ScalaCheckDrivenPropertyChecks with Matchers {

  property("MDEventHierarchy data should be isomorphic") {
    new GenMDEventHierarchyModel {
      new MDEventHierarchyModelIso {
        forAll(genMDEventHierarchyModel) { givenModel =>
          val pb = mdEventHierarchyModelIso.get(givenModel)
          val result = mdEventHierarchyModelIso.reverseGet(pb)
          givenModel shouldBe result
        }
      }
    }
  }
}