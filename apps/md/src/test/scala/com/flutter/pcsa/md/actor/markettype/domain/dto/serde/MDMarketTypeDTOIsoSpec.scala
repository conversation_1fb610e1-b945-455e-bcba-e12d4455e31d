package com.flutter.pcsa.md.actor.markettype.domain.dto.serde

import com.flutter.pcsa.md.actor.markettype.domain.dto.generators.GenMDMarketTypeDTO
import com.flutter.pcsa.md.actor.markettype.serde.ProtocolsIso.mdMarketTypeDTOIso
import org.scalatest.matchers.should.Matchers
import org.scalatest.propspec.AnyPropSpec
import org.scalatestplus.scalacheck.ScalaCheckDrivenPropertyChecks

class MDMarketTypeDTOIsoSpec extends AnyPropSpec with ScalaCheckDrivenPropertyChecks with Matchers with GenMDMarketTypeDTO {
  property("Market type data should be isomorphic") {
    forAll(genMDMarketTypeDTO) { givenModel =>
      val pb = mdMarketTypeDTOIso.get(givenModel)
      val result = mdMarketTypeDTOIso.reverseGet(pb)

      givenModel shouldBe result
    }
  }
}
