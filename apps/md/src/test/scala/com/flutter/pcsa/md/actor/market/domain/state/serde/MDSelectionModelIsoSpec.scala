package com.flutter.pcsa.md.actor.market.domain.state.serde

import com.flutter.pcsa.common.datatypes.GenPlatform
import com.flutter.pcsa.common.state.model.datatypes.{GenOverridableAndInheritableFeedField, GenOverridableFeedField, OverridableAndInheritableFeedField, OverridableFeedField, FeedField}
import com.flutter.pcsa.common.state.model.model.generators.GenEntityMetadata
import com.flutter.pcsa.md.actor.market.domain.state.MDSelectionModel
import org.scalatest.matchers.should.Matchers
import org.scalatest.propspec.AnyPropSpec
import org.scalatestplus.scalacheck.ScalaCheckDrivenPropertyChecks
import org.scalacheck.Gen

class MDSelectionModelIsoSpec extends AnyPropSpec with ScalaCheckDrivenPropertyChecks with Matchers {

  property("MDSelectionModel data should be isomorphic") {
    new GenPlatform with GenOverridableAndInheritableFeedField with GenEntityMetadata {
      new MDSelectionModelIso {

        lazy val genMDSelectionModelForProto: Gen[MDSelectionModel] = for {
          entityIdentifiers <- genEntityIdentifiers
          name <- genOverridableAndInheritableFeedStringField
          status <- genOverridableAndInheritableFeedStringField
        } yield MDSelectionModel(
          entityIdentifiers = entityIdentifiers,
          name = name,
          status = status,
          //TODO: PCSA-MD Milestone 3 - Below fields use exact defaults from MDSelectionModelIso.reverseGet
          displayed = OverridableAndInheritableFeedField.empty[Boolean].copy(value = Some(true)),
          dispOrder = OverridableFeedField.empty[Short].copy(value = Some(0)),
          jurisdiction = FeedField.empty[String],
          runnerNumber = OverridableFeedField.empty[Short],
          price = OverridableFeedField.empty[Double],
          startingPrice = FeedField.empty[Double],
          probability = FeedField.empty[Double],
          selectionType = FeedField.empty[String],
          handicapScore = FeedField.empty[Double],
          correctScoreA = OverridableFeedField.empty[Double],
          correctScoreB = OverridableFeedField.empty[Double],
          statusResult = OverridableFeedField.empty[String],
          statusResultConfirmed = FeedField.empty[Boolean],
          statusPlacePosition = OverridableFeedField.empty[Int],
          statusDeadHeatReductionWin = OverridableFeedField.empty[Double]
        )

        forAll(genMDSelectionModelForProto) { givenModel =>
          val pb = mdSelectionModelIso.get(givenModel)
          val result = mdSelectionModelIso.reverseGet(pb)
          givenModel shouldBe result
        }
      }
    }
  }
}