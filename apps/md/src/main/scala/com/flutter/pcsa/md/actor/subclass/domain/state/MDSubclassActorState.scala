package com.flutter.pcsa.md.actor.subclass.domain.state

import com.flutter.baseactor.domain.state.{ActorNoState, ActorState, SequenceNumber}

sealed trait MDSubclassActorState extends ActorState

case object MDSubclassNoState extends ActorNoState with MDSubclassActorState

case class MDSubclassState(lastPublishedNo: SequenceNumber,
                           inboundSeqNo: SequenceNumber,
                           subclassAggregationModel: MDSubclassAggregationModel,
                           metadata: Map[(String, Int), Long] = Map.empty) extends MDSubclassActorState
