package com.flutter.pcsa.md.actor.market.converter

import com.flutter.baseactor.converter.ActorCommand2ActorEvent
import com.flutter.pcsa.md.actor.market.MDMarketActor
import com.flutter.pcsa.md.actor.market.MDMarketActor.{MDMarketActorCommand, MDMarketActorEvent, MDMarketFeedCommand}

class MDMarketActorCommand2ActorEvent extends ActorCommand2ActorEvent[MDMarketActorCommand, MDMarketActorEvent] {

  override def toActorEvent(actorCommand: MDMarketActorCommand): MDMarketActorEvent = actorCommand match {
    case cmd: MDMarketFeedCommand =>
      MDMarketActor.MDMarketFeedCommandApplied(
        metadata = cmd.metadata,
        headers = cmd.headers,
        action = cmd.action,
        marketHierarchy = cmd.marketHierarchy,
        market = cmd.market,
        selections = cmd.selections
      )
  }

}
