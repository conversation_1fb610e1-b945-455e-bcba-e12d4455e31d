package com.flutter.pcsa.md.actor.market.domain.state

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.state.model.model.{EntityMetadata, HierarchyLinkMetadata, LiveEntityMetadata}

object MDMarketAggregationModel {

  def empty: MDMarketAggregationModel = MDMarketAggregationModel(
    market = LiveEntityMetadata(MDMarketModel.empty, feedCreated = false, hierarchyCreated = false, isLive = true),
    selections = Map.empty,
    marketTypeId = None,
    marketHierarchy = MDMarketHierarchyModel(
      event = HierarchyLinkMetadata(identifier = GbpId.empty, feedCreated = false),
      marketTypeLink = HierarchyLinkMetadata(identifier = GbpId.empty, feedCreated = false)
    )
  )
}

case class MDMarketAggregationModel(
    market: LiveEntityMetadata[MDMarketModel],
    selections: Map[GbpId, EntityMetadata[MDSelectionModel]],
    marketTypeId: Option[String],
    marketHierarchy: MDMarketHierarchyModel)
