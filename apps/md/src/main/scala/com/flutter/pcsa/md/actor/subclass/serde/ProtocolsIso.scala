package com.flutter.pcsa.md.actor.subclass.serde

import cats.implicits.catsSyntaxOptionId
import com.flutter.pcsa.md.actor.eventtype.serde.ProtocolsIso.mdFeedActionDTOIso
import com.flutter.pcsa.md.actor.subclass.MDSubclassActor.{MDInheritanceUpdateSubclass, MDSubclassFeedCommand, MDSubclassFeedCommandApplied, MDSubclassInheritanceUpdated, MDSubscribeSubclass, MDUnsubscribeSubclass}
import com.flutter.pcsa.md.actor.subclass.commands.proto.{MDInheritanceUpdateSubclassProto, MDSubclassFeedCommandProto, MDSubscribeSubclassProto, MDUnsubscribeSubclassProto}
import com.flutter.pcsa.md.actor.subclass.domain.dto.MDSubclassDTO
import com.flutter.pcsa.md.actor.subclass.domain.dto.serde.MDSubclassDTOIso
import com.flutter.pcsa.md.actor.subclass.events.proto.{MDSubclassFeedCommandApplied<PERSON>roto, MDSubclassInheritanceUpdatedProto}
import com.flutter.retryableactor.MetadataProtocolsIso
import com.flutter.retryableactor.metadata.Metadata.KafkaMetadata
import monocle.Iso

object ProtocolsIso extends MDSubclassDTOIso with MetadataProtocolsIso  {

  val mdSubclassFeedCommandIso: Iso[MDSubclassFeedCommand, MDSubclassFeedCommandProto] = Iso[MDSubclassFeedCommand, MDSubclassFeedCommandProto] { im =>
    MDSubclassFeedCommandProto(
      headers = im.headers,
      action = mdFeedActionDTOIso.get(im.action),
      subclassDTO = mdSubclassDTOIso.get(im.subclass).some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    MDSubclassFeedCommand(
      headers = pb.headers,
      action = mdFeedActionDTOIso.reverseGet(pb.action),
      subclass = pb.subclassDTO.map(mdSubclassDTOIso.reverseGet).getOrElse(MDSubclassDTO.empty),
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val mdSubclassFeedCommandAppliedIso: Iso[MDSubclassFeedCommandApplied, MDSubclassFeedCommandAppliedProto] = Iso[MDSubclassFeedCommandApplied, MDSubclassFeedCommandAppliedProto] { im =>
    MDSubclassFeedCommandAppliedProto(
      headers = im.headers,
      action = mdFeedActionDTOIso.get(im.action),
      subclassDto = mdSubclassDTOIso.get(im.subclass).some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    MDSubclassFeedCommandApplied(
      headers = pb.headers,
      action = mdFeedActionDTOIso.reverseGet(pb.action),
      subclass = mdSubclassDTOIso.reverseGet(pb.subclassDto.get),
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val subscribeSubclassCommandIso: Iso[MDSubscribeSubclass, MDSubscribeSubclassProto] = Iso[MDSubscribeSubclass, MDSubscribeSubclassProto] { im =>
    MDSubscribeSubclassProto(
      headers = im.headers,
      eventTypeId = im.eventTypeId,
      subclassId = im.subclassId,
      metadata = Option(kafkaMetadataIso.get(im.metadata))
    )
  } { pb =>
    MDSubscribeSubclass(
      subclassId = pb.subclassId,
      eventTypeId = pb.eventTypeId,
      headers = pb.headers,
      metadata = kafkaMetadataIso.reverseGet(pb.metadata.get)
    )
  }

  val unsubscribeSubclassCommandIso: Iso[MDUnsubscribeSubclass, MDUnsubscribeSubclassProto] = Iso[MDUnsubscribeSubclass, MDUnsubscribeSubclassProto] { im =>
    MDUnsubscribeSubclassProto(
      headers = im.headers,
      eventTypeId = im.eventTypeId,
      subclassId = im.subclassId,
      metadata = Option(kafkaMetadataIso.get(im.metadata))
    )
  } { pb =>
    MDUnsubscribeSubclass(
      subclassId = pb.subclassId,
      eventTypeId = pb.eventTypeId,
      headers = pb.headers,
      metadata = kafkaMetadataIso.reverseGet(pb.metadata.get)
    )
  }

  val subclassInheritanceUpdatedIso: Iso[MDSubclassInheritanceUpdated, MDSubclassInheritanceUpdatedProto] = Iso[MDSubclassInheritanceUpdated, MDSubclassInheritanceUpdatedProto] { im =>
    MDSubclassInheritanceUpdatedProto(
      headers = im.headers,
      identifier = im.identifier,
      notification = im.notification.some,
      metadata = Option(kafkaMetadataIso.get(im.metadata))
    )
  } { pb =>
    MDSubclassInheritanceUpdated(
      headers = pb.headers,
      identifier = pb.identifier,
      notification = pb.getNotification,
      metadata = kafkaMetadataIso.reverseGet(pb.metadata.get)
    )
  }

  val subclassInheritanceUpdateIso: Iso[MDInheritanceUpdateSubclass, MDInheritanceUpdateSubclassProto] = Iso[MDInheritanceUpdateSubclass, MDInheritanceUpdateSubclassProto] { im =>
    MDInheritanceUpdateSubclassProto(
      headers = im.headers,
      identifier = im.identifier,
      notification = im.notification.some,
      metadata = Option(kafkaMetadataIso.get(im.metadata))
    )
  } { pb =>
    MDInheritanceUpdateSubclass(
      headers = pb.headers,
      identifier = pb.identifier,
      notification = pb.getNotification,
      metadata = kafkaMetadataIso.reverseGet(pb.metadata.get)
    )
  }
}
