package com.flutter.pcsa.md.actor.eventtype.behaviour

import com.flutter.baseactor.BaseActor.ValidateState
import com.flutter.baseactor.behaviour.ActorDomainBehaviour
import com.flutter.pcsa.md.actor.eventtype.MDEventTypeActor.MDEventTypeActorCommand
import com.flutter.pcsa.md.actor.eventtype.domain.state.{MDEventTypeActorState, MDEventTypeNoState, MDEventTypeState}
import com.flutter.pcsa.md.common.actor.behaviour.{DomainValidatingBehaviour, EntityDomainValidation, ValidationResponse}
import com.flutter.pcsa.md.common.actor.validator.EntityDomainValidationDescription
import org.apache.pekko.actor.Actor


trait MDEventTypeActorDomainValidationBehaviour extends DomainValidatingBehaviour[MDEventTypeState]  with ActorDomainBehaviour[MDEventTypeActorState, MDEventTypeActorCommand]{

  this: Actor =>

  // $COVERAGE-OFF$
  override protected val validations: List[EntityDomainValidation[MDEventTypeState]] = List[EntityDomainValidation[MDEventTypeState]](
    EntityDomainValidation[MDEventTypeState](_.eventTypeAggregation.eventType.feedCreated,
      EventType => EntityDomainValidationDescription.CREATE_FROM_FEED_MISSING(EventType.eventTypeAggregation.eventType.identifier.asLong)),
    EntityDomainValidation[MDEventTypeState](_.eventTypeAggregation.eventType.hierarchyCreated,
      EventType => EntityDomainValidationDescription.HIERARCHY_NOTIFICATION_MISSING(EventType.eventTypeAggregation.eventType.identifier.asLong))
  )
  // $COVERAGE-ON$

  override def productBehaviour(state: MDEventTypeActorState): Receive = {
    validateCommands(state)
      .orElse(super.productBehaviour(state))
  }

  override protected def validate(state: MDEventTypeState): List[String] = {
    super.validate(state)
  }

  def validateCommands(state: MDEventTypeActorState): Receive = {
    case command: ValidateState =>
      val result = getValidationsForJMX(state, command.shardingKey)
      sender() ! result
  }

  private def getValidationsForJMX(state: MDEventTypeActorState, shardingKey: String): ValidationResponse = {
    state match {
      case currentState: MDEventTypeState =>
        createValidationResponse(currentState.eventTypeAggregation.eventType.identifier.asLong, validate(currentState), currentState.metadata)
      case MDEventTypeNoState =>
        ValidationResponse("No State to validate", shardingKey, None, None, None, List.empty)
    }
  }

  protected def getValidationResponse(state: MDEventTypeActorState): Option[(String,List[String])] = {
    state match {
      case  currentState: MDEventTypeState =>
        val validationResponse = createValidationResponse(currentState.eventTypeAggregation.eventType.identifier.asLong, validate(currentState), currentState.metadata)
        Some(currentState.eventTypeAggregation.eventType.identifier.asLong,validationResponse.errors.toList)
      case MDEventTypeNoState => None
    }
  }
}
