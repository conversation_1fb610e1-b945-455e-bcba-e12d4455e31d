package com.flutter.pcsa.md.actor.event.notifier

import com.flutter.baseactor.notifier.{ActorStateNotifier, ActorStateNotifierLogger}
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.md.actor.event.MDEventActor.{MDEventActorEvent, MDEventFeedCommandApplied}
import com.flutter.pcsa.md.actor.event.domain.state.{MDEventActorState, MDEventModel, MDEventNoState, MDEventState}
import com.flutter.pcsa.md.common.actor.domain.dto.MDFeedActionDTO
import com.flutter.pcsa.md.common.actor.publish.MDHeaders
import com.flutter.publish.HeaderPublisher
import com.flutter.publish.MessagePublisher.KafkaPublish

import scala.concurrent.Future

class MDEventStateNotifier(val headerPublisher: HeaderPublisher,
                           eventGlobalInstructionCreator: creator.MDEventGlobalInstructionCreator)
  extends ActorStateNotifier[MDEventActorState, MDEventActorEvent]
    with MDHeaders with ActorStateNotifierLogger {

  /**
   * Method responsible to calculate changes between actor states and publish a notification to a stream (if exists)
   *
   * @param previousState Previous actor state
   * @param state         Next actor state
   * @return Future that should only be completed when publish is confirmed
   */
  override def notify(previousState: MDEventActorState,
                      state: MDEventActorState,
                      actorEvent: MDEventActorEvent): Future[Unit] = {
    (previousState, state, actorEvent) match {

      case (MDEventNoState, currentState: MDEventState, _) =>
        notify(None, currentState, actorEvent)
      case (previousState: MDEventState, currentState: MDEventState, _) if previousState.aggregateEventDomain != currentState.aggregateEventDomain =>
        notify(Some(previousState), currentState, actorEvent)
      case (previousState: MDEventState, currentState: MDEventState, event: MDEventFeedCommandApplied) if event.action == MDFeedActionDTO.REFRESH =>
        notify(Some(previousState), currentState, actorEvent)
      case (_, _, _) => Future.successful()
    }
  }

  private def notify(previousState: Option[MDEventState],
                     currentState: MDEventState,
                     actorEvent: MDEventActorEvent): Future[Unit] = {
    val rampSportHeader = currentState.aggregateEventDomain.eventHierarchy.subclass.identifier.asLong.split(":").lastOption.filterNot(_.isBlank)
      .map(rampSport => Map[String, AnyRef]("RAMP_SPORT" -> rampSport)).getOrElse(Map.empty)

    val seqNum = currentState.lastPublishedNo + 1
    val internalSeqNum = currentState.inboundSeqNo
    val currentEvent = currentState.aggregateEventDomain.event

    if (isCurrentEventValid(currentEvent)) {
      val eventInstruction = eventGlobalInstructionCreator.createInstruction(
        previousState.map(_.aggregateEventDomain.event),
        currentEvent,
        currentState.aggregateEventDomain.eventHierarchy,
        actorEvent)

      val eventKafkaPublishable = eventInstruction.map(eventInstruction => {
        KafkaPublish(
          currentState.aggregateEventDomain.event.identifier.asLong,
          seqNum,
          Some(eventInstruction.action.name),
          actorEvent.headers ++ rampSportHeader,
          extractHeaders(actorEvent.headers, seqNum.value, internalSeqNum.value, eventInstruction, actorEvent),
          eventInstruction.toByteArray
        )
      })

      val instructions = eventKafkaPublishable.toList

      logEndOfProcessing(
        gbpId = currentState.aggregateEventDomain.event.identifier.asLong,
        actorEventName = actorEvent.getClass.getSimpleName,
        actorEventHeaders = actorEvent.headers,
        nrOfMessagesToBePublished = instructions.length
      )

      headerPublisher.publish(instructions)
    } else {
      logger.atWarn()
        .log(s"operation=notify, msg='Event validation failed', eventId=${currentEvent.identifier.asLong}, " +
          s"isValid=${currentEvent.isValid}, hasName=${currentEvent.model.name.value.exists(_.trim.nonEmpty)}, hasStartTime=${currentEvent.model.startTime.value.nonEmpty}, hasStatus=${currentEvent.model.status.value.nonEmpty}")
      Future.successful()
    }
  }

  private def isCurrentEventValid(currentEvent: EntityMetadata[MDEventModel]) = {
    currentEvent.model.name.value.exists(_.trim.nonEmpty) && currentEvent.model.startTime.value.nonEmpty && currentEvent.model.status.value.exists(_.trim.nonEmpty)
  }
}
