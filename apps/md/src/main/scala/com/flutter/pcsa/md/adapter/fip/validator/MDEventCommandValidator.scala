package com.flutter.pcsa.md.adapter.fip.validator

import com.flutter.adapter.validator.Validator
import com.flutter.pcsa.md.actor.event.MDEventActor.{MDEventActorCommand, MDEventFeedCommand}
import com.flutter.pcsa.md.common.actor.domain.dto.MDFeedActionDTO
import com.flutter.pcsa.md.common.actor.domain.dto.MDFeedActionDTO.{CREATE, REFRESH}


class MDEventCommandValidator extends Validator[MDEventActorCommand] {

  override def isValid(command: MDEventActorCommand): Boolean = {
    // TODO - APOLLO11 - Review validation
    command match {
      case event: MDEventFeedCommand if isRefreshOrCreate(event.action) => true
      case _ => false
    }
  }

  private def isRefreshOrCreate(action: MDFeedActionDTO.Value): Boolean = {
    action == CREATE || action == REFRESH
  }

}
