package com.flutter.pcsa.md.actor.eventtype.domain.state

import com.flutter.pcsa.common.datatypes.{EntityIdentifiers, Platform}
import com.flutter.pcsa.common.state.model.datatypes.OverridableFeedField
import com.flutter.pcsa.common.state.model.model.EntityModel

object MDEventTypeModel {
  val empty: MDEventTypeModel = MDEventTypeModel(
    entityIdentifiers = EntityIdentifiers(Map.empty),
    name = OverridableFeedField.empty[String]
  )
}

case class MDEventTypeModel(
    entityIdentifiers: EntityIdentifiers,
    name: OverridableFeedField[String]) extends EntityModel {

  def isEmpty: Boolean = {
    this.copy(
      entityIdentifiers = EntityIdentifiers(entityIdentifiers.identifiers - Platform.Gbp)
    ) == MDEventTypeModel.empty
  }
}
