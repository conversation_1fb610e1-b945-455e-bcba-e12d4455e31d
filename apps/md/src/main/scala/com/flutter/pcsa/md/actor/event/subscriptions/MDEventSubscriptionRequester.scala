package com.flutter.pcsa.md.actor.event.subscriptions

import com.flutter.infra.kafka.producer.GenericKafkaProducer
import com.flutter.pcsa.md.actor.event.MDEventActor.MDEventActorEvent
import com.flutter.pcsa.md.actor.event.domain.state.MDEventActorState
import com.flutter.pcsa.md.subscriptions.contract.proto.MDSubscriptionInstruction
import com.flutter.sharedplatforms.LazyLogging
import com.flutter.subscriptions.requester.SubscriptionRequester

import scala.concurrent.Future

class MDEventSubscriptionRequester(val genericKafkaProducer: GenericKafkaProducer[String, MDSubscriptionInstruction])
  extends SubscriptionRequester[MDEventActorState, MDEventActorEvent] with LazyLogging{

  override def requestSubscriptions(previousState: MDEventActorState, state: MDEventActorState, actorEvent: MDEventActorEvent): Future[Unit] = {
    Future.successful()
  }
}
