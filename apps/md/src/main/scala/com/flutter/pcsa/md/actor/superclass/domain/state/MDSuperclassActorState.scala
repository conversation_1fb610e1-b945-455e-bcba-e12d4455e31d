package com.flutter.pcsa.md.actor.superclass.domain.state

import com.flutter.baseactor.domain.state.{ActorNoState, ActorState, SequenceNumber}

sealed trait MDSuperclassActorState extends ActorState

case object MDSuperclassNoState extends ActorNoState with MDSuperclassActorState

case class MDSuperclassState(lastPublishedNo: SequenceNumber,
                             inboundSeqNo: SequenceNumber,
                             superclassAggregationModel: MDSuperclassAggregationModel,
                             metadata: Map[(String, Int), Long] = Map.empty) extends MDSuperclassActorState
