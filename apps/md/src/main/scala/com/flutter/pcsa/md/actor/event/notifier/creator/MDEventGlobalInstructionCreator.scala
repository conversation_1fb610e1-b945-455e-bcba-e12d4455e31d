package com.flutter.pcsa.md.actor.event.notifier.creator

import com.flutter.baseactor.notifier.creator.StateDeltaSyntax.StateDelta
import com.flutter.pcsa.common.datatypes.Platform
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.md.actor.event.MDEventActor.{MDEventActorEvent, MDEventFeedCommandApplied}
import com.flutter.pcsa.md.actor.event.domain.state.{MDEventHierarchyModel, MDEventModel}
import com.flutter.pcsa.md.common.actor.domain.dto.MDFeedActionDTO
import com.flutter.pcsa.md.common.actor.notifier.converters.implicits.modelConverters.{asStatusField, asStringField, asTimestampField}
import com.flutter.product.catalogue.global.market.GlobalCatalogueInstruction.Action
import com.flutter.product.catalogue.global.market.{Event, GlobalCatalogueInstruction}
import com.flutter.product.catalogue.market.domain.common.{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TimestampField}


class MDEventGlobalInstructionCreator {

    def createInstruction(
                           previousEvent: Option[EntityMetadata[MDEventModel]],
                           currentEvent: EntityMetadata[MDEventModel],
                           eventHierarchyModel: MDEventHierarchyModel,
                           actorEvent: MDEventActorEvent
                         ): Option[GlobalCatalogueInstruction] = {

      if (currentEvent.isValid) {

        val wasPreviousValid = previousEvent.exists(_.isValid)
        val action: Action = defineAction(currentEvent, actorEvent, wasPreviousValid)
        createEventEntity(
          previousEvent.filter(_.isValid).filter(_ => action != Action.REFRESH).map(_.model),
          currentEvent.model,
          eventHierarchyModel
        ).map(
          entity =>
            GlobalCatalogueInstruction(
              action = action,
              entity = entity
            )
        )
      } else {
        None
      }
    }

  private def defineAction(currentEvent: EntityMetadata[MDEventModel], actorEvent: MDEventActorEvent, wasPreviousValid: Boolean) = {
    val action: Action = actorEvent match {
      case feedInstruction: MDEventFeedCommandApplied if wasPreviousValid && feedInstruction.action == MDFeedActionDTO.REFRESH => Action.REFRESH
      case feedInstruction: MDEventFeedCommandApplied if wasPreviousValid => Action.UPDATE
      case _ if !wasPreviousValid && currentEvent.isValid => Action.CREATE
      case _ => Action.UPDATE
    }
    action
  }

  private def createEventEntity(
                                   previousEvent: Option[MDEventModel],
                                   currentEvent: MDEventModel,
                                   eventHierarchyModel: MDEventHierarchyModel
                                 ): Option[GlobalCatalogueInstruction.Entity.Event] = {

      previousEvent
        .filter(_ == currentEvent)
        .map(_ => None)
        .getOrElse({
          val name: Option[StringField] = (previousEvent, currentEvent) ? (_.name)
          val status: Option[StatusField] = (previousEvent, currentEvent) ?  (_.status)
          val startTime: Option[TimestampField] = (previousEvent, currentEvent) ? (_.startTime)

          if (Seq(name, status, startTime).exists(_.nonEmpty)) {
            val event = Event(
              id = currentEvent.identifier.asLong,
              name = name,
              status = status,
              startTime = startTime,
              sportexId = currentEvent.entityIdentifiers.identifiers.getOrElse(Platform.Sportex, ""),
              eventTypeId = eventHierarchyModel.eventType.identifier.asLong,
              subclassId = eventHierarchyModel.subclass.identifier.asLong
            )
            Some(GlobalCatalogueInstruction.Entity.Event(event))
          } else {
            None
          }
        })
    }
  }
