package com.flutter.pcsa.md.actor.market.subscriptions

import com.flutter.infra.kafka.producer.GenericKafkaProducer
import com.flutter.pcsa.md.actor.market.MDMarketActor.MDMarketActorEvent
import com.flutter.pcsa.md.actor.market.domain.state.MDMarketActorState
import com.flutter.pcsa.md.subscriptions.contract.proto.MDSubscriptionInstruction
import com.flutter.sharedplatforms.LazyLogging
import com.flutter.subscriptions.requester.SubscriptionRequester

import scala.concurrent.Future

class MDMarketSubscriptionRequester (val genericKafkaProducer: GenericKafkaProducer[String, MDSubscriptionInstruction])
  extends SubscriptionRequester[MDMarketActorState, MDMarketActorEvent] with LazyLogging {

  override def requestSubscriptions(previousState: MDMarketActorState, state: MDMarketActorState, actorEvent: MDMarketActorEvent): Future[Unit] = {
    Future.successful()
  }

}
