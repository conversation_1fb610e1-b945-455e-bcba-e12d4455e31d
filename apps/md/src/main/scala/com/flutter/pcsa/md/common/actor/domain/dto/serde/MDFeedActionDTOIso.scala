package com.flutter.pcsa.md.common.actor.domain.dto.serde

import com.flutter.pcsa.md.actor.common.domain.dto.proto.FeedActionProto
import com.flutter.pcsa.md.common.actor.domain.dto.MDFeedActionDTO
import monocle.Iso

trait MDFeedActionDTOIso {

  val mdFeedActionDTOIso: Iso[MDFeedActionDTO.Value, FeedActionProto] = Iso[MDFeedActionDTO.Value, FeedActionProto] {
    case MDFeedActionDTO.CREATE => FeedActionProto.CREATE
    case MDFeedActionDTO.UPDATE => FeedActionProto.UPDATE
    case MDFeedActionDTO.REFRESH => FeedActionProto.REFRESH
    case MDFeedActionDTO.UNDEFINED => FeedActionProto.UNDEFINED
  } {
    case FeedActionProto.CREATE => MDFeedActionDTO.CREATE
    case FeedActionProto.UPDATE => MDFeedActionDTO.UPDATE
    case FeedActionProto.REFRESH => MDFeedActionDTO.REFRESH
    case FeedActionProto.UNDEFINED => MDFeedActionDTO.UNDEFINED
  }
}
