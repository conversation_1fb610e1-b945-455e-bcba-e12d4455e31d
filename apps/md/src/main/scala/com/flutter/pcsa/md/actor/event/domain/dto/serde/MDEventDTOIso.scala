package com.flutter.pcsa.md.actor.event.domain.dto.serde

import com.flutter.pcsa.common.datatypes
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.serde.EnumerationsIso.entityIdentifiersIso
import com.flutter.pcsa.md.actor.common.domain.dto.proto.MDEventDTOProto
import com.flutter.pcsa.md.actor.event.domain.dto.MDEventDTO
import com.flutter.pcsa.md.actor.event.domain.state.serde.MDCommonModelIsoUtils.{localDateTimeToLong, longToLocalDateTime}
import monocle.Iso

trait MDEventDTOIso {

  val mdEventDTOIso: Iso[MDEventDTO, MDEventDTOProto] = Iso[MDEventDTO, MDEventDTOProto] { im =>
    MDEventDTOProto(
      identifier = im.identifier.asLong,
      entityIdentifiers = entityIdentifiersIso.get(im.entityIdentifiers.identifiers),
      name = im.name,
      status = im.status,
      startTime = im.startTime.map(localDateTimeToLong)
    )
  } { pb =>
    MDEventDTO(
      identifier = GbpId(pb.identifier),
      entityIdentifiers = datatypes.EntityIdentifiers(entityIdentifiersIso.reverseGet(Map.from(pb.entityIdentifiers))),
      name = pb.name,
      startTime = pb.startTime.map(longToLocalDateTime),
      status = pb.status,
      // TODO: APOLLO PCSA-MD Milestone 3 - Below fields not present in proto
      displayed = None,
      offInRunning = None,
      isOff = None,
      eventLocation = None,
      participantAOrigins = None,
      participantBOrigins = None
    )
  }
}