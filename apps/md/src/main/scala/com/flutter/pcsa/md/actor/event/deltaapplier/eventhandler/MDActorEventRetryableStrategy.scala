package com.flutter.pcsa.md.actor.event.deltaapplier.eventhandler

import com.flutter.pcsa.md.actor.event.MDEventActor.{MDEventActorCommand, MDEventActorEvent}
import com.flutter.pcsa.md.actor.event.domain.state.MDEventActorState
import com.flutter.retryableactor.behaviour.strategy.RetryableStrategy
import com.typesafe.config.Config

// TODO APOLLO Implement strategy
class MDActorEventRetryableStrategy (override val actorConfig: Config) extends RetryableStrategy[MDEventActorState, MDEventActorCommand, MDEventActorEvent] {

  override def applyMetadataToState(state: MDEventActorState, actorEvent: MDEventActorEvent): MDEventActorState =  {
    state
  }
  override def getRefreshEvent(actorEvent: MDEventActorEvent): MDEventActorEvent = {
    actorEvent
  }
}
