package com.flutter.pcsa.md.actor.subclass.domain.state

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.state.model.model.{EntityMetadata, HierarchyLinkMetadata}

object MDSubclassAggregationModel {
  def empty: MDSubclassAggregationModel =
    MDSubclassAggregationModel(
      subclass = EntityMetadata(MDSubclassModel.empty, feedCreated = false, hierarchyCreated = false),
      subclassHierarchy = MDSubclassHierarchyModel(
        superclass =  HierarchyLinkMetadata(identifier = GbpId.empty, feedCreated = false)
      )
    )
}

case class MDSubclassAggregationModel(subclass: EntityMetadata[MDSubclassModel], subclassHierarchy: MDSubclassHierarchyModel)
