package com.flutter.pcsa.md.actor.market.deltaapplier.eventhandler

import com.flutter.baseactor.deltaapplier.eventhandler.ActorEventHandler
import com.flutter.baseactor.domain.state.SequenceNumber
import com.flutter.pcsa.md.actor.market.MDMarketActor.{MDMarketActorEvent, MDMarketFeedCommandApplied}
import com.flutter.pcsa.md.actor.market.deltaapplier.eventhandler.eventupdated.{MDMarketFeedToStateMerger, MDMarketHierarchyFeedToStateMerger, MDSelectionFeedToStateMerger}
import com.flutter.pcsa.md.actor.market.domain.state.{MDMarketActorState, MDMarketAggregationModel, MDMarketModel, MDMarketNoState, MDMarketState, MDSelectionModel}
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.md.common.actor.deltaapplier.feedcommand.MDFeedCommandApplier

object MDMarketFeedCommandAppliedHandler
  extends ActorEventHandler[MDMarketActorState, MDMarketActorEvent]
    with MDMarketHierarchyFeedToStateMerger
    with MDMarketFeedToStateMerger
    with MDSelectionFeedToStateMerger
    with MDFeedCommandApplier {

  override def handle(): MDMarketFeedCommandAppliedHandler.Handler = {
    case (MDMarketNoState, event: MDMarketFeedCommandApplied) =>
      applyEventToState(SequenceNumber(0), SequenceNumber(0) + 1, MDMarketAggregationModel.empty, event)
    case (MDMarketState(lastPublishedNo, inboundSeqNo, aggregateMarketDomain, _), event: MDMarketFeedCommandApplied) =>
      applyEventToState(lastPublishedNo, inboundSeqNo + 1, aggregateMarketDomain, event)
  }

  def applyEventToState(lastPublishedNo: SequenceNumber,
                        inboundSeqNo: SequenceNumber,
                        stateMarketAggregation: MDMarketAggregationModel,
                        actorEvent: MDMarketFeedCommandApplied): MDMarketState = {

    val marketUpdate = actorEvent.market
    val selectionsUpdate = actorEvent.selections
    val marketHierarchyUpdate = actorEvent.marketHierarchy

    val stateMarketHierarchy = stateMarketAggregation.marketHierarchy
    val stateMarketSelections = stateMarketAggregation.selections

    val marketHierarchyUpdated = applyFeedUpdate(stateMarketHierarchy, marketHierarchyUpdate)
    val marketUpdated = applyFeedCommand[MDMarketModel](
      stateMarketAggregation.market,
      actorEvent.action,
      marketModel => applyFeedUpdate(marketModel, marketUpdate))

    val selectionsUpdated = selectionsUpdate.foldLeft(stateMarketSelections) {
      case (currentSelections, (selectionId, selectionUpdate)) =>
        val existingSelection = currentSelections.get(selectionId)
        val updatedSelection = existingSelection match {
          case Some(selection) =>
            applyFeedCommand[MDSelectionModel](
              selection,
              actorEvent.action,
              selectionModel => applyFeedUpdate(selectionModel, selectionUpdate)
            )
          case None =>
            // Create new selection if it doesn't exist
            applyFeedCommand[MDSelectionModel](
              EntityMetadata(MDSelectionModel.empty, feedCreated = true, hierarchyCreated = true),
              actorEvent.action,
              selectionModel => applyFeedUpdate(selectionModel, selectionUpdate)
            )
        }
        currentSelections.updated(selectionId, updatedSelection)
    }

    MDMarketState(
      lastPublishedNo,
      inboundSeqNo,
      stateMarketAggregation.copy(
        market = marketUpdated,
        selections = selectionsUpdated,
        marketHierarchy = marketHierarchyUpdated
      ))
  }

}
