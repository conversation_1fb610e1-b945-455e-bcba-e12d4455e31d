package com.flutter.pcsa.md.actor.event.converter

import com.flutter.baseactor.converter.ActorCommand2ActorEvent
import com.flutter.pcsa.md.actor.event.MDEventActor.{MDEventActorCommand, MDEventActorEvent, MDEventFeedCommand, MDEventFeedCommandApplied}

class MDEventActorCommand2ActorEvent extends ActorCommand2ActorEvent[MDEventActorCommand, MDEventActorEvent]{

  override def toActorEvent(actorCommand: MDEventActorCommand): MDEventActorEvent = actorCommand match {
    case cmd: MDEventFeedCommand =>
      MDEventFeedCommandApplied(
        metadata = cmd.metadata,
        headers = cmd.headers,
        action = cmd.action,
        eventHierarchy = cmd.eventHierarchy,
        event = cmd.event
      )
  }
}
