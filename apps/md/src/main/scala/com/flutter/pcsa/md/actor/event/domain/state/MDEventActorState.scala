package com.flutter.pcsa.md.actor.event.domain.state

import com.flutter.baseactor.domain.state.{ActorNoState, ActorState, SequenceNumber}

sealed trait MDEventActorState extends ActorState

case object MD<PERSON>ventNoState extends ActorNoState with MDEventActorState
case class MD<PERSON>ventState(lastPublishedNo: SequenceNumber,
                        inboundSeqNo: SequenceNumber,
                        aggregateEventDomain: MDEventAggregationModel,
                        metadata: Map[(String, Int), Long] = Map.empty) extends MDEventActorState
