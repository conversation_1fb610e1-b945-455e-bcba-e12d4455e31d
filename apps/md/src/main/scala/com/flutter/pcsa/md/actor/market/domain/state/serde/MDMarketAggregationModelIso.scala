package com.flutter.pcsa.md.actor.market.domain.state.serde

import cats.implicits.catsSyntaxOptionId
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.state.model.model.{HierarchyLinkMetadata, LiveEntityMetadata}
import com.flutter.pcsa.common.state.model.serde.HierarchyLinkMetadataIso
import com.flutter.pcsa.md.actor.market.domain.state.{MDMarketAggregationModel, MDMarketHierarchyModel, MDMarketModel, MDSelectionModel}
import com.flutter.pcsa.md.actor.market.domain.state.proto.MDMarketAggregationModelProto
import monocle.Iso

trait MDMarketAggregationModelIso extends MDMarketModelIso with MDSelectionModelIso with HierarchyLinkMetadataIso {

  val mdMarketAggregationModelIso: Iso[MDMarketAggregationModel, MDMarketAggregationModelProto] = Iso[MDMarketAggregationModel, MDMarketAggregationModelProto] { im =>
    MDMarketAggregationModelProto(
      event = hierarchyLinkMetadataIso.get(im.marketHierarchy.event),
      marketTypeLink = hierarchyLinkMetadataIso.get(im.marketHierarchy.marketTypeLink),
      market = mdMarketEntityMetadataModelIso.get(im.market).some,
      selections = im.selections.map({ case (_, selection) => mdSelectionEntityMetadataModelIso.get(selection) }).toSeq,
      marketTypeId = im.marketTypeId
    )
  } { pb =>
    MDMarketAggregationModel(
      market = pb.market.map(mdMarketEntityMetadataModelIso.reverseGet).getOrElse(
        LiveEntityMetadata(
          MDMarketModel.empty,
          feedCreated = false,
          hierarchyCreated = false,
          isLive = false)
      ),
      selections = pb.selections.map(mdSelectionEntityMetadataModelIso.reverseGet).map(selection =>
        GbpId(selection.model.entityIdentifiers.identifiers.getOrElse(com.flutter.pcsa.common.datatypes.Platform.Gbp, GbpId.empty.asLong)) -> selection).toMap,
      marketTypeId = pb.marketTypeId,
      marketHierarchy = MDMarketHierarchyModel(
        event = pb.event match {
          case Some(e) => hierarchyLinkMetadataIso.reverseGet(e.some)
          case None => HierarchyLinkMetadata(identifier = GbpId.empty, feedCreated = false)
        },
        marketTypeLink = pb.marketTypeLink match {
          case Some(mtl) => hierarchyLinkMetadataIso.reverseGet(mtl.some)
          case None => HierarchyLinkMetadata(identifier = GbpId.empty, feedCreated = false)
        }
      )
    )
  }
}
