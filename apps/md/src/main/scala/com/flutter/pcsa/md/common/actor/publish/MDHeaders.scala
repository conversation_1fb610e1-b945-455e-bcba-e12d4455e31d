package com.flutter.pcsa.md.common.actor.publish

import com.flutter.HOST_NAME
import com.flutter.baseactor.BaseActor.ActorEvent
import com.flutter.headers.{ACTION, ACTOR_EVENT}
import com.flutter.pcsa.md.MD
import com.flutter.product.catalogue.global.market.GlobalCatalogueInstruction
import com.flutter.publish.{Head<PERSON>, HeadersExtractor}

trait MDHeaders extends HeadersExtractor[GlobalCatalogueInstruction] {

  override def getCurrentTime: java.lang.Long = java.lang.Long.valueOf(System.currentTimeMillis())

  def extractHeaders(actorHeader: Map[String, AnyRef], sequenceNumber: Int, internalSequenceNumber: Int, payload: GlobalCatalogueInstruction, actorEvent: ActorEvent): Map[String, AnyRef] =
    Map(
      Headers.EVENT_ID -> String.valueOf(payload.getEvent.id),
      Headers.SEQUENCE_NUMBER -> java.lang.Integer.valueOf(sequenceNumber),
      Headers.PCSA_INTERNAL_SEQUENCE_NUMBER -> java.lang.Integer.valueOf(internalSequenceNumber),
      Headers.SEQUENCER_HOST -> HOST_NAME,
      ACTION(MD.ServiceName) -> payload.action.toString,
      ACTOR_EVENT(MD.ServiceName) -> actorEvent.getClass.getSimpleName,
      Headers.SERVICE_OUT -> getCurrentTime,
      Headers.DOMAIN -> "MARKET"
    )
}
