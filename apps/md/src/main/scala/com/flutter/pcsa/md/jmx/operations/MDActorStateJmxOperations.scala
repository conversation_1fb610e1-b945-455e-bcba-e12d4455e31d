package com.flutter.pcsa.md.jmx.operations

import com.flutter.baseactor.domain.state.ActorNoState
import com.flutter.jmx.support.JMXUtils.beautyJson
import com.flutter.jmx.JmxActorResolver.ActorResolver
import com.flutter.jmx.operations.ActorStateOperations
import com.flutter.jmx.operations.internal.{GetStateActorOperation, GetValidationActorOperation, PurgeActorOperation, ToggleValidationLoggingOperation}
import com.flutter.jmx.support.ActorCommunicationSupport
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.md.actor.eventtype.domain.state.MDEventTypeState
import com.flutter.pcsa.md.actor.eventtype.domain.state.serde.MDEventTypeStateIso
import com.flutter.pcsa.md.actor.markettype.domain.state.MDMarketTypeState
import com.flutter.pcsa.md.actor.markettype.domain.state.serde.MDMarketTypeStateIso
import com.flutter.pcsa.md.actor.subclass.domain.state.MDSubclassState
import com.flutter.pcsa.md.actor.subclass.domain.state.serde.MDSubclassStateIso
import com.flutter.pcsa.md.actor.superclass.domain.state.MDSuperclassState
import com.flutter.pcsa.md.actor.superclass.domain.state.serde.MDSuperclassStateIso
import com.flutter.pcsa.md.common.actor.behaviour.ValidationResponse
import com.flutter.pcsa.md.common.actor.domain.state.serde.MDValidationResponseIso
import com.flutter.pcsa.md.jmx.operations.MDActorStateJmxOperations.ActorStateNotFound
import org.apache.pekko.actor.ActorRef
import org.apache.pekko.util.Timeout

class MDActorStateJmxOperations(override val actorResolver: ActorResolver, override val timeout: Timeout) extends ActorStateOperations
  with PurgeActorOperation with GetStateActorOperation with GetValidationActorOperation with ToggleValidationLoggingOperation with ActorCommunicationSupport {

  override protected def internalFlush(gbpId: GbpId, sendTo: ActorRef, headers: Map[String, String], isPropagated: Boolean): String = NOT_IMPLEMENTED

  protected val actorStateToString: PartialFunction[Any, String] = {
    case state: MDEventTypeState   => beautyJson(MDEventTypeStateIso.mdEventTypeStateIso.get(state))
    case state: MDMarketTypeState  => beautyJson(MDMarketTypeStateIso.mdMarketTypeStateIso.get(state))
    case state: MDSubclassState    => beautyJson(MDSubclassStateIso.mdSubclassStateIso.get(state))
    case state: MDSuperclassState  => beautyJson(MDSuperclassStateIso.mdSuperclassStateIso.get(state))
    case _: ActorNoState       => ActorStateNotFound
  }

  protected val validationToString: PartialFunction[Any, String] = {
    case response: ValidationResponse =>
      beautyJson(
        MDValidationResponseIso.mdValidationResponseIso.get(
          response.copy(entityId = GbpId(response.entityId).asLong)
        )
      )
  }

}

object MDActorStateJmxOperations {
  lazy val ActorStateNotFound = "Actor state not found"
  def apply(actorResolver: String => Option[ActorRef], timeout: Timeout): MDActorStateJmxOperations =
    new MDActorStateJmxOperations(actorResolver, timeout)
}