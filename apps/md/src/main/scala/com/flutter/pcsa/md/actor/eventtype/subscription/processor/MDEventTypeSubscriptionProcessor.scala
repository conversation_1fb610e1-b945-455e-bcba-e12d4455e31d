package com.flutter.pcsa.md.actor.eventtype.subscription.processor

import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.md.actor.eventtype.MDEventTypeActor.{MDEventTypeActorEvent, MDEventTypeSubscribeCommands, MDRefreshActorEventType}
import com.flutter.pcsa.md.actor.eventtype.domain.state.{MDEventTypeActorState, MDEventTypeModel, MDEventTypeNoState, MDEventTypeState}
import com.flutter.pcsa.md.subscription.notifications.contract.proto.MDSubscriptionNotificationInstruction.Notification
import com.flutter.subscriptions.actor.notifier.SubscribersPublisher
import com.flutter.subscriptions.actor.processor.{BaseSubscriptionProcessor, ExtractedNotification}
import com.typesafe.config.Config
import org.apache.pekko.actor.ActorContext
import com.flutter.pcsa.md.actor.eventtype.subscription.notification.MDDeltaNotificationEventType.deltaMDNotificationEventTypeUpdate
import com.flutter.pcsa.md.subscription.notifications.contract.proto.MDEventTypeUpdateNotification

//noinspection ScalaStyle
class MDEventTypeSubscriptionProcessor(
                                       val actorContext: ActorContext,
                                       val subscriptionNotifierPublisher: SubscribersPublisher[Notification.EventTypeUpdateNotification],
                                       val subscriptionShardActorConfig: Config)
  extends BaseSubscriptionProcessor[
    MDEventTypeActorState,
    MDEventTypeActorEvent,
    MDEventTypeSubscribeCommands,
    MDEventTypeModel,
    Notification.EventTypeUpdateNotification
  ] {

  override val actorName: MDEventTypeSubscribeCommands => String = _ => eventTypeId
  override val emptyEntityModel: MDEventTypeModel = MDEventTypeModel.empty
  private val eventTypeId = actorContext.self.path.name
  
  private val EmptyEventTypeMetadata: EntityMetadata[MDEventTypeModel] = EntityMetadata(
    model = emptyEntityModel,
    feedCreated = false,
    hierarchyCreated = false
  )

  override def deltaNotification(
                                  previousStateNotification: Notification.EventTypeUpdateNotification,
                                  newStateNotification: Notification.EventTypeUpdateNotification
                                ): Option[Notification.EventTypeUpdateNotification] = {
    if (newStateNotification == previousStateNotification) {
      None
    } else {
      Some(
        newStateNotification |-| previousStateNotification
      )
    }
  }

  override def generateNotifications(
                                      previousState: MDEventTypeActorState, 
                                      state: MDEventTypeActorState, 
                                      event: MDEventTypeActorEvent
                                    ): List[ExtractedNotification[Notification.EventTypeUpdateNotification]] = {
    val previousStateOption = event match {
      case _: MDRefreshActorEventType =>
        None
      case _: Any => 
        previousState match {
          case MDEventTypeNoState => None
          case state: MDEventTypeState => Some(state)
        } 
    }

    deriveStateNotification(previousStateOption, state, extractEntityMetadataNotification)
      .map(
        extractedNotification => ExtractedNotification(eventTypeId, extractedNotification, event.headers)
      )
      .toList
    
  }

  private def extractEntityMetadataNotification(state: MDEventTypeActorState): EntityMetadata[MDEventTypeModel] = state match {
    case MDEventTypeNoState =>
      EmptyEventTypeMetadata
    case stateToExtract: MDEventTypeState =>
      stateToExtract.eventTypeAggregation.eventType
  }

  override def extractEntityMetadataSubscription(cmd: MDEventTypeSubscribeCommands): MDEventTypeActorState => EntityMetadata[MDEventTypeModel] =
    extractEntityMetadataNotification

  override def ensureItIsTheExpectedTypeOfSubscribeCommands(cmd: Any): Boolean =
    cmd.isInstanceOf[MDEventTypeSubscribeCommands]

  override def buildEntityNotification(model: MDEventTypeModel): Notification.EventTypeUpdateNotification = {
    Notification.EventTypeUpdateNotification(
      MDEventTypeUpdateNotification()
    )
  }
}
