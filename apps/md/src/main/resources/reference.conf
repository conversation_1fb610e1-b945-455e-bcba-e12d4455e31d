md {
  actor-system.shutdown-timeout: 1.minutes
  kafka {
    consumer {
      gth {
        enableConsumerOnBootstrap: true
        enableConsumerOnBootstrap: ${?MD_GTH_CONSUMER_ENABLED}

        topics: []

        use-dispatcher = "md-gth-consumer-dispatcher"
        kafka-clients {
          #Should be changed to SASL_SSL protocol to use AWS IAM
          #For now only SASL_SSL and PLAINTEXT security protocols are supported by the application
          security.protocol = "PLAINTEXT"
          security.protocol = ${?KAFKA_SECURITY_PROTOCOL}
          sasl.mechanism = "AWS_MSK_IAM"
          sasl.jaas.config = "software.amazon.msk.auth.iam.IAMLoginModule required;"
          sasl.client.callback.handler.class = "software.amazon.msk.auth.iam.IAMClientCallbackHandler"

          session.timeout.ms = 30000

          bootstrap.servers = ${?GTH_KAFKA_CONSUMER_HOST}

          group.id = "md-gth-group-id"
          client.id = "md-gth-group-id"
        }

        stream-config {
          name = "gth-stream-kafka"
          send-to-actor-ask-timeout = 1580 millis
          retry-configs {
            min-backoff-on-process-message = 1580 millis
            max-backoff-on-process-message = 3160 milliseconds
          }
          processing {
            partition-number = 1
          }
        }
      }
      fip {
          enableConsumerOnBootstrap: true
          enableConsumerOnBootstrap: ${?MD_FIP_CONSUMER_ENABLED}

          topics: []

          use-dispatcher = "md-fip-consumer-dispatcher"
          kafka-clients {
            security.protocol = "PLAINTEXT"
            security.protocol = ${?KAFKA_SECURITY_PROTOCOL}

            session.timeout.ms = 30000

            bootstrap.servers = ${?FIP_KAFKA_CONSUMER_HOST}

            group.id = "md-fip-consumer"
            client.id = "md-fip-consumer"
          }
          stream-config {
            name = "fip-stream-kafka"
            send-to-actor-ask-timeout = 1580 millis
            retry-configs{
              min-backoff-on-process-message = 1580 millis
              max-backoff-on-process-message = 3160 milliseconds
            }
          }
        }
    }
    producer {
      kafka-clients.bootstrap.servers = "localhost:9092"
      enableOnBootstrap = true
      delivery.timeout.ms = 100
      request.timeout.ms = 20
      default-topic {
        topic: "catalog.market.domain.fd.others.delta"
        topic: ${?PCSA_MD_OUTBOUND_OTHERS}
      }
      topics: [
        {
          sportId: [
            "49"
          ]
          topic: "catalog.market.domain.fd.football.delta"
          topic: ${?PCSA_MD_OUTBOUND_FOOTBALL}
        }
        {
          sportId: [
            "13"
          ]
          topic: "catalog.market.domain.fd.tennis.delta"
          topic: ${?PCSA_MD_OUTBOUND_TENNIS}
        }
        {
          sportId: [
            "23"
          ]
          topic: "catalog.market.domain.fd.racing.delta"
          topic: ${?PCSA_MD_OUTBOUND_HORSE_RACING}
        }
        {
          sportId: [
            "146", # Euro Basketball
            "16"   # US Basketball
          ]
          topic: "catalog.market.domain.fd.basketball.delta"
          topic: ${?PCSA_MD_OUTBOUND_BASKETBALL}
        }
      ]
      kafka-clients {
        bootstrap.servers = ${?MD_KAFKA_PRODUCER_HOST}
        client.id = "md-producer"
      }
    }
    subscription {
      request {
        common-kafka-clients {
          client.id = "pcsa-md-subscription-request"
          bootstrap.servers = ${?MD_SUBSCRIPTION_REQUEST_CONSUMER_HOST}
        }
        producer {
          topic: "pcsa.md.subscription.request"
          topic: ${?MD_PRODUCER_SUBSCRIPTION_REQUEST_TOPIC}
          delivery.timeout.ms = 100
          request.timeout.ms = 20
          kafka-clients {
          }
        }
        consumer {
          topic: "pcsa.md.subscription.request"
          topic: ${?MD_CONSUMER_SUBSCRIPTION_REQUEST_TOPIC}
          enableConsumerOnBootstrap: true
          enableConsumerOnBootstrap: ${?MD_SUBSCRIPTION_REQUEST_CONSUMER_ENABLED}

          use-dispatcher = "md-subscription-request-consumer-dispatcher"
          kafka-clients {
            group.id = "pcsa.md.subscription.request"
          }

          stream-config {
            name = "subscription-request-stream-kafka"
            send-to-actor-ask-timeout = 4610 millis
            retry-configs {
              min-backoff-on-process-message = 4610 millis
              max-backoff-on-process-message = 9220 milliseconds
            }
          }
        }
      }
      notification {
        common-kafka-clients {
          client.id = "pcsa-md-subscription-notification"
          bootstrap.servers = ${?MD_SUBSCRIPTION_NOTIFICATION_CONSUMER_HOST}
        }
        producer {
          topic: "pcsa.md.subscription.notification"
          topic: ${?MD_PRODUCER_SUBSCRIPTION_NOTIFIER_TOPIC}
          delivery.timeout.ms = 100
          request.timeout.ms = 20
          kafka-clients {
          }
        }
        consumer {
          topic: "pcsa.md.subscription.notification"
          topic: ${?MD_CONSUMER_SUBSCRIPTION_NOTIFIER_TOPIC}
          enableConsumerOnBootstrap: true
          enableConsumerOnBootstrap: ${?MD_SUBSCRIPTION_NOTIFIER_CONSUMER_ENABLED}

          kafka-clients {
            group.id = "pcsa.md.subscription.notification"
          }

          stream-config {
            name = "subscription-notification-stream-kafka"
            send-to-actor-ask-timeout = 1580.milliseconds
            retry-configs {
              min-backoff-on-process-message = 1580.milliseconds
              max-backoff-on-process-message = 3160.milliseconds
            }
          }
        }
      }
    }
  }
  event-type-actor {
    shardName = "MDEventTypeStateActor"
    noOfshards = 11
    noOfshards = ${?MD_EVENTTYPE_ACTOR_NO_OF_SHARDS}

    passivation {
      strategy = "md-event-type-actor-strategy"
      md-event-type-actor-strategy = ${config.actor.passivation.default-pcsa-strategy}
      md-event-type-actor-strategy {
        active-entity-limit = 700
      }
    }

    shard-allocation-strategy = "md-event-type-actor-shard-allocation-strategy"
    md-event-type-actor-shard-allocation-strategy = ${config.actor.least-shard-allocation-strategy}
    md-event-type-actor-shard-allocation-strategy {
      rebalance-absolute-limit = 1
      rebalance-relative-limit = 0.9
    }

    event-type-subscriptions {
      notifier {
        maxNotificationsBatchSize = 20
        producer {
        }
      }
      shard-actor {
        maxSubscriptionsPerShard = 10000
        timeout = 3060 milliseconds
        snapshotInterval = 500
        maxRetryAttempts = 3

        persistence {
          journalPluginId = "md-event-type-subscription-actor-persistence-plugin.journal"
          snapshotPluginId = "md-event-type-subscription-actor-persistence-plugin.snapshot"
        }
      }
    }
  }
  subclass-actor {
    shardName = "MDSubclassStateActor"
    noOfshards = 11
    noOfshards = ${?MD_SUBCLASS_ACTOR_NO_OF_SHARDS}

    passivation {
      strategy = "md-subclass-actor-strategy"
      md-subclass-actor-strategy = ${config.actor.passivation.default-pcsa-strategy}
      md-subclass-actor-strategy {
        active-entity-limit = 700
      }
    }

    shard-allocation-strategy = "md-subclass-actor-shard-allocation-strategy"
    md-subclass-actor-shard-allocation-strategy = ${config.actor.least-shard-allocation-strategy}
    md-subclass-actor-shard-allocation-strategy {
      rebalance-absolute-limit = 1
      rebalance-relative-limit = 0.9
    }

    subclass-subscriptions {
      notifier {
        maxNotificationsBatchSize = 20
        producer {
        }
      }
      shard-actor {
        maxSubscriptionsPerShard = 10000
        timeout = 3060 milliseconds
        snapshotInterval = 500
        maxRetryAttempts = 3

        persistence {
          journalPluginId = "md-subclass-subscription-actor-persistence-plugin.journal"
          snapshotPluginId = "md-subclass-subscription-actor-persistence-plugin.snapshot"
        }
      }
    }
  }

  market-type-actor {
     shardName = "MDMarketTypeStateActor"
     noOfshards = 11
     noOfshards = ${?MD_MARKET_TYPE_ACTOR_NO_OF_SHARDS}

     passivation {
       strategy = "md-market-type-actor-strategy"
       md-market-type-actor-strategy = ${config.actor.passivation.default-pcsa-strategy}
       md-market-type-actor-strategy {
         active-entity-limit = 700
       }
     }

     shard-allocation-strategy = "md-market-type-actor-shard-allocation-strategy"
     md-market-type-actor-shard-allocation-strategy = ${config.actor.least-shard-allocation-strategy}
     md-market-type-actor-shard-allocation-strategy {
       rebalance-absolute-limit = 1
       rebalance-relative-limit = 0.9
     }

     market-type-subscriptions {
       notifier {
         maxNotificationsBatchSize = 20
         producer {
         }
       }
       shard-actor {
         maxSubscriptionsPerShard = 10000
         timeout = 3060 milliseconds
         snapshotInterval = 500
         maxRetryAttempts = 3

         persistence {
           journalPluginId = "md-market-type-link-subscription-actor-persistence-plugin.journal"
           snapshotPluginId = "md-market-type-link-subscription-actor-persistence-plugin.snapshot"
         }
       }
     }
  }

  superclass-actor {
    shardName = "MDSuperclassStateActor"
    noOfShards = 11
    noOfShards = ${?MD_SUPERCLASS_ACTOR_NO_OF_SHARDS}
    passivation {
      strategy = "md-superclass-actor-strategy"

      md-superclass-actor-strategy = ${config.actor.passivation.default-pcsa-strategy}
      md-superclass-actor-strategy {
        active-entity-limit = 150
      }
    }
    shard-allocation-strategy = "md-superclass-actor-shard-allocation-strategy"
    md-superclass-actor-shard-allocation-strategy = ${config.actor.least-shard-allocation-strategy}
    md-superclass-actor-shard-allocation-strategy {
      rebalance-absolute-limit = 1
      rebalance-relative-limit = 0.9
    }

    superclass-subscriptions {
      notifier {
        maxNotificationsBatchSize = 20
        producer {
        }
      }
      shard-actor {
        maxSubscriptionsPerShard = 10000
        timeout = 3060 milliseconds
        snapshotInterval = 500
        maxRetryAttempts = 3

        persistence {
          journalPluginId = "md-superclass-subscription-actor-persistence-plugin.journal"
          snapshotPluginId = "md-superclass-subscription-actor-persistence-plugin.snapshot"
        }
      }
    }
  }

  event-actor {
      shardName = "MDEventStateActor"
      noOfshards = 11
      noOfshards = ${?MD_EVENT_ACTOR_NO_OF_SHARDS}

      passivation {
        strategy = "md-event-actor-strategy"
        md-event-actor-strategy = ${config.actor.passivation.default-pcsa-strategy}
        md-event-actor-strategy {
          active-entity-limit = 700
        }
      }

      shard-allocation-strategy = "md-event-actor-shard-allocation-strategy"
      md-event-actor-shard-allocation-strategy = ${config.actor.least-shard-allocation-strategy}
      md-event-actor-shard-allocation-strategy {
        rebalance-absolute-limit = 1
        rebalance-relative-limit = 0.9
      }

      event-subscriptions {
        notifier {
          maxNotificationsBatchSize = 20
          producer {
          }
        }
        shard-actor {
          maxSubscriptionsPerShard = 10000
          timeout = 3060 milliseconds
          snapshotInterval = 500
          maxRetryAttempts = 3

          persistence {
            journalPluginId = "md-event-subscription-actor-persistence-plugin.journal"
            snapshotPluginId = "md-event-subscription-actor-persistence-plugin.snapshot"
          }
        }
      }
  }

  market-actor {
        shardName = "MDMarketStateActor"
        noOfshards = 11
        noOfshards = ${?MD_MARKET_ACTOR_NO_OF_SHARDS}

        passivation {
          strategy = "md-market-actor-strategy"
          md-market-actor-strategy = ${config.actor.passivation.default-pcsa-strategy}
          md-market-actor-strategy {
            active-entity-limit = 700
          }
        }

        shard-allocation-strategy = "md-event-actor-shard-allocation-strategy"
        md-market-actor-shard-allocation-strategy = ${config.actor.least-shard-allocation-strategy}
        md-market-actor-shard-allocation-strategy {
          rebalance-absolute-limit = 1
          rebalance-relative-limit = 0.9
        }

        market-subscriptions {
          notifier {
            maxNotificationsBatchSize = 20
            producer {
            }
          }
          shard-actor {
            maxSubscriptionsPerShard = 10000
            timeout = 3060 milliseconds
            snapshotInterval = 500
            maxRetryAttempts = 3

            persistence {
              journalPluginId = "md-market-subscription-actor-persistence-plugin.journal"
              snapshotPluginId = "md-market-subscription-actor-persistence-plugin.snapshot"
            }
          }
        }
    }


  jmx {
    enable = false
    enable = ${?MD_JMX_ENABLED}
    operations {
      actorState {
        enable = false
        enable = ${?MD_JMX_OPERATIONS_ACTOR_STATE_ENABLED}
        timeout = 10
        timeout = ${?MD_JMX_OPERATIONS_ACTOR_STATE_TIMEOUT}
      }
    }
  }
}

pekko {
  actor {
    serializers {
      md-superclass-proto = "com.flutter.pcsa.md.serialization.MDSuperclassProtobufSerializer"
      md-subclass-proto = "com.flutter.pcsa.md.serialization.MDSubclassProtobufSerializer"
      md-event-type-proto = "com.flutter.pcsa.md.serialization.MDEventTypeProtobufSerializer"
      md-market-type-proto = "com.flutter.pcsa.md.serialization.MDMarketTypeProtobufSerializer"
      md-validation-response-proto = "com.flutter.pcsa.md.serialization.MDValidationResponseSerializer"

    }

    serialization-bindings {
      "com.flutter.pcsa.md.actor.superclass.MDSuperclassActor$MDSuperclassActorCommand" = md-superclass-proto
      "com.flutter.pcsa.md.actor.superclass.MDSuperclassActor$MDSuperclassFeedCommandApplied" = md-superclass-proto
      "com.flutter.pcsa.md.actor.superclass.domain.state.MDSuperclassActorState" = md-superclass-proto
      "com.flutter.pcsa.md.actor.subclass.MDSubclassActor$MDSubclassActorCommand" = md-subclass-proto
      "com.flutter.pcsa.md.actor.subclass.MDSubclassActor$MDSubclassFeedCommandApplied" = md-subclass-proto
      "com.flutter.pcsa.md.actor.subclass.MDSubclassActor$MDSubclassInheritanceUpdated" = md-subclass-proto
      "com.flutter.pcsa.md.actor.subclass.domain.state.MDSubclassActorState" = md-subclass-proto
      "com.flutter.pcsa.md.actor.eventtype.MDEventTypeActor$MDEventTypeActorCommand" = md-event-type-proto
      "com.flutter.pcsa.md.actor.eventtype.MDEventTypeActor$MDEventTypeFeedCommandApplied" = md-event-type-proto
      "com.flutter.pcsa.md.actor.eventtype.MDEventTypeActor$MDEventTypeInheritanceUpdated" = md-event-type-proto
      "com.flutter.pcsa.md.actor.eventtype.domain.state.MDEventTypeActorState" = md-event-type-proto
      "com.flutter.pcsa.md.actor.markettype.MDMarketTypeActor$MDMarketTypeActorCommand" = md-market-type-proto
      "com.flutter.pcsa.md.actor.markettype.MDMarketTypeActor$MDMarketTypeFeedCommandApplied" = md-market-type-proto
      "com.flutter.pcsa.md.actor.markettype.MDMarketTypeActor$MDMarketTypeLinkInheritanceUpdated" = md-market-type-proto
      "com.flutter.pcsa.md.actor.markettype.domain.state.MDMarketTypeActorState" = md-market-type-proto
      "com.flutter.pcsa.md.common.actor.behaviour.ValidationResponse" = md-validation-response-proto

    }
  }
}

md-gth-consumer-dispatcher = ${pekko.actor.default-dispatcher}
md-fip-consumer-dispatcher = ${pekko.actor.default-dispatcher}
md-subclass-worker-dispatcher = ${pekko.actor.default-dispatcher}
md-superclass-worker-dispatcher = ${pekko.actor.default-dispatcher}
md-market-type-worker-dispatcher = ${pekko.actor.default-dispatcher}
md-event-type-worker-dispatcher = ${pekko.actor.default-dispatcher}
md-market-worker-dispatcher = ${pekko.actor.default-dispatcher}
md-event-worker-dispatcher = ${pekko.actor.default-dispatcher}

md-subscription-request-consumer-dispatcher =${pekko.actor.default-dispatcher}