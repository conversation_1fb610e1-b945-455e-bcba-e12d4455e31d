pekko.persistence {
  journal {
    auto-start-journals = ${pekko.persistence.journal.auto-start-journals} [
      "md-superclass-actor-persistence-plugin.journal",
      "md-superclass-subscription-actor-persistence-plugin.journal",
      "md-subclass-actor-persistence-plugin.journal",
      "md-subclass-subscription-actor-persistence-plugin.journal"
      "md-event-type-actor-persistence-plugin.journal",
      "md-event-type-subscription-actor-persistence-plugin.journal",
      "md-market-type-actor-persistence-plugin.journal",
      "md-market-type-link-subscription-actor-persistence-plugin.journal",
      "md-event-actor-persistence-plugin.journal",
      "md-event-subscription-actor-persistence-plugin.journal",
      "md-market-actor-persistence-plugin.journal",
      "md-market-subscription-actor-persistence-plugin.journal"
    ]
  }
  snapshot-store {
    auto-start-snapshot-stores = ${pekko.persistence.snapshot-store.auto-start-snapshot-stores} [
      "md-superclass-actor-persistence-plugin.snapshot",
      "md-superclass-subscription-actor-persistence-plugin.snapshot",
      "md-subclass-actor-persistence-plugin.snapshot",
      "md-subclass-subscription-actor-persistence-plugin.snapshot",
      "md-event-type-actor-persistence-plugin.snapshot",
      "md-event-type-subscription-actor-persistence-plugin.snapshot",
      "md-market-type-actor-persistence-plugin.snapshot",
      "md-market-type-link-subscription-actor-persistence-plugin.snapshot",
      "md-event-actor-persistence-plugin.snapshot",
      "md-event-subscription-actor-persistence-plugin.snapshot",
      "md-market-actor-persistence-plugin.snapshot",
      "md-market-subscription-actor-persistence-plugin.snapshot"
    ]
  }
}
