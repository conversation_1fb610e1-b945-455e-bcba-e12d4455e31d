syntax = "proto3";

import "md-common/MDFeedActions.proto";
import "md-market-type-dto/MDMarketTypeDTO.proto";
import "md-market-type-dto/MDMarketTypeLinkDTO.proto";
import "md-market-type-dto/MDMarketTypeHierarchyDTO.proto";
import "KafkaMetadata.proto";

package com.flutter.pcsa.md.actor.markettype.commands.proto;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  flat_package: true
};

message MDMarketTypeFeedCommandProto {
  map<string, string> headers = 1;
  com.flutter.pcsa.md.actor.common.domain.dto.proto.FeedActionProto action = 2;
  com.flutter.pcsa.actor.common.metadata.proto.KafkaMetadataProto metadata = 3;
  optional com.flutter.pcsa.md.actor.markettype.domain.dto.proto.MDMarketTypeDTOProto marketType = 4;
  com.flutter.pcsa.md.actor.markettype.domain.dto.proto.MDMarketTypeHierarchyDTOProto marketTypeHierarchy = 5;
}

message MDSubscribeMarketTypeLinkProto {
  map<string, string> headers = 1;
  string marketTypeId = 2;
  string marketTypeLinkId = 3;
  string marketId = 4;
  com.flutter.pcsa.actor.common.metadata.proto.KafkaMetadataProto metadata = 5;
}

message MDUnsubscribeMarketTypeLinkProto {
  map<string, string> headers = 1;
  string marketTypeId = 2;
  string marketTypeLinkId = 3;
  string marketId = 4;
  com.flutter.pcsa.actor.common.metadata.proto.KafkaMetadataProto metadata = 5;
}
