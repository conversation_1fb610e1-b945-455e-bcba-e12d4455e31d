package com.flutter.pcsa.ms.actor.market.domain.dto.serde

import cats.implicits.catsSyntaxOptionId
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.ms.actor.common.domain.dto.proto.{MSMarketAggregationDTOProto, MSMarketDTOProto}
import com.flutter.pcsa.ms.actor.market.domain.dto.MSMarketAggregationDTO
import monocle.Iso

trait MSMarketAggregationDTOIso extends MSMarketDTOIso with MSSelectionAggregationDTOIso {

  val msMarketAggregationDTOIso: Iso[MSMarketAggregationDTO, MSMarketAggregationDTOProto] = Iso[MSMarketAggregationDTO, MSMarketAggregationDTOProto] {im =>
    MSMarketAggregationDTOProto(
      market = msMarketDTOIso.get(im.market).some,
      selections = msSelectionAggregationDTOIso.get(im.selections),
      marketTypeLinkIdentifier = if(im.marketTypeLinkIdentifier.isEmpty) None else Some(im.marketTypeLinkIdentifier.asLong))
  }{ pb =>
    MSMarketAggregationDTO(
      market = msMarketDTOIso.reverseGet(pb.market.getOrElse(MSMarketDTOProto.defaultInstance)),
      selections = msSelectionAggregationDTOIso.reverseGet(pb.selections),
      marketTypeLinkIdentifier = pb.marketTypeLinkIdentifier.map(GbpId.apply(_)).getOrElse(GbpId.empty)
    )
  }
}
