package com.flutter.pcsa.ms.common.actor.domain.dto.serde

import com.flutter.pcsa.ms.common.actor.domain.dto.MSActionDTO
import com.flutter.pcsa.ms.common.actor.domain.dto.MSActionDTO.{CLOSE, CREATE, DELETE, FLUSH, REFRESH, UNDEFINED, UPDATE}
import monocle.Iso

trait MSActionDTOIso {
  val msActionDTOIso: Iso[MSActionDTO.Value, String] = Iso[MSActionDTO.Value, String]{
    case UNDEFINED => "UNDEFINED"
    case CREATE => "CREATE"
    case UPDATE => "UPDATE"
    case REFRESH => "REFRESH"
    case FLUSH => "FLUSH"
    case CLOSE => "CLOSE"
    case DELETE => "DELETE"
  } {
    case "CREATE" => CREATE
    case "UPDATE" => UPDATE
    case "REFRESH" => REFRESH
    case "FLUSH" => FLUSH
    case "CLOSE" => CLOSE
    case "DELETE" => DELETE
    case _ => UNDEFINED
  }
}
