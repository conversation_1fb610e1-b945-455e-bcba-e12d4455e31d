package com.flutter.pcsa.ms.actor.market.converter

import com.flutter.baseactor.converter.ActorCommand2ActorEvent
import com.flutter.pcsa.ms.actor.market.MSMarketActor.{MSMarketActorCommand, MSMarketActorEvent, MSMarketUpdated, MSUpdateMarket}

class MSMarketActorCommand2ActorEvent extends ActorCommand2ActorEvent[MSMarketActorCommand, MSMarketActorEvent] {

  override def toActorEvent(actorCommand: MSMarketActorCommand): MSMarketActorEvent = actorCommand match {
    case cmd: MSUpdateMarket =>
      MSMarketUpdated(
        metadata = cmd.metadata,
        identifier = cmd.shardingKey,
        action = cmd.action,
        timestamp = cmd.eventIn,
        headers = cmd.headers,
        aggregate = cmd.marketAggregationDTO)
  }
}
