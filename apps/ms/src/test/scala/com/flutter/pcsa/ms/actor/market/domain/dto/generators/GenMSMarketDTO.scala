package com.flutter.pcsa.ms.actor.market.domain.dto.generators

import com.flutter.pcsa.common.datatypes.GenPlatform
import com.flutter.pcsa.common.datatypes.dto.GenPatchableField
import com.flutter.pcsa.ms.actor.market.domain.dto.MSMarketDTO
import org.scalacheck.Gen

trait GenMSMarketDTO extends GenPlatform with GenPatchableField {
  lazy val genMSMarketDTO: Gen[MSMarketDTO] = for {
    entityIdentifiers <- genEntityIdentifiers
    eventId <- genSomeString
    sportexId <- genSomeString
    name <- genPatchableField(genOptionString)
  } yield MSMarketDTO(
    entityIdentifiers = entityIdentifiers,
    eventId = eventId,
    sportexId = sportexId,
    name = name
  )
}
