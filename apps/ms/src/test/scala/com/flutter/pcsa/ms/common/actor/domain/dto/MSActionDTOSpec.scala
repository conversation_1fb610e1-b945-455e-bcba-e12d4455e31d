package com.flutter.pcsa.ms.common.actor.domain.dto

import com.flutter.pcsa.common.GenCommon
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.{TableDrivenPropertyChecks, TableFor2}

class MSActionDTOSpec extends AnyFlatSpecLike with Matchers with TableDrivenPropertyChecks {

  trait MSActionDTOFixture extends GenCommon {
    lazy val cases: TableFor2[MSActionDTO.Value, String] = Table(
      ("value", "stringValue"),
      (MSActionDTO.UNDEFINED, "UNDEFINED"),
      (MSActionDTO.CREATE, "CREATE"),
      (MSActionDTO.UPDATE, "UPDATE"),
      (MSActionDTO.REFRESH, "REFRESH"),
      (MSActionDTO.FLUSH, "FLUSH"),
      (MSActionDTO.CLOSE, "CLOSE"),
      (MSActionDTO.DELETE, "DELETE")
    )
  }

  "MSActionDTO" should "have the correct values" in new MSActionDTOFixture {
    forAll(cases)((value, stringRepresentation) => {
      value.toString shouldBe stringRepresentation
    })
  }

  it should "provide a method to extract an action from a String, with UNDEFINED as the default value" in new MSActionDTOFixture {
    forAll(cases)((value, stringRepresentation) => {
      MSActionDTO.fromString(stringRepresentation) shouldBe value
    })
    MSActionDTO.fromString(genString.sample.get) shouldBe MSActionDTO.UNDEFINED
  }

}
