package com.flutter.pcsa.rd.actor.subclass.domain.state

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.state.model.model.{EntityMetadata, HierarchyLinkMetadata}

object RDSubclassAggregationModel {
  def empty: RDSubclassAggregationModel =
    RDSubclassAggregationModel(
      subclass = EntityMetadata(RDSubclassModel.empty, feedCreated = false, hierarchyCreated = false),
      subclassHierarchy = RDSubclassHierarchyModel(
        superclass =  HierarchyLinkMetadata(identifier = GbpId.empty, feedCreated = false)
      )
    )
}

case class RDSubclassAggregationModel(subclass: EntityMetadata[RDSubclassModel],
                                      subclassHierarchy: RDSubclassHierarchyModel)
