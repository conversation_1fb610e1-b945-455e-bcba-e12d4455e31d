package com.flutter.pcsa.rd.actor.event.deltaapplier.postprocessor.calculations

import com.flutter.pcsa.common.Global.{GLOBAL, Instance}
import com.flutter.pcsa.common.state.model.datatypes.viewer.implicits.overridableInheritableValueViewer.ViewerOverridableInheritableValueOps
import com.flutter.pcsa.common.state.model.datatypes.viewer.{Flag, InstanceValue}
import com.flutter.pcsa.common.state.model.datatypes.{OverridableAndInheritableField, Unlocked}
import com.flutter.pcsa.rd.actor.event.deltaapplier.postprocessor.OverridableAndInheritableCalculationOps

class RDMarketLeastAndMostMaxBetLimitsInnerProcessor extends OverridableAndInheritableCalculationOps{

  def applyLimitsLogic(
                                leastMaxBet: OverridableAndInheritableField[Double],
                                mostMaxBet: OverridableAndInheritableField[Double],
                                recalculatedInstances: Set[Instance]
                              ): (OverridableAndInheritableField[Double], OverridableAndInheritableField[Double]) = {

    val instancesToRemoveUnlockedOverrides: Set[Instance] = if (recalculatedInstances.contains(GLOBAL)) {
      recalculatedInstances ++ leastMaxBet.getInstanceValues.keys ++ mostMaxBet.getInstanceValues.keys
    } else {
      recalculatedInstances
    }

    val leastMaxBetWithoutOverridesForAffectedInstances = leastMaxBet.removeOverridesForInstances(instancesToRemoveUnlockedOverrides)
    val mostMaxBetWithoutOverridesForAffectedInstances = mostMaxBet.removeOverridesForInstances(instancesToRemoveUnlockedOverrides)
    val leastMaxBetValues = leastMaxBetWithoutOverridesForAffectedInstances.getInstanceValues
    val mostMaxBetValues = mostMaxBetWithoutOverridesForAffectedInstances.getInstanceValues

    val instancesToApplyLimits: Set[Instance] = if (recalculatedInstances.contains(GLOBAL)) {
      recalculatedInstances ++ leastMaxBetValues.keys ++ mostMaxBetValues.keys
    } else {
      recalculatedInstances
    }

    val (leastFieldUpdated, mostFieldUpdated) = instancesToApplyLimits
      .flatMap(instance => {
        for {
          leastTupleValue <- getInstanceValueFor(instance, leastMaxBetValues)
          mostTupleValue <- getInstanceValueFor(instance, mostMaxBetValues)
          leastValue <- leastTupleValue._2.value
          mostValue <- mostTupleValue._2.value
          if leastValue > mostValue
        } yield (instance, leastTupleValue, mostTupleValue)
      })
      .foldLeft((leastMaxBetWithoutOverridesForAffectedInstances, mostMaxBetWithoutOverridesForAffectedInstances))({
        case ((leastMaxBet, mostMaxBet), (instance, leastTupleValue, mostTupleValue)) =>
          (leastTupleValue, mostTupleValue) match {
            case ((leastInstance, InstanceValue(Some(leastValue), Flag.Lock | Flag.LockAndInherited)), (_, InstanceValue(_, _))) if leastInstance == instance =>
              (leastMaxBet, mostMaxBet.copy(overrides = mostMaxBet.overrides + (instance -> Unlocked(Some(leastValue)))))
            case (_, (mostInstance, InstanceValue(Some(mostValue), Flag.Lock | Flag.LockAndInherited))) if mostInstance == instance =>
              (leastMaxBet.copy(overrides = leastMaxBet.overrides + (instance -> Unlocked(Some(mostValue)))), mostMaxBet)
            case ((_, InstanceValue(Some(leastValue), _)), _) =>
              (leastMaxBet, mostMaxBet.copy(overrides = mostMaxBet.overrides + (instance -> Unlocked(Some(leastValue)))))
          }
      })

    (
      leastFieldUpdated.propagateInheritedDataToOverridesWhenAnOverrideToAllExists,
      mostFieldUpdated.propagateInheritedDataToOverridesWhenAnOverrideToAllExists
    )
  }


  private def getInstanceValueFor[A](instance: Instance, values: Map[Instance, InstanceValue[A]]): Option[(Instance, InstanceValue[A])] = {
    values.get(instance).map(v => (instance, v)).orElse(values.get(GLOBAL).map(v => (GLOBAL, v)))
  }

}
