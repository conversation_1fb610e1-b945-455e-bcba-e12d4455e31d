package com.flutter.pcsa.rd.actor.event.domain.dto

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.rd.actor.eventtype.domain.dto.RDOverrideDTO

object RDMarketOverrideAggregationDTO {

  def empty: RDMarketOverrideAggregationDTO = RDMarketOverrideAggregationDTO(
    market = RDMarketOverrideDTO.empty,
    selections = Map.empty[GbpId, RDSelectionOverrideDTO],
    marketTypeLinkIdentifier = GbpId.empty
  )
}

case class RDMarketOverrideAggregationDTO(
    market: RDMarketOverrideDTO,
    selections: Map[GbpId, RDSelectionOverrideDTO],
    marketTypeLinkIdentifier: GbpId) extends RDOverrideDTO
