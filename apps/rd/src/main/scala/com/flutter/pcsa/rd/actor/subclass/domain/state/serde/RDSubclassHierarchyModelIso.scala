package com.flutter.pcsa.rd.actor.subclass.domain.state.serde

import com.flutter.pcsa.common.state.model.serde.HierarchyLinkMetadataIso
import com.flutter.pcsa.rd.actor.subclass.domain.state.RDSubclassHierarchyModel
import com.flutter.pcsa.rd.actor.subclass.domain.state.proto.RDSubclassHierarchyModelProto
import monocle.Iso

trait RDSubclassHierarchyModelIso extends HierarchyLinkMetadataIso {

  val rdSubclassHierarchyModelIso = Iso[RDSubclassHierarchyModel, RDSubclassHierarchyModelProto] { im =>
    RDSubclassHierarchyModelProto(
      superclass = hierarchyLinkMetadataIso.get(im.superclass)
    )
  } { pb =>
    RDSubclassHierarchyModel(
      superclass = hierarchyLinkMetadataIso.reverseGet(pb.superclass)
    )
  }
}
