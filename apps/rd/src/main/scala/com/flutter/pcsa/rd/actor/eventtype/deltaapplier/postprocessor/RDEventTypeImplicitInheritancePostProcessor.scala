package com.flutter.pcsa.rd.actor.eventtype.deltaapplier.postprocessor

import com.flutter.baseactor.deltaapplier.postprocessor.ActorEventPostProcessor
import com.flutter.baseactor.deltaapplier.postprocessor.inheritance.ActorEntityImplicitInheritanceProcessor
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.RDEventTypeActorEvent
import com.flutter.pcsa.rd.actor.eventtype.domain.state.{RDEventTypeActorState, RDEventTypeAggregationModel, RDEventTypeModel, RDEventTypeNoState, RDEventTypeState, RDMarketTypeLinkModel}

class RDEventTypeImplicitInheritancePostProcessor(eventTypeToMarketTypeLinkImplicitInheritanceProcessor: ActorEntityImplicitInheritanceProcessor[RDEventTypeModel, RDMarketTypeLinkModel, RDEventTypeActorEvent])
  extends ActorEventPostProcessor[RDEventTypeActorState, RDEventTypeActorEvent]{

  override def apply(oldState: RDEventTypeActorState, newState: RDEventTypeActorState, actorEvent: RDEventTypeActorEvent): RDEventTypeActorState = {
    (oldState, newState) match {
      case (_, RDEventTypeNoState) => RDEventTypeNoState
      case (RDEventTypeState(_, _, oldEventTypeAggregation, _), newState @ RDEventTypeState(_, _, eventTypeAggregation, _)) =>
        newState.copy(
          eventTypeAggregation = applyImplicitInheritance(Some(oldEventTypeAggregation), eventTypeAggregation, actorEvent)
        )
      case (RDEventTypeNoState, newState @ RDEventTypeState(_, _, eventTypeAggregation, _)) =>
        newState.copy(
          eventTypeAggregation = applyImplicitInheritance(None, eventTypeAggregation, actorEvent)
        )
    }
  }

  private def applyImplicitInheritance(oldState: Option[RDEventTypeAggregationModel], newState: RDEventTypeAggregationModel, event: RDEventTypeActorEvent): RDEventTypeAggregationModel = {
    val oldEventTypeModel = oldState.map(_.eventType)

    newState.copy(
      marketTypeLinks = newState.marketTypeLinks.map({ case (id, marketTypeLink) =>
        val oldMarketTypeLinkModel = oldState.flatMap(_.marketTypeLinks.get(id))
        id -> marketTypeLink.applyUpdate(model =>
          eventTypeToMarketTypeLinkImplicitInheritanceProcessor(oldEventTypeModel.map(_.model), newState.eventType.model, oldMarketTypeLinkModel.map(_.model), model, event)
        )
      })
    )
  }
}
