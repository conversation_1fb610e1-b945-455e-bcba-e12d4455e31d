package com.flutter.pcsa.rd.common.actor.notifier.converters

import com.flutter.pcsa.common.state.model.datatypes.OverridableFeedField
import com.flutter.pcsa.common.state.model.datatypes.viewer.implicits.overridableFeedValueViewer.OverridableFeedValueViewerOps
import com.flutter.pcsa.rd.common.actor.notifier.converters.implicits.modelConverters.asOutboundFlag
import com.flutter.product.catalogue.common.BoolField.{BoolFieldValue, OptionalBool}
import com.flutter.product.catalogue.common.DoubleField.{DoubleFieldValue, OptionalDouble}
import com.flutter.product.catalogue.common.IntField.{IntFieldValue, OptionalInt}
import com.flutter.product.catalogue.common.LongField.{LongFieldValue, OptionalLong}
import com.flutter.product.catalogue.common.StringField.{OptionalString, StringFieldValue}
import com.flutter.product.catalogue.common.{<PERSON><PERSON><PERSON><PERSON>, <PERSON>Field, IntField, <PERSON><PERSON>ield, StringField}

import scala.language.implicitConversions

trait OverridableFeedField2InstructionOps {

  implicit def asStringField(overridableFeedField: OverridableFeedField[String]): StringField = {
    StringField(
      base = overridableFeedField.baseValues.map(kv => kv._1 -> OptionalString(kv._2)),
      instances = overridableFeedField.getInstanceValues.map(kv => kv._1 -> StringFieldValue(value = kv._2.value, flag = kv._2.flag))
    )
  }

  implicit def asIntField(overridableFeedField: OverridableFeedField[Int]): IntField = {
    IntField(
      base = overridableFeedField.baseValues.map(kv => kv._1 -> OptionalInt(kv._2)),
      instances = overridableFeedField.getInstanceValues.map(kv => kv._1 -> IntFieldValue(value = kv._2.value, flag = kv._2.flag))
    )
  }

  implicit def asDoubleField(overridableFeedField: OverridableFeedField[Double]): DoubleField = {
    DoubleField(
      base = overridableFeedField.baseValues.map(kv => kv._1 -> OptionalDouble(kv._2)),
      instances = overridableFeedField.getInstanceValues.map(kv => kv._1 -> DoubleFieldValue(value = kv._2.value, flag = kv._2.flag))
    )
  }
  implicit def asLongField(overridableFeedField: OverridableFeedField[Long]): LongField = {
    LongField(
      base = overridableFeedField.baseValues.map(kv => kv._1 -> OptionalLong(kv._2)),
      instances = overridableFeedField.getInstanceValues.map(kv => kv._1 -> LongFieldValue(value = kv._2.value, flag = kv._2.flag))
    )
  }

  implicit def asBoolField(overridableFeedField: OverridableFeedField[Boolean]): BoolField = {
    BoolField(
      base = overridableFeedField.baseValues.map(kv => kv._1 -> OptionalBool(kv._2)),
      instances = overridableFeedField.getInstanceValues.map(kv => kv._1 -> BoolFieldValue(value = kv._2.value, flag = kv._2.flag))
    )
  }
}
