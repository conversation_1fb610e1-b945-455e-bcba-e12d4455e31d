package com.flutter.pcsa.rd.common.actor.deltaapplier.feedcommand

import com.flutter.pcsa.common.state.model.model.{EntityMetadata, EntityModel, LiveEntityMetadata}
import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO

trait RDFeedCommandApplier {
  def applyFeedCommand[E <: EntityModel](entityMetadata: EntityMetadata[E], action: RDFeedActionDTO.Value, applyCommand: E => E): EntityMetadata[E] = {
    action match {
      case RDFeedActionDTO.CREATE | RDFeedActionDTO.REFRESH =>
        entityMetadata.copy(
          feedCreated = true,
          model = applyCommand(entityMetadata.model)
        )
      case _ =>
        entityMetadata.applyUpdate(applyCommand)
    }
  }

  def applyFeedCommand[E <: EntityModel](liveEntityMetadata: LiveEntityMetadata[E], action: RDFeedActionDTO.Value, applyCommand: E => E): LiveEntityMetadata[E] = {
    action match {
      case RDFeedActionDTO.CREATE | RDFeedActionDTO.REFRESH =>
        liveEntityMetadata.copy(
          feedCreated = true,
          model = applyCommand(liveEntityMetadata.model)
        )
      case _ =>
        liveEntityMetadata.applyUpdate(applyCommand)
    }
  }

}
