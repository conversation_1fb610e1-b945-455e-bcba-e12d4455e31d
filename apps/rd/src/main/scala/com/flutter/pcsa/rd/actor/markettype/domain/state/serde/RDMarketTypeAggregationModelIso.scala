package com.flutter.pcsa.rd.actor.markettype.domain.state.serde

import cats.implicits.catsSyntaxOptionId
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.rd.actor.markettype.domain.state.proto.RDMarketTypeAggregationModelProto
import com.flutter.pcsa.rd.actor.markettype.domain.state.{RDMarketTypeAggregationModel, RDMarketTypeModel}
import com.flutter.pcsa.rd.common.actor.domain.dto.state.RDGBPIdentifierModel
import monocle.Iso

trait RDMarketTypeAggregationModelIso extends RDMarketTypeModelIso with RDMarketTypeLinkModelIso {

  val rdMarketTypeAggregationModelIso: Iso[RDMarketTypeAggregationModel, RDMarketTypeAggregationModelProto] =
    Iso[RDMarketTypeAggregationModel, RDMarketTypeAggregationModelProto] { im =>
      RDMarketTypeAggregationModelProto(
        marketType = rdMarketTypeEntityMetadataModelIso.get(im.marketType).some,
        marketTypeLinks = im.marketTypeLinks.map({ case (_, marketTypeLink) => rdMarketTypeLinksEntityMetadataModelMapIso.get(marketTypeLink) }).toSeq
      )
    } { pb =>
      RDMarketTypeAggregationModel(
        marketType = pb.marketType
          .map(rdMarketTypeEntityMetadataModelIso.reverseGet)
          .getOrElse(EntityMetadata(RDMarketTypeModel.empty, feedCreated = false, hierarchyCreated = false)),
        marketTypeLinks = pb.marketTypeLinks
          .map(rdMarketTypeLinksEntityMetadataModelMapIso.reverseGet)
          .map(marketTypeLink => RDGBPIdentifierModel(marketTypeLink.identifier.asLong) -> marketTypeLink)
          .toMap
      )
    }
}
