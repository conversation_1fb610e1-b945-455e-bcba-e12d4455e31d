package com.flutter.pcsa.rd.actor.markettype.domain.dto

import com.flutter.pcsa.common.datatypes.dto.EntityDTO
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.datatypes.EntityIdentifiers
import com.flutter.pcsa.common.state.model.datatypes.FeedField
import com.flutter.pcsa.overrides.OverridesDTO
import com.flutter.pcsa.rd.actor.eventtype.domain.dto.{RDHierarchyDTO, RDOverrideDTO}

object RDMarketTypeDTO {

  val empty: RDMarketTypeDTO =
    RDMarketTypeDTO(
      identifier = GbpId.empty,
      entityIdentifiers = EntityIdentifiers(Map.empty),
      marketTypeName = None,
      subclassId = None,
      marketSort = None,
      birIndexStart = None,
      displayOrder = None,
      layToLosePercentage = None,
      leastMaxBetPercentage = None,
      mostMaxBetPercentage = None,
      liabilityLimitPercentage = None
    )
}

case class RDMarketTypeDTO(
                            identifier: GbpId,
                            entityIdentifiers: EntityIdentifiers,
                            marketTypeName: Option[String],
                            subclassId: Option[Int],
                            marketSort: Option[String],
                            birIndexStart: Option[Int],
                            displayOrder: Option[Int],
                            layToLosePercentage: Option[Int],
                            leastMaxBetPercentage: Option[Int],
                            mostMaxBetPercentage: Option[Int],
                            liabilityLimitPercentage: Option[Int],
                            // GBPE-9044 - MD emulation fix to unblock e2e tests
                            name: Option[String] = None
                          )
    extends EntityDTO with RDHierarchyDTO {

  override def isEmpty: Boolean = {
    this.copy(identifier = GbpId.empty) == RDMarketTypeDTO.empty
  }
}

object RDMarketTypeOverrideDTO {

  def empty: RDMarketTypeOverrideDTO =
    RDMarketTypeOverrideDTO(
      id = EntityIdentifiers.empty,
      marketTypeName = None,
      subclassId = None,
      marketSort = None,
      birIndexStart = None,
      displayOrder = None,
      liabiliyLimitPercentage = None
    )
}

case class RDMarketTypeOverrideDTO(
    id: EntityIdentifiers,
    marketTypeName: Option[OverridesDTO[String]],
    subclassId: Option[OverridesDTO[Int]],
    marketSort: Option[OverridesDTO[String]],
    birIndexStart: Option[OverridesDTO[Int]],
    displayOrder: Option[OverridesDTO[Int]],
    liabiliyLimitPercentage: Option[OverridesDTO[Int]])
    extends RDOverrideDTO
