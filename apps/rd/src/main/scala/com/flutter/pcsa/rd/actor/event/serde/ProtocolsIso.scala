package com.flutter.pcsa.rd.actor.event.serde

import cats.implicits.catsSyntaxOptionId
import com.flutter.baseactor.serialization.CommonRiskIso.headersIso
import com.flutter.pcsa.rd.actor.event.RDEventActor.{RDBatchInheritanceUpdateEvent, RDBatchInheritanceUpdateMarket, RDBatchInheritanceUpdateMarketFromMTL, RDEventFeedCommand, RDEventFeedCommandApplied, RDEventInheritanceUpdated, RDEventOverridden, RDEventOverride, RDInheritanceUpdateEvent, RDInheritanceUpdateMarket, RDInheritanceUpdateMarketFromMTL, RDMarketInheritanceUpdated, RDMarketInheritanceUpdatedFromMTL, RDMarketOverridden, RDMarketOverride}
import com.flutter.pcsa.rd.actor.event.commands.proto.{RDBatchInheritanceUpdateEventProto, RDBatchInheritanceUpdateMarketFromMTLProto, RD<PERSON>atchInheritanceUpdateMarketProto, RDEventFeedCommandProto, RDEventOverrideProto, RDInheritanceUpdateEventProto, RDInheritanceUpdateMarketFromMTLProto, RDInheritanceUpdateMarketProto, RDMarketOverrideProto}
import com.flutter.pcsa.rd.actor.event.domain.dto.serde.{RDEventDTOIso, RDEventHierarchyDTOIso, RDEventOverrideDTOIso, RDMarketFeedCommandIso, RDMarketOverrideAggregationIso}
import com.flutter.pcsa.rd.actor.event.domain.dto.{RDEventDTO, RDEventHierarchyDTO, RDEventOverrideDTO, RDMarketOverrideAggregationDTO}
import com.flutter.pcsa.rd.actor.event.events.proto.{RDEventFeedCommandAppliedProto, RDEventInheritanceUpdatedProto, RDEventOverriddenProto, RDMarketInheritanceUpdatedFromMTLProto, RDMarketInheritanceUpdatedProto, RDMarketOverriddenProto}
import com.flutter.pcsa.rd.common.domain.dto.serde.RDFeedActionDTOIso
import com.flutter.retryableactor.MetadataProtocolsIso
import com.flutter.retryableactor.metadata.Metadata.KafkaMetadata
import monocle.Iso

object ProtocolsIso
    extends RDEventDTOIso with RDMarketFeedCommandIso with RDEventHierarchyDTOIso with RDFeedActionDTOIso with RDEventOverrideDTOIso
    with RDMarketOverrideAggregationIso with MetadataProtocolsIso {

  val rdEventFeedCommandAppliedIso = Iso[RDEventFeedCommandApplied, RDEventFeedCommandAppliedProto] { im =>
    RDEventFeedCommandAppliedProto(
      headers = im.headers,
      action = rdFeedActionDTOIso.get(im.action),
      eventHierarchy = rdEventHierarchyDTOIso.get(im.eventHierarchy).some,
      event = rdEventDTOIso.get(im.event).some,
      markets = im.markets.values.map(rdMarketFeedCommandIso.get).toSeq,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDEventFeedCommandApplied(
      headers = pb.headers,
      action = rdFeedActionDTOIso.reverseGet(pb.action),
      eventHierarchy = pb.eventHierarchy.map(rdEventHierarchyDTOIso.reverseGet).getOrElse(RDEventHierarchyDTO.empty),
      event = pb.event.map(rdEventDTOIso.reverseGet).getOrElse(RDEventDTO.empty),
      markets = pb.markets
        .map(marketFeedCommandProto => {
          val marketFeedCommand = rdMarketFeedCommandIso.reverseGet(marketFeedCommandProto)
          marketFeedCommand.market.identifier -> marketFeedCommand
        })
        .toMap,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdEventOverrideIso = Iso[RDEventOverride, RDEventOverrideProto] { im =>
    RDEventOverrideProto(
      headers = im.headers,
      identifier = im.identifier,
      eventDTO = rdEventOverrideDTOIso.get(im.eventDTO).some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDEventOverride(
      headers = pb.headers,
      identifier = pb.identifier,
      eventDTO = pb.eventDTO.map(rdEventOverrideDTOIso.reverseGet).getOrElse(RDEventOverrideDTO.empty),
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdMarketOverrideIso = Iso[RDMarketOverride, RDMarketOverrideProto] { im =>
    RDMarketOverrideProto(
      headers = im.headers,
      identifier = im.identifier,
      marketAgg = rdMarketOverrideAggregationDTOIso.get(im.marketAgg).some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDMarketOverride(
      headers = pb.headers,
      identifier = pb.identifier,
      marketAgg = pb.marketAgg.map(rdMarketOverrideAggregationDTOIso.reverseGet).getOrElse(RDMarketOverrideAggregationDTO.empty),
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdEventFeedCommandIso = Iso[RDEventFeedCommand, RDEventFeedCommandProto] { im =>
    RDEventFeedCommandProto(
      headers = im.headers,
      action = rdFeedActionDTOIso.get(im.action),
      eventHierarchy = rdEventHierarchyDTOIso.get(im.eventHierarchy).some,
      event = rdEventDTOIso.get(im.event).some,
      markets = im.markets.values.map(rdMarketFeedCommandIso.get).toSeq,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDEventFeedCommand(
      headers = pb.headers,
      action = rdFeedActionDTOIso.reverseGet(pb.action),
      eventHierarchy = pb.eventHierarchy.map(rdEventHierarchyDTOIso.reverseGet).getOrElse(RDEventHierarchyDTO.empty),
      event = pb.event.map(rdEventDTOIso.reverseGet).getOrElse(RDEventDTO.empty),
      markets = pb.markets
        .map(marketFeedCommandProto => {
          val marketFeedCommand = rdMarketFeedCommandIso.reverseGet(marketFeedCommandProto)
          marketFeedCommand.market.identifier -> marketFeedCommand
        })
        .toMap,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdBatchInheritanceUpdateEventIso = Iso[RDBatchInheritanceUpdateEvent, RDBatchInheritanceUpdateEventProto] { im =>
    RDBatchInheritanceUpdateEventProto(
      headers = headersIso.reverseGet(im.headers),
      identifiers = im.identifiers,
      notification = im.notification.some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDBatchInheritanceUpdateEvent(
      headers = pb.headers,
      identifiers = pb.identifiers.toList,
      notification = pb.getNotification,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdInheritanceUpdateEventIso = Iso[RDInheritanceUpdateEvent, RDInheritanceUpdateEventProto] { im =>
    RDInheritanceUpdateEventProto(
      headers = headersIso.reverseGet(im.headers),
      identifier = im.identifier,
      notification = im.notification.some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDInheritanceUpdateEvent(
      headers = pb.headers,
      identifier = pb.identifier,
      notification = pb.getNotification,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdBatchInheritanceUpdateMarketIso = Iso[RDBatchInheritanceUpdateMarket, RDBatchInheritanceUpdateMarketProto] { im =>
    RDBatchInheritanceUpdateMarketProto(
      headers = headersIso.reverseGet(im.headers),
      identifiers = im.identifiers,
      notification = im.notification.some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDBatchInheritanceUpdateMarket(
      headers = pb.headers,
      identifiers = pb.identifiers.toList,
      notification = pb.getNotification,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdInheritanceUpdateMarketIso = Iso[RDInheritanceUpdateMarket, RDInheritanceUpdateMarketProto] { im =>
    RDInheritanceUpdateMarketProto(
      headers = headersIso.reverseGet(im.headers),
      eventIdentifier = im.eventIdentifier,
      identifier = im.identifier,
      notification = im.notification.some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDInheritanceUpdateMarket(
      headers = pb.headers,
      eventIdentifier = pb.eventIdentifier,
      identifier = pb.identifier,
      notification = pb.getNotification,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdBatchInheritanceUpdateMarketFromMTLIso = Iso[RDBatchInheritanceUpdateMarketFromMTL, RDBatchInheritanceUpdateMarketFromMTLProto] { im =>
    RDBatchInheritanceUpdateMarketFromMTLProto(
      headers = headersIso.reverseGet(im.headers),
      identifiers = im.identifiers,
      notification = im.notification.some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDBatchInheritanceUpdateMarketFromMTL(
      headers = pb.headers,
      identifiers = pb.identifiers.toList,
      notification = pb.getNotification,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdInheritanceUpdateMarketFromMTLIso = Iso[RDInheritanceUpdateMarketFromMTL, RDInheritanceUpdateMarketFromMTLProto] { im =>
    RDInheritanceUpdateMarketFromMTLProto(
      headers = headersIso.reverseGet(im.headers),
      eventIdentifier = im.eventIdentifier,
      identifier = im.identifier,
      notification = im.notification.some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDInheritanceUpdateMarketFromMTL(
      headers = pb.headers,
      eventIdentifier = pb.eventIdentifier,
      identifier = pb.identifier,
      notification = pb.getNotification,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdEventInheritanceUpdatedIso = Iso[RDEventInheritanceUpdated, RDEventInheritanceUpdatedProto] { im =>
    RDEventInheritanceUpdatedProto(
      identifier = im.identifier,
      headers = headersIso.reverseGet(im.headers),
      notification = im.notification.some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDEventInheritanceUpdated(
      identifier = pb.identifier,
      headers = pb.headers,
      notification = pb.getNotification,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdMarketInheritanceUpdatedIso = Iso[RDMarketInheritanceUpdated, RDMarketInheritanceUpdatedProto] { im =>
    RDMarketInheritanceUpdatedProto(
      identifier = im.identifier,
      headers = headersIso.reverseGet(im.headers),
      notification = im.notification.some,
      eventIdentifier = im.eventIdentifier,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDMarketInheritanceUpdated(
      headers = pb.headers,
      identifier = pb.identifier,
      notification = pb.getNotification,
      eventIdentifier = pb.eventIdentifier,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdMarketInheritanceUpdatedFromMTLIso = Iso[RDMarketInheritanceUpdatedFromMTL, RDMarketInheritanceUpdatedFromMTLProto] { im =>
    RDMarketInheritanceUpdatedFromMTLProto(
      identifier = im.identifier,
      headers = headersIso.reverseGet(im.headers),
      notification = im.notification.some,
      eventIdentifier = im.eventIdentifier,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDMarketInheritanceUpdatedFromMTL(
      headers = pb.headers,
      identifier = pb.identifier,
      notification = pb.getNotification,
      eventIdentifier = pb.eventIdentifier,
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdEventOverriddenIso = Iso[RDEventOverridden, RDEventOverriddenProto] { im =>
    RDEventOverriddenProto(
      identifier = im.identifier,
      headers = headersIso.reverseGet(im.headers),
      eventDTO = rdEventOverrideDTOIso.get(im.event).some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDEventOverridden(
      identifier = pb.identifier,
      headers = pb.headers,
      event = pb.eventDTO.map(rdEventOverrideDTOIso.reverseGet).getOrElse(RDEventOverrideDTO.empty),
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }

  val rdMarketOverriddenIso = Iso[RDMarketOverridden, RDMarketOverriddenProto] { im =>
    RDMarketOverriddenProto(
      identifier = im.identifier,
      headers = headersIso.reverseGet(im.headers),
      marketAgg = rdMarketOverrideAggregationDTOIso.get(im.marketAgg).some,
      metadata = kafkaMetadataIso.get(im.metadata).some
    )
  } { pb =>
    RDMarketOverridden(
      identifier = pb.identifier,
      headers = pb.headers,
      marketAgg = pb.marketAgg.map(rdMarketOverrideAggregationDTOIso.reverseGet).getOrElse(RDMarketOverrideAggregationDTO.empty),
      metadata = pb.metadata.map(kafkaMetadataIso.reverseGet).getOrElse(KafkaMetadata.empty)
    )
  }
}
