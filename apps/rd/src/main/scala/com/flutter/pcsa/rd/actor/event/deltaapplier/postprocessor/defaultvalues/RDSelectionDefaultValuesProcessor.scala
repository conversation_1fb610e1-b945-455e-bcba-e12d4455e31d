package com.flutter.pcsa.rd.actor.event.deltaapplier.postprocessor.defaultvalues

import com.flutter.baseactor.deltaapplier.postprocessor.defaultvalues.ActorEntityDefaultValuesProcessor
import com.flutter.pcsa.common.state.model.datatypes.syntax.setters.implicits.setFeedValueSyntax.SetFeedValue
import com.flutter.pcsa.rd.actor.event.domain.state.RDSelectionModel

class RDSelectionDefaultValuesProcessor extends ActorEntityDefaultValuesProcessor[RDSelectionModel] {

  override def apply(selection: RDSelectionModel): RDSelectionModel = {
    selection.copy(
      riskInfo = selection.riskInfo.applyDefault("UNSET")
    )
  }
}
