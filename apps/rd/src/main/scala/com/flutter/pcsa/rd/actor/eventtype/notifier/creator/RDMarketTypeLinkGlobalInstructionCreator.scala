package com.flutter.pcsa.rd.actor.eventtype.notifier.creator

import com.flutter.baseactor.notifier.creator.StateDeltaSyntax.StateDelta
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.{RDEventTypeActorEvent, RDEventTypeFeedCommandApplied, RDFlushActorEventType}
import com.flutter.pcsa.rd.actor.eventtype.domain.state.{RDEventTypeHierarchyModel, RDEventTypeModel, RDMarketTypeLinkModel}
import com.flutter.pcsa.rd.common.actor.notifier.converters.implicits.modelConverters.{asDoubleField, asIntField}
import com.flutter.pcsa.rd.common.actor.notifier.creator.RDFeedActionSyntax.rdFeedAction2GlobalInstructionAction
import com.flutter.product.catalogue.common.{DoubleField, IntField}
import com.flutter.product.catalogue.global.risk.GlobalRiskInstruction.Action
import com.flutter.product.catalogue.global.risk.{GlobalRiskInstruction, MarketTypeLink}

class RDMarketTypeLinkGlobalInstructionCreator {

  def createInstruction(eventType: RDEventTypeModel,
                        previousMarketTypeLink: Option[EntityMetadata[RDMarketTypeLinkModel]],
                        currentMarketTypeLink: EntityMetadata[RDMarketTypeLinkModel],
                        eventTypeHierarchyModel: RDEventTypeHierarchyModel,
                        actorEvent: RDEventTypeActorEvent): Option[GlobalRiskInstruction] = {

    if (currentMarketTypeLink.isValid) {
      val wasPreviousValid = previousMarketTypeLink.exists(_.isValid)
      val action: Action = actorEvent match {
        case _: RDFlushActorEventType =>
          Action.FLUSH
        case _ if !wasPreviousValid && currentMarketTypeLink.isValid =>
          Action.CREATE
        case feedInstruction: RDEventTypeFeedCommandApplied =>
          feedInstruction
            .marketTypeLinks
            .get(currentMarketTypeLink.identifier)
            .map(_.action)
            .map(rdFeedAction2GlobalInstructionAction)
            .getOrElse(Action.UPDATE)
        case _ =>
          Action.UPDATE
      }

      createMarketTypeLinkEntity(eventType, previousMarketTypeLink.filter(_.isValid).filterNot(_ => action == Action.REFRESH).map(_.model), currentMarketTypeLink.model, eventTypeHierarchyModel).map(entity =>
        GlobalRiskInstruction(
          action = action,
          entity = entity
        )
      )
    } else {
      None
    }
  }

  private def createMarketTypeLinkEntity(eventType: RDEventTypeModel,
                                         previousMarketTypeLink: Option[RDMarketTypeLinkModel],
                                         currentMarketTypeLink: RDMarketTypeLinkModel,
                                         eventTypeHierarchyModel: RDEventTypeHierarchyModel): Option[GlobalRiskInstruction.Entity.MarketTypeLink] = {

    previousMarketTypeLink.filter(_ == currentMarketTypeLink).map(_ => None).getOrElse({
      val inPlayBettingDelay: Option[IntField] = (previousMarketTypeLink, currentMarketTypeLink) ? (_.inPlayBettingDelay)
      val layToLosePerc: Option[IntField] = (previousMarketTypeLink, currentMarketTypeLink) ? (_.layToLosePerc)
      val leastMaxBetPerc: Option[IntField] = (previousMarketTypeLink, currentMarketTypeLink) ? (_.leastMaxBetPerc)
      val mostMaxBetPerc: Option[IntField] = (previousMarketTypeLink, currentMarketTypeLink) ? (_.mostMaxBetPerc)
      val stakeFactor: Option[DoubleField] = (previousMarketTypeLink, currentMarketTypeLink) ? (_.stakeFactor)

      if (Seq(inPlayBettingDelay, layToLosePerc, leastMaxBetPerc, mostMaxBetPerc, stakeFactor).exists(_.nonEmpty)) {
        val marketTypeLink = MarketTypeLink(
          id = currentMarketTypeLink.identifier.asLong,
          eventTypeId = eventType.identifier.asLong,
          mappedIds = Map.empty,
          subclassId = eventTypeHierarchyModel.subclass.identifier.asLong,
          superclassId = eventTypeHierarchyModel.superclass.identifier.asLong,

          inPlayBettingDelay = inPlayBettingDelay,
          layToLosePerc = layToLosePerc,
          leastMaxBetPerc = leastMaxBetPerc,
          mostMaxBetPerc = mostMaxBetPerc,
          stakeFactor = stakeFactor
        )
        Some(GlobalRiskInstruction.Entity.MarketTypeLink(marketTypeLink))
      } else {
        None
      }
    })
  }
}
