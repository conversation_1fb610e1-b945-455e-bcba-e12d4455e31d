package com.flutter.pcsa.rd.actor.superclass.domain.state.serde

import cats.implicits.catsSyntaxOptionId
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.rd.actor.superclass.domain.state.proto.RDSuperclassAggregationModelProto
import com.flutter.pcsa.rd.actor.superclass.domain.state.{RDSuperclassAggregationModel, RDSuperclassModel}
import monocle.Iso

trait RDSuperclassAggregationModelIso extends RDSuperclassModelIso {

  val rdSuperclassAggregationModelIso: Iso[RDSuperclassAggregationModel, RDSuperclassAggregationModelProto] =
    Iso[RDSuperclassAggregationModel, RDSuperclassAggregationModelProto] { im =>
      RDSuperclassAggregationModelProto(
        superclass = rdSuperclassEntityMetadataModelIso.get(im.superclass).some
      )
    } { pb =>
      RDSuperclassAggregationModel(
        superclass = pb.superclass.map(rdSuperclassEntityMetadataModelIso.reverseGet).getOrElse(EntityMetadata(RDSuperclassModel.empty, feedCreated = false, hierarchyCreated = false))
      )
    }

}
