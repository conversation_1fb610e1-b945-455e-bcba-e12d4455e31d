package com.flutter.pcsa.rd.adapter.gth

import cats.data.Ior
import com.flutter.adapter.{InboundKafkaMetadata, InboundMetadata, IorConverter}
import com.flutter.baseactor.BaseActor.ActorCommand
import com.flutter.headers.HeaderConstants.{DEST_MGM, GPD, POWERS_FEED}
import com.flutter.headers.{PROVIDER, ROOT_SOURCE_TIMESTAMP, SOURCE_APP, SOURCE_TIMESTAMP, START_TIMESTAMP, USERNAME}
import com.flutter.infra.kafka.consumer.KafkaUtils.extractHeaders
import com.flutter.infra.kafka.converter.Converter
import com.flutter.infra.kafka.streams.kafka.{KafkaMessageInterpreterException, KafkaMessageValidationFailures}
import com.flutter.logger.CommandDataLogger
import com.flutter.observability.profile.ProfileTechnicalMetricsCollector
import com.flutter.pcsa.rd.RD
import com.flutter.pcsa.rd.adapter.gth.convert.{GTHInstruction, GTHInstructionSerializer}
import com.flutter.pcsa.rd.common.observability.AddHeadersToCommandHelper.addHeadersToActorCommand
import org.apache.kafka.clients.consumer.ConsumerRecord

import scala.util.{Failure, Success}

case class GTHMessage(headers: Map[String, String], payload: GTHInstruction)

class RDGTHMessage2ActorCommand(converter: IorConverter[InboundMetadata[GTHMessage], ActorCommand])
    extends Converter[ConsumerRecord[String, String], ActorCommand] with GTHInstructionSerializer with CommandDataLogger[String, String] {
  this: ProfileTechnicalMetricsCollector[ActorCommand] =>
  override def convert(inbound: ConsumerRecord[String, String]): Ior[KafkaMessageValidationFailures, ActorCommand] = {

    val inboundMetadata = inboundMetadataInstruction(inbound)
    val commandIor = converter.convert(inboundMetadata).map(cmd => enrichCommandWithHeaders(cmd, inbound))
    logCommandData("GTH", inbound, commandIor)

    commandIor
  }

  private def enrichCommandWithHeaders(cmd: ActorCommand, cr: ConsumerRecord[String, String]): ActorCommand = {
    val metrics = getProfileTechnicalMetricsHeaders(cmd)
    val headers = extractHeaders(cr.headers()) ++ cmd.headers ++ Map(
      ROOT_SOURCE_TIMESTAMP -> System.currentTimeMillis().toString, //TODO: extract header from right place (for now it does not exists)
      SOURCE_TIMESTAMP(RD.ServiceName) -> cr.timestamp().toString,
      START_TIMESTAMP(RD.ServiceName) -> System.currentTimeMillis().toString,
      SOURCE_APP -> GPD,
      USERNAME -> DEST_MGM,
      PROVIDER -> POWERS_FEED
    ) ++ metrics
    addHeadersToActorCommand(cmd, headers)
  }

  private def inboundMetadataInstruction(income: ConsumerRecord[String, String]): Ior[KafkaMessageValidationFailures, InboundMetadata[GTHMessage]] = {
    deserialize(income.value()) match {
      case Failure(exception) => Ior.left(KafkaMessageInterpreterException(List(s"Unable to parse GTH Instruction - $exception")))
      case Success(value) =>
        val inboundMetadata = InboundMetadata(
          metadata = InboundKafkaMetadata(
            topic = income.topic(),
            partition = income.partition(),
            offset = income.offset()
          ),
          instruction = GTHMessage(extractHeaders(income.headers()), value)
        )
        Ior.right(inboundMetadata)
    }
  }

}
