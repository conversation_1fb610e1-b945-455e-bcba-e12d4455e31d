package com.flutter.pcsa.rd.actor.subclass.domain.dto

import com.flutter.pcsa.common.datatypes.gbpid.GbpId


object RDSubclassDTO {
  val empty: RDSubclassDTO = RDSubclassDTO(
    identifier = GbpId.empty,
    superclassId = GbpId.empty,
    minForecastStakeLimit = None,
    minTricastStakeLimit = None,
    forecastStakeFactor = None,
    tricastStakeFactor = None,
    name = None
  )
}

case class RDSubclassDTO(identifier: GbpId,
                         superclassId: GbpId,
                         minForecastStakeLimit: Option[Int],
                         minTricastStakeLimit: Option[Int],
                         forecastStakeFactor: Option[Double],
                         tricastStakeFactor: Option[Double],
                         // GBPE-9044: MD Fields for E2E testing unblocking. Default `None` added here to facilitate development
                         name: Option[String]) {

  def isEmpty: Boolean = {
    this.copy() == RDSubclassDTO.empty
  }

}