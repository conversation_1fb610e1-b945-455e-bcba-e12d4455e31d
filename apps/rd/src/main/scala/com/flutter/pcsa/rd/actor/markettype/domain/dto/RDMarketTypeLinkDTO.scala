package com.flutter.pcsa.rd.actor.markettype.domain.dto

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.datatypes.EntityIdentifiers
import com.flutter.pcsa.overrides.OverridesDTO
import com.flutter.pcsa.rd.actor.eventtype.domain.dto.RDOverrideDTO

object RDMarketTypeLinkDTO {

  def empty: RDMarketTypeLinkDTO =
    RDMarketTypeLinkDTO(
      identifier = GbpId.empty,
      entityIdentifiers = EntityIdentifiers(Map.empty),
      eventTypeId = GbpId.empty,
      marketTypeId = GbpId.empty,
      stakeFactor = None,
      layToLosePerc = None,
      leastMaxBetPerc = None,
      mostMaxBetPerc = None,
      inPlayBettingDelay = None,
      liabilityLimitPerc = None
    )
}

case class RDMarketTypeLinkDTO(
                                identifier: GbpId,
                                eventTypeId: GbpId,
                                marketTypeId: GbpId,
                                entityIdentifiers: EntityIdentifiers,
                                stakeFactor: Option[Double],
                                layToLosePerc: Option[Int],
                                leastMaxBetPerc: Option[Int],
                                mostMaxBetPerc: Option[Int],
                                inPlayBettingDelay: Option[Int],
                                liabilityLimitPerc: Option[Int]) {
  def isEmpty: Boolean = {
    this.copy(
      identifier = GbpId.empty
    ) == RDMarketTypeLinkDTO.empty
  }
}

object RDMarketTypeLinkOverrideDTO {

  def empty: RDMarketTypeLinkOverrideDTO =
    RDMarketTypeLinkOverrideDTO(
      id = EntityIdentifiers.empty,
      eventTypeId = EntityIdentifiers.empty,
      marketTypeId = EntityIdentifiers.empty,
      subclassId = EntityIdentifiers.empty,
      superclassId = EntityIdentifiers.empty,
      layToLosePerc = None,
      leastMaxBetPerc = None,
      mostMaxBetPerc = None,
      stakeFactor = None,
      inPlayBettingDelay = None
    )
}

case class RDMarketTypeLinkOverrideDTO(
                                        id: EntityIdentifiers,
                                        eventTypeId: EntityIdentifiers,
                                        marketTypeId: EntityIdentifiers,
                                        subclassId: EntityIdentifiers,
                                        superclassId: EntityIdentifiers,
                                        layToLosePerc: Option[OverridesDTO[Int]],
                                        leastMaxBetPerc: Option[OverridesDTO[Int]],
                                        mostMaxBetPerc: Option[OverridesDTO[Int]],
                                        stakeFactor: Option[OverridesDTO[Double]],
                                        inPlayBettingDelay: Option[OverridesDTO[Int]]) extends RDOverrideDTO

