package com.flutter.pcsa.rd.actor.markettype.domain.dto.serde

import com.flutter.pcsa.common.datatypes
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.serde.EnumerationsIso.entityIdentifiersIso
import com.flutter.pcsa.rd.actor.markettype.domain.dto.RDMarketTypeDTO
import com.flutter.pcsa.rd.actor.markettype.domain.dto.proto.RDMarketTypeDTOProto
import monocle.Iso

trait RDMarketTypeDTOIso {

  val rdMarketTypeDTOIso: Iso[RDMarketTypeDTO, RDMarketTypeDTOProto] = Iso[RDMarketTypeDTO, RDMarketTypeDTOProto] { dto =>
    RDMarketTypeDTOProto(
      identifier = dto.identifier.asLong,
      identifiers = entityIdentifiersIso.get(dto.entityIdentifiers.identifiers),
      marketTypeName = dto.marketTypeName,
      subclassId = dto.subclassId,
      marketSort = dto.marketSort,
      birIndexStart = dto.birIndexStart,
      displayOrder = dto.displayOrder,
      layToLosePercentage = dto.layToLosePercentage,
      leastMaxBetPercentage = dto.leastMaxBetPercentage,
      mostMaxBetPercentage = dto.mostMaxBetPercentage,
      liabilityLimitPercentage = dto.liabilityLimitPercentage,
      name = dto.name
    )
  } { proto =>
    RDMarketTypeDTO(
      identifier = GbpId(proto.identifier),
      entityIdentifiers = datatypes.EntityIdentifiers(entityIdentifiersIso.reverseGet(Map.from(proto.identifiers))),
      marketTypeName = proto.marketTypeName,
      subclassId = proto.subclassId,
      marketSort = proto.marketSort,
      birIndexStart = proto.birIndexStart,
      displayOrder = proto.displayOrder,
      layToLosePercentage = proto.layToLosePercentage,
      leastMaxBetPercentage = proto.leastMaxBetPercentage,
      mostMaxBetPercentage = proto.mostMaxBetPercentage,
      liabilityLimitPercentage = proto.liabilityLimitPercentage,
      name = proto.name
    )
  }

}
