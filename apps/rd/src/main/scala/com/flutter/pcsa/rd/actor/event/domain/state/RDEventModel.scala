package com.flutter.pcsa.rd.actor.event.domain.state

import com.flutter.pcsa.common.datatypes.{EntityIdentifiers, Platform}
import com.flutter.pcsa.common.state.model.datatypes.{FeedField, OverridableAndInheritableFeedField, OverridableFeedField}
import com.flutter.pcsa.common.state.model.model.EntityModel

object RDEventModel {

  val empty: RDEventModel =
    RDEventModel(
      entityIdentifiers = EntityIdentifiers(Map.empty),
      leastMaxBet = OverridableFeedField.empty[Double],
      mostMaxBet = OverridableFeedField.empty[Double],
      layToLose = OverridableFeedField.empty[Double],
      inPlayBettingDelay = OverridableAndInheritableFeedField.empty[Int],
      sort = OverridableFeedField.empty[String],
      liabilityLimit = FeedField.empty[Double],
      name = OverridableFeedField.empty[String],
      status = OverridableFeedField.empty[String],
      startTime = OverridableFeedField.empty[Long]
    )
}

case class RDEventModel(
    entityIdentifiers: EntityIdentifiers,
    leastMaxBet: OverridableFeedField[Double],
    mostMaxBet: OverridableFeedField[Double],
    layToLose: OverridableFeedField[Double],
    inPlayBettingDelay: OverridableAndInheritableFeedField[Int],
    sort: OverridableFeedField[String],
    liabilityLimit: FeedField[Double],
    // GBPE-9044: MD Fields for E2E testing unblocking. Defaults added here to facilitate development
    name: OverridableFeedField[String],
    status: OverridableFeedField[String] ,
    startTime: OverridableFeedField[Long]) extends EntityModel {

  def isEmpty: Boolean = {
    this.copy(
      entityIdentifiers = EntityIdentifiers(entityIdentifiers.identifiers - Platform.Gbp)
    ) == RDEventModel.empty
  }
}
