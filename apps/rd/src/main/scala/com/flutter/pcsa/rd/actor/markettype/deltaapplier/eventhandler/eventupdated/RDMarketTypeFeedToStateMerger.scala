package com.flutter.pcsa.rd.actor.markettype.deltaapplier.eventhandler.eventupdated

import com.flutter.pcsa.common.eventupdated.EntityIdentifiersMerger
import com.flutter.pcsa.rd.actor.markettype.domain.state.RDMarketTypeModel
import com.flutter.pcsa.common.state.model.datatypes.syntax.setters.implicits.setFeedValueSyntax.SetFeedValue
import com.flutter.pcsa.rd.actor.markettype.domain.dto.RDMarketTypeDTO

trait RDMarketTypeFeedToStateMerger extends EntityIdentifiersMerger {

  def applyFeedUpdate(state: RDMarketTypeModel, update: RDMarketTypeDTO): RDMarketTypeModel = {
    state.copy(
      entityIdentifiers = merge(state.entityIdentifiers, update.entityIdentifiers),
      marketTypeName = state.marketTypeName.updateFeedValue(update.marketTypeName),
      subclassId = state.subclassId.updateFeedValue(update.subclassId),
      marketSort = state.marketSort.updateFeedValue(update.marketSort),
      birIndexStart = state.birIndexStart.updateFeedValue(update.birIndexStart),
      displayOrder = state.displayOrder.updateFeedValue(update.displayOrder),
      layToLosePercentage = state.layToLosePercentage.updateFeedValue(update.layToLosePercentage),
      leastMaxBetPercentage = state.leastMaxBetPercentage.updateFeedValue(update.leastMaxBetPercentage),
      mostMaxBetPercentage = state.mostMaxBetPercentage.updateFeedValue(update.mostMaxBetPercentage),
      liabilityLimitPercentage = state.liabilityLimitPercentage.updateFeedValue(update.liabilityLimitPercentage),
      name = state.name.updateFeedValue(update.name)
    )
  }

}
