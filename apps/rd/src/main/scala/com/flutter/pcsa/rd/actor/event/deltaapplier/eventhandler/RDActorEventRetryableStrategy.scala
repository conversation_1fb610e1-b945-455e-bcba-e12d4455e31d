package com.flutter.pcsa.rd.actor.event.deltaapplier.eventhandler

import com.flutter.pcsa.rd.actor.event.RDEventActor.{RDEventActorCommand, RDEventActorEvent, RDFlushActorEvent}
import com.flutter.pcsa.rd.actor.event.domain.state.{RDEventActorState, RDEventState}
import com.flutter.retryableactor.behaviour.strategy.RetryableStrategy
import com.flutter.retryableactor.metadata.MetadataUtils
import com.typesafe.config.Config

class RDActorEventRetryableStrategy(override val actorConfig: Config) extends RetryableStrategy[RDEventActorState, RDEventActorCommand, RDEventActorEvent] {

  override def applyMetadataToState(state: RDEventActorState, actorEvent: RDEventActorEvent): RDEventActorState = state match {
    case eventState: RDEventState => eventState.copy(metadata = MetadataUtils.getKafkaMetadataMap(eventState, actorEvent))
    case _ => state
  }

  override def getRefreshEvent(actorEvent: RDEventActorEvent): RDEventActorEvent = RDFlushActorEvent(headers = actorEvent.headers)
}
