package com.flutter.pcsa.rd.actor.event.deltaapplier.eventhandler

import com.flutter.sharedplatforms.LazyLogging
import com.flutter.baseactor.deltaapplier.eventhandler.ActorEventHandler
import com.flutter.baseactor.domain.state.SequenceNumber
import com.flutter.pcsa.common.datatypes.gbpid.{GbpId, GbpIdHierarchyLevel}
import com.flutter.pcsa.common.state.model.model.{HierarchyLinkMetadata, LiveEntityMetadata}
import com.flutter.pcsa.rd.actor.event.RDEventActor.{RDEventActorEvent, RDEventFeedCommandApplied}
import com.flutter.pcsa.rd.actor.event.deltaapplier.eventhandler.eventupdated.{RDEventFeedToStateMerger, RDEventHierarchyFeedToStateMerger, RDMarketsFeedToStateMerger}
import com.flutter.pcsa.rd.actor.event.domain.state._
import com.flutter.pcsa.rd.common.actor.deltaapplier.feedcommand.RDFeedCommandApplier
import com.flutter.pcsa.rd.common.actor.domain.dto.state.RDGBPIdentifierModel

import scala.util.Try

import com.flutter.pcsa.common.datatypes.gbpid.GbpId._
import com.flutter.adapter.IorConverter._

object RDEventFeedCommandAppliedHandler
    extends ActorEventHandler[RDEventActorState, RDEventActorEvent] with RDEventFeedToStateMerger with RDMarketsFeedToStateMerger
    with RDEventHierarchyFeedToStateMerger with RDFeedCommandApplier with LazyLogging {

  override def handle(): Handler = {
    case (RDEventNoState, event: RDEventFeedCommandApplied) =>
      applyEventToState(SequenceNumber(0), SequenceNumber(0) + 1, RDEventAggregationModel.empty, event)
    case (state: RDEventState, event: RDEventFeedCommandApplied) =>
      applyEventToState(state.lastPublishedNo, state.inboundSeqNo + 1, state.aggregateRiskEvent, event)
  }

  private def applyEventToState(
      lastPublishedNo: SequenceNumber,
      inboundSeqNo: SequenceNumber,
      stateEventAggregation: RDEventAggregationModel,
      actorEvent: RDEventFeedCommandApplied
    ): RDEventState = {

    val eventUpdate = actorEvent.event
    val marketsUpdate = actorEvent.markets
    val eventHierarchyUpdate = actorEvent.eventHierarchy

    val stateMarkets = stateEventAggregation.markets
    val stateEventHierarchy = stateEventAggregation.eventHierarchy

    val eventTypeHierarchyUpdated = applyFeedUpdate(stateEventHierarchy, eventHierarchyUpdate)

    val marketsUpdated = if (marketsUpdate.nonEmpty) {
      val marketsUpdated = applyFeedUpdateToMarkets(stateMarkets, marketsUpdate)
      val updatedMarketIds = marketsUpdate.values.map(identifier => RDGBPIdentifierModel(identifier.market.identifier.asLong))
      enrichMarketsWithMarketTypeLinkId(updatedMarketIds, eventTypeHierarchyUpdated.eventType, marketsUpdated)
    } else {
      val isEventTypeSetTransition = stateEventHierarchy.eventType.isEmpty && !eventTypeHierarchyUpdated.eventType.isEmpty
      if (isEventTypeSetTransition) {
        enrichMarketsWithMarketTypeLinkId(stateMarkets.keys, eventTypeHierarchyUpdated.eventType, stateMarkets)
      } else {
        stateMarkets
      }
    }

    val stateEvent = if (marketsUpdate.nonEmpty) {
      cascadeMarketIsLiveToEvent(stateEventAggregation.event, marketsUpdated)
    } else {
      stateEventAggregation.event
    }

    RDEventState(
      lastPublishedNo,
      inboundSeqNo,
      stateEventAggregation.copy(
        //Note: it is either an event command or a market command
        event = applyFeedCommand(stateEvent, actorEvent.action, eventModel => applyFeedUpdate(eventModel, eventUpdate)),
        markets = marketsUpdated,
        eventHierarchy = eventTypeHierarchyUpdated
      )
    )
  }

  private def cascadeMarketIsLiveToEvent(
      event: LiveEntityMetadata[RDEventModel],
      markets: Map[RDGBPIdentifierModel, RDMarketAggregationModel]
    ): LiveEntityMetadata[RDEventModel] = {
    val hasAnyMarketLive = markets.find({ case (_, market) => market.market.isLive }).nonEmpty

    if (markets.nonEmpty && event.isLive != hasAnyMarketLive) {
      event.copy(
        isLive = hasAnyMarketLive
      )
    } else {
      event
    }
  }

  private def enrichMarketsWithMarketTypeLinkId(
      marketIds: Iterable[RDGBPIdentifierModel],
      gbpEventTypeId: HierarchyLinkMetadata,
      markets: Map[RDGBPIdentifierModel, RDMarketAggregationModel]
    ) = {

    val eventTypeIdContextIor = Try(GbpId(gbpEventTypeId.identifier.asLong)).asIor

    eventTypeIdContextIor
      .map(eventTypeIdContext => {
        val isMarketTypeDefined: RDMarketAggregationModel => Boolean =
          marketAggregation => marketAggregation.marketTypeLink.isEmpty && marketAggregation.marketTypeId.isDefined
        markets ++ marketIds
          .map(id => id -> markets.get(id))
          .collect({
            case (id, Some(marketAggregation)) if isMarketTypeDefined(marketAggregation) =>
              val marketTypeIdContext = GbpId(marketAggregation.marketTypeId.get)
              val gbpMarketTypeLinkIdContext = {
                Try(GbpId(s"urn:${eventTypeIdContext.source}:${eventTypeIdContext.sourceId}_${marketTypeIdContext.sourceId}", GbpIdHierarchyLevel.MarketTypeLink))
              }
              id -> marketAggregation.copy(
                marketTypeLink = marketAggregation.marketTypeLink.copy(
                  identifier = gbpMarketTypeLinkIdContext.map((gbpId:GbpId) => GbpId(gbpId.asLong)).getOrElse(GbpId.empty)
                )
              )
          })
          .toMap
      })
      .getOrElse({
        logger
          .atWarn()
          .setMessage("operation=enrichMarketsWithMarketTypeLinkId, msg='Invalid event type id', gbpEventTypeId={}, marketIds={}")
          .addArgument(gbpEventTypeId)
          .addArgument(marketIds)

        markets
      })

  }

}
