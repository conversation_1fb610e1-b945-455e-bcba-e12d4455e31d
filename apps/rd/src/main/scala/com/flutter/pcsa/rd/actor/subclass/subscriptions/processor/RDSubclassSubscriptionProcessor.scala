package com.flutter.pcsa.rd.actor.subclass.subscriptions.processor

import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.rd.actor.subclass.RDSubclassActor.{
  RDFlushActorSubclass,
  RDSubclassActorEvent,
  RDSubclassFeedCommandAppliedEvent,
  RDSubclassSubscribeCommands
}
import com.flutter.pcsa.rd.actor.subclass.domain.state.{RDSubclassActorState, RDSubclassModel, RDSubclassNoState, RDSubclassState}
import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import com.flutter.pcsa.rd.subscription.notifications.contract.proto.RDSubclassUpdateNotification
import com.flutter.pcsa.rd.subscription.notifications.contract.proto.RDSubscriptionNotificationInstruction.Notification
import com.flutter.subscriptions.actor.notifier.SubscribersPublisher
import com.flutter.subscriptions.actor.processor.{BaseSubscriptionProcessor, ExtractedNotification}
import com.typesafe.config.Config
import org.apache.pekko.actor.ActorContext
import com.flutter.pcsa.rd.actor.subclass.subscriptions.notification.RDDeltaNotificationSubclass.deltaRDNotificationSubclassUpdate


//noinspection ScalaStyle
class RDSubclassSubscriptionProcessor(
    val actorContext: ActorContext,
    val subscriptionNotifierPublisher: SubscribersPublisher[Notification.SubclassUpdateNotification],
    val subscriptionShardActorConfig: Config)
    extends BaseSubscriptionProcessor[
      RDSubclassActorState,
      RDSubclassActorEvent,
      RDSubclassSubscribeCommands,
      RDSubclassModel,
      Notification.SubclassUpdateNotification
    ] {

  override val actorName: RDSubclassSubscribeCommands => String = _ => subclassId
  override val emptyEntityModel: RDSubclassModel = RDSubclassModel.empty
  private val subclassId = actorContext.self.path.name
  private val EmptySubclassMetadata: EntityMetadata[RDSubclassModel] = EntityMetadata(
    model = emptyEntityModel,
    feedCreated = false,
    hierarchyCreated = false
  )

  override def deltaNotification(
      previousStateNotification: Notification.SubclassUpdateNotification,
      newStateNotification: Notification.SubclassUpdateNotification
    ): Option[Notification.SubclassUpdateNotification] = {
    if (newStateNotification == previousStateNotification) {
      None
    } else {
      Some(
        newStateNotification |-| previousStateNotification
      )
    }
  }

  override def generateNotifications(
      previousState: RDSubclassActorState,
      state: RDSubclassActorState,
      event: RDSubclassActorEvent
    ): List[ExtractedNotification[Notification.SubclassUpdateNotification]] = {

    val previousStateOption = event match {
      case _: RDFlushActorSubclass =>
        None
      case _: Any =>
        previousState match {
          case RDSubclassNoState      => None
          case state: RDSubclassState => Some(state)
        }
    }

    deriveStateNotification(previousStateOption, state, extractEntityMetadataNotification)
      .map(
        extractedNotification => ExtractedNotification(subclassId, extractedNotification, event.headers)
      )
      .toList
  }

  override def extractEntityMetadataSubscription(cmd: RDSubclassSubscribeCommands): RDSubclassActorState => EntityMetadata[RDSubclassModel] =
    extractEntityMetadataNotification

  private def extractEntityMetadataNotification(state: RDSubclassActorState): EntityMetadata[RDSubclassModel] = state match {
    case RDSubclassNoState =>
      EmptySubclassMetadata
    case stateToExtract: RDSubclassState =>
      stateToExtract.subclassAggregationModel.subclass
  }

  override def ensureItIsTheExpectedTypeOfSubscribeCommands(cmd: Any): Boolean =
    cmd.isInstanceOf[RDSubclassSubscribeCommands]

  override def buildEntityNotification(model: RDSubclassModel): Notification.SubclassUpdateNotification = {
    Notification.SubclassUpdateNotification(
      RDSubclassUpdateNotification()
    )
  }
}
// scalastyle:on
