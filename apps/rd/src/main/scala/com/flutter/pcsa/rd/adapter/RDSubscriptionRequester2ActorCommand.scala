package com.flutter.pcsa.rd.adapter

import cats.data.Ior
import com.flutter.baseactor.BaseActor.ActorCommand
import com.flutter.baseactor.behaviour.SubscribeCommands
import com.flutter.headers.{INTERNAL_SOURCE_TIMESTAMP, INTERNAL_START_TIMESTAMP}
import com.flutter.infra.kafka.converter.Converter
import com.flutter.infra.kafka.streams.kafka.{KafkaMessageInterpreterException, KafkaMessageValidationFailures}
import com.flutter.observability.profile.ProfileTechnicalMetricsCollector
import com.flutter.pcsa.rd.RD
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.{RDSubscribeEventType, RDSubscribeEventTypeFromMarketType, RDSubscribeMarketTypeLink, RDUnsubscribeEventType, RDUnsubscribeEventTypeFromMarketType, RDUnsubscribeMarketTypeLink}
import com.flutter.pcsa.rd.actor.markettype.RDMarketTypeActor.{RDSubscribeMarketTypeLinkFromMarket, RDUnsubscribeMarketTypeLinkFromMarket}
import com.flutter.pcsa.rd.actor.subclass.RDSubclassActor.{RDSubscribeSubclass, RDUnsubscribeSubclass}
import com.flutter.pcsa.rd.actor.superclass.RDSuperclassActor.{RDSubscribeSuperclass, RDUnsubscribeSuperclass}
import com.flutter.pcsa.rd.common.observability.AddHeadersToCommandHelper.addHeadersToActorCommand
import com.flutter.pcsa.rd.subscriptions.contract.proto.RDSubscriptionInstruction
import com.flutter.pcsa.rd.subscriptions.contract.proto.RDSubscriptionInstruction.Command.{SubscribeEventType, SubscribeEventTypeFromMarketTypeLink, SubscribeMarketTypeLink, SubscribeSubclass, SubscribeSuperclass, UnsubscribeEventType, UnsubscribeEventTypeFromMarketTypeLink, UnsubscribeMarketTypeLink, UnsubscribeSubclass, UnsubscribeSuperclass, SubscribeMarketTypeLinkFromMarket, UnsubscribeMarketTypeLinkFromMarket}
import com.flutter.retryableactor.metadata.MetadataUtils
import org.apache.kafka.clients.consumer.ConsumerRecord

class RDSubscriptionRequester2ActorCommand extends Converter[ConsumerRecord[String, RDSubscriptionInstruction], ActorCommand]{
  this: ProfileTechnicalMetricsCollector[ActorCommand with SubscribeCommands] =>

  override def convert(inbound: ConsumerRecord[String, RDSubscriptionInstruction]): Ior[KafkaMessageValidationFailures, ActorCommand] = {

    val cmdIor = inbound.value().command match {
      case SubscribeEventType(subscribeEventType) =>
        Ior.right(
          RDSubscribeEventType(
            eventTypeId = subscribeEventType.eventTypeId,
            eventId = subscribeEventType.eventId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case UnsubscribeEventType(unsubscribeEventType) =>
        Ior.right(
          RDUnsubscribeEventType(
            eventTypeId = unsubscribeEventType.eventTypeId,
            eventId = unsubscribeEventType.eventId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case SubscribeEventTypeFromMarketTypeLink(rdSubscribeEventType) =>
        Ior.right(
          RDSubscribeEventTypeFromMarketType(
            eventTypeId = rdSubscribeEventType.eventTypeId,
            marketTypeId = rdSubscribeEventType.marketTypeId,
            marketTypeLinkId = rdSubscribeEventType.marketTypeLinkId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case UnsubscribeEventTypeFromMarketTypeLink(rdUnsubscribeEventType) =>
        Ior.right(
          RDUnsubscribeEventTypeFromMarketType(
            eventTypeId = rdUnsubscribeEventType.eventTypeId,
            marketTypeId = rdUnsubscribeEventType.marketTypeId,
            marketTypeLinkId = rdUnsubscribeEventType.marketTypeLinkId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case SubscribeMarketTypeLink(subscribeMarketTypeLink) =>
        Ior.right(
          RDSubscribeMarketTypeLink(
            eventTypeId = subscribeMarketTypeLink.eventTypeId,
            marketTypeLinkId = subscribeMarketTypeLink.marketTypeLinkId,
            marketId = subscribeMarketTypeLink.marketId,
            eventId = subscribeMarketTypeLink.eventId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case UnsubscribeMarketTypeLink(unsubscribeMarketTypeLink) =>
        Ior.right(
          RDUnsubscribeMarketTypeLink(
            eventTypeId = unsubscribeMarketTypeLink.eventTypeId,
            marketTypeLinkId = unsubscribeMarketTypeLink.marketTypeLinkId,
            marketId = unsubscribeMarketTypeLink.marketId,
            eventId = unsubscribeMarketTypeLink.eventId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case SubscribeSubclass(subscribeSubclass) =>
        Ior.right(
          RDSubscribeSubclass(
            subclassId = subscribeSubclass.subclassId,
            eventTypeId = subscribeSubclass.eventTypeId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case UnsubscribeSubclass(unsubscribeSubclass) =>
        Ior.right(
          RDUnsubscribeSubclass(
            subclassId = unsubscribeSubclass.subclassId,
            eventTypeId = unsubscribeSubclass.eventTypeId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case SubscribeSuperclass(subscribeSuperclass) =>
        Ior.right(
          RDSubscribeSuperclass(
            superclassId = subscribeSuperclass.superclassId,
            subclassId = subscribeSuperclass.subclassId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case UnsubscribeSuperclass(unsubscribeSuperclass) =>
        Ior.right(
          RDUnsubscribeSuperclass(
            superclassId = unsubscribeSuperclass.superclassId,
            subclassId = unsubscribeSuperclass.subclassId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case SubscribeMarketTypeLinkFromMarket(subscribeMarketTypeLink) =>
        Ior.right(
          RDSubscribeMarketTypeLinkFromMarket(
            marketTypeId = subscribeMarketTypeLink.marketTypeId,
            marketTypeLinkId = subscribeMarketTypeLink.marketTypeLinkId,
            marketId = subscribeMarketTypeLink.marketId,
            eventId = subscribeMarketTypeLink.eventId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case UnsubscribeMarketTypeLinkFromMarket(unsubscribeMarketTypeLink) =>
        Ior.right(
          RDUnsubscribeMarketTypeLinkFromMarket(
            marketTypeId = unsubscribeMarketTypeLink.marketTypeId,
            marketTypeLinkId = unsubscribeMarketTypeLink.marketTypeLinkId,
            marketId = unsubscribeMarketTypeLink.marketId,
            eventId = unsubscribeMarketTypeLink.eventId,
            headers = extractHeaders(inbound),
            metadata = MetadataUtils.getKafkaMetadata(inbound)
          )
        )
      case _: Any =>
        Ior.left(KafkaMessageInterpreterException.apply(List(s"not implemented for $inbound")))
    }

    val commandIorWithMetricsHeaders = cmdIor.map(cmd => {
      addHeadersToActorCommand(cmd, getProfileTechnicalMetricsHeaders(cmd))
    })

    commandIorWithMetricsHeaders
  }

  private def extractHeaders(inbound: ConsumerRecord[String, RDSubscriptionInstruction]): Map[String, String] = {
    inbound.headers().toArray.map(h => h.key() -> new String(h.value())).toMap ++ Map(
      INTERNAL_SOURCE_TIMESTAMP(RD.ServiceName) -> inbound.timestamp().toString,
      INTERNAL_START_TIMESTAMP(RD.ServiceName) -> System.currentTimeMillis().toString
    )
  }
}
