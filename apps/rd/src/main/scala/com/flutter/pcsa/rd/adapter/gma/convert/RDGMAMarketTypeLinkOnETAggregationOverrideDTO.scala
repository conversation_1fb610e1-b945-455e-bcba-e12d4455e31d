package com.flutter.pcsa.rd.adapter.gma.convert

import cats.data.Ior
import cats.implicits.catsSyntaxOptionId
import com.flutter.adapter.IorConverter.IorConverterExtensionOps
import com.flutter.infra.kafka.streams.kafka.{KafkaMessageInterpreterException, KafkaMessageValidationFailures}
import com.flutter.pcsa.common.datatypes.EntityIdentifiers
import com.flutter.pcsa.overrides.OverridesDTO
import com.flutter.pcsa.rd.actor.eventtype.domain.dto.{RDMarketTypeLinkOverrideDTO, RDOverrideDTO}
import com.flutter.product.catalogue.overrides.MarketTypeLink

import scala.util.{Failure, Success, Try}

trait RDGMAMarketTypeLinkOnETAggregationOverrideDTO extends GMAOverrideConverter {

  def innerMarketTypeLinkConverter(inbound: MarketTypeLink): Ior[KafkaMessageValidationFailures, RDOverrideDTO] = {
    validateGbpIdIor(inbound.eventTypeId)
      .mapFn(
        gbpEventTypeId =>
          validateGbpIdIor(inbound.id)
            .mapFn(
              gbpMarketTypeLinkId =>
                Try(asMarketOverride(inbound)) match {
                  case Failure(e)           => Ior.left(KafkaMessageInterpreterException(List(s"$e")))
                  case Success(computedDTO) => Ior.right(computedDTO)
                }
            )
      )
  }

  private def asMarketOverride(inbound: MarketTypeLink) = {
    RDMarketTypeLinkOverrideDTO.empty.copy(
      id = EntityIdentifiers.createGbpId(inbound.id),
      eventTypeId = EntityIdentifiers.createGbpId(inbound.eventTypeId),
      subclassId = EntityIdentifiers.createGbpId(inbound.subclassId),
      superclassId = EntityIdentifiers.createGbpId(inbound.superclassId),
      layToLosePerc = OverridesDTO.empty[Int].copy(overrides = convertField(inbound.layToLosePerc)).some,
      leastMaxBetPerc = OverridesDTO.empty[Int].copy(overrides = convertField(inbound.leastMaxBetPerc)).some,
      mostMaxBetPerc = OverridesDTO.empty[Int].copy(overrides = convertField(inbound.mostMaxBetPerc)).some,
      stakeFactor = OverridesDTO.empty[Double].copy(overrides = convertField(inbound.stakeFactor)).some,
      inPlayBettingDelay = OverridesDTO.empty[Double].copy(overrides = convertField(inbound.inPlayBettingDelay)).some
    )
  }

}
