package com.flutter.pcsa.rd.adapter.fip

import com.flutter.observability.profile.TechnicalMessageProfile.TechnicalMessageProfile
import com.flutter.observability.profile._
import com.flutter.observability.profile.ProfileMetricsCollectorSyntax.IsEmptyToNonEmptySyntax
import com.flutter.pcsa.rd.actor.event.RDEventActor.RDEventFeedCommand
import com.flutter.pcsa.rd.actor.event.domain.dto.{RDEventDTO, RDMarketDTO, RDSelectionDTO}

import scala.language.implicitConversions

/**
 * This Trait collects FIP profiling data. It collects the Action, the affected Hierarchy levels, and Business Unit Metrics.
 *
 * Business Unit Metrics collected here:
 *  - EVENT:
 *    - BIR Delay   -> GENERAL
 *    - LayToLose   -> BET LIMITS
 *    - LeastMaxBet -> BET LIMITS
 *    - MostMaxBet  -> BET LIMITS
 *  - MARKET:
 *    - LayToLose           -> Bet Limits
 *    - LeastMaxBet         -> Bet Limits
 *    - MostMaxBet          -> Bet Limits
 *    - Win GMLTL           -> Bet Limits
 *    - Ignore Time Config  -> Bet Limits
 *    - BIR delay           -> General
 *  - SELECTION:
 *    - Max Bet (LP)               -> Bet Limits
 */
trait RDFIPProfileMetricsCollector extends ProfileTechnicalMetricsCollector[RDEventFeedCommand] {

  override def getProfileTechnicalMetricsHeaders(cmd: RDEventFeedCommand): Map[String, String] = {

    val selections = cmd.markets.values.flatMap(_.selections.values.map(_.selection)).toSet
    val markets = cmd.markets.values.map(_.market).toSet

    val profiles = Profiles.merge(
      getEventTechnicalMetrics(cmd.event),
      getMarketTechnicalMetrics(markets),
      getSelectionTechnicalMetrics(selections)
    )

    val hierarchyLevels = Profiles.withConditionalProfiles(
      cmd.event.nonEmpty            -> TechnicalMessageHierarchyLevel.EVENT,
      markets.exists(_.nonEmpty)    -> TechnicalMessageHierarchyLevel.MARKET,
      selections.exists(_.nonEmpty) -> TechnicalMessageHierarchyLevel.SELECTION
    )

    createHeadersMap(
      action = cmd.action.toString,
      hierarchyLevels = hierarchyLevels,
      technicalProfiles = profiles
    )
  }


  private def getSelectionTechnicalMetrics(selections: Set[RDSelectionDTO]): Profiles[TechnicalMessageProfile] =
    Profiles.withConditionalProfiles(
      selections.exists(_.lpMaxBet.isDefined) -> TechnicalMessageProfile.BET_LIMITS
    )

  private def getEventTechnicalMetrics(event: RDEventDTO): Profiles[TechnicalMessageProfile] = {
    Profiles.withConditionalProfiles(
      event.inPlayBettingDelay.isDefined -> TechnicalMessageProfile.GENERAL,
      event.leastMaxBet.isDefined        -> TechnicalMessageProfile.BET_LIMITS,
      event.mostMaxBet.isDefined         -> TechnicalMessageProfile.BET_LIMITS,
      event.layToLose.isDefined          -> TechnicalMessageProfile.BET_LIMITS
    )
  }

  private def getMarketTechnicalMetrics(markets: Set[RDMarketDTO]): Profiles[TechnicalMessageProfile] = {
    Profiles.withConditionalProfiles(
      markets.exists(_.layToLose.isDefined)          -> TechnicalMessageProfile.BET_LIMITS,
      markets.exists(_.leastMaxBet.isDefined)        -> TechnicalMessageProfile.BET_LIMITS,
      markets.exists(_.mostMaxBet.isDefined)         -> TechnicalMessageProfile.BET_LIMITS,
      markets.exists(_.inPlayBettingDelay.isDefined) -> TechnicalMessageProfile.GENERAL
    )
  }
}
