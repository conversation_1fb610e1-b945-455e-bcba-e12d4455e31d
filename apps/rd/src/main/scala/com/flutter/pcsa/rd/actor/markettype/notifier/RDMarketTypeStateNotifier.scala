package com.flutter.pcsa.rd.actor.markettype.notifier

import com.flutter.baseactor.notifier.{ActorStateNotifier, ActorStateNotifierLogger}
import com.flutter.observability.profile.ProfileInheritanceMetricsCollector
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.rd.actor.markettype.RDMarketTypeActor.{RDMarketTypeActorEvent, RDMarketTypeFeedCommandApplied, RDFlushActorMarketType}
import com.flutter.pcsa.rd.actor.markettype.domain.state.{RDMarketTypeActorState, RDMarketTypeNoState, RDMarketTypeState}
import com.flutter.pcsa.rd.actor.markettype.notifier.creator.RDMarketTypeLinkGlobalInstructionCreator
import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import com.flutter.pcsa.rd.common.publish.RDHeaders
import com.flutter.publish.HeaderPublisher
import com.flutter.publish.MessagePublisher.KafkaPublish

import scala.concurrent.Future

class RDMarketTypeStateNotifier(val headerPublisher: HeaderPublisher, marketTypeLinkGlobalInstructionCreator: RDMarketTypeLinkGlobalInstructionCreator)
  extends ActorStateNotifier[RDMarketTypeActorState, RDMarketTypeActorEvent] with RDHeaders with ActorStateNotifierLogger {
  this: ProfileInheritanceMetricsCollector[RDMarketTypeActorEvent, RDMarketTypeState] =>

  /**
   * Method responsible to calculate changes between actor states and publish a notification to a stream (if exists)
   *
   * @param previousState Previous actor state
   * @param state         Next actor state
   * @return Future that should only be completed when publish is confirmed
   */
  override def notify(previousState: RDMarketTypeActorState, state: RDMarketTypeActorState, actorEvent: RDMarketTypeActorEvent): Future[Unit] =
    (previousState, state, actorEvent) match {
      case (RDMarketTypeNoState, currentState: RDMarketTypeState, _) => notify(None, currentState, actorEvent)
      case (previousState: RDMarketTypeState, currentState: RDMarketTypeState, _) if previousState.marketTypeAggregationModel != currentState.marketTypeAggregationModel => notify(Some(previousState), currentState, actorEvent)
      case (previousState: RDMarketTypeState, currentState: RDMarketTypeState, event: RDMarketTypeFeedCommandApplied) if event.action == RDFeedActionDTO.REFRESH => notify(Some(previousState), currentState, actorEvent)
      case (previousState: RDMarketTypeState, currentState: RDMarketTypeState, _: RDFlushActorMarketType) => notify(Some(previousState), currentState, actorEvent)
      case (_, _, _) => Future.successful()
    }

  private def notify(previousState: Option[RDMarketTypeState], currentState: RDMarketTypeState, actorEvent: RDMarketTypeActorEvent): Future[Unit] = {
    val rampSportHeader = currentState.marketTypeAggregationModel.marketType.model.subclassId.value.map(_.toString).filterNot(_.isBlank)
      .orElse(actorEvent.headers.get("SPORT"))
      .map(rampSport => Map[String, AnyRef]("RAMP_SPORT" -> rampSport)).getOrElse(Map.empty)

    val seqNum = currentState.lastPublishedNo + 1
    val internalSeqNum = currentState.inboundSeqNo


    val marketTypeLinkInstructions = currentState.marketTypeAggregationModel.marketTypeLinks.flatMap({
      case (id, marketTypeLink) =>
        val previousMarketTypeLink = previousState.flatMap(_.marketTypeAggregationModel.marketTypeLinks.get(id))

        val marketTypeLinkInstruction = marketTypeLinkGlobalInstructionCreator.createInstruction(
          previousMarketTypeLink,
          marketTypeLink,
          actorEvent
        )

        marketTypeLinkInstruction.map(marketTypeLinkInstruction => {
          KafkaPublish(
            id.identifier,
            seqNum,
            Some(marketTypeLinkInstruction.action.name),
            actorEvent.headers ++ rampSportHeader,
            extractHeaders(actorEvent.headers, seqNum.value, internalSeqNum.value, marketTypeLinkInstruction, actorEvent),
            marketTypeLinkInstruction.toByteArray
          )
        })
    }).toList

    val instructions = marketTypeLinkInstructions

    logEndOfProcessing(
      gbpId = currentState.marketTypeAggregationModel.marketType.identifier.asLong,
      actorEventName = actorEvent.getClass.getSimpleName,
      actorEventHeaders = actorEvent.headers ++ getProfileInheritanceMetricsHeaders(actorEvent, currentState, previousState),
      nrOfMessagesToBePublished = instructions.length
    )

    headerPublisher.publish(instructions)
  }
}
