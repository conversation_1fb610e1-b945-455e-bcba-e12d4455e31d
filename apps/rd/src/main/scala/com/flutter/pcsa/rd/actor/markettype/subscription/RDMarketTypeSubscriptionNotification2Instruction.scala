package com.flutter.pcsa.rd.actor.markettype.subscription

import cats.data.Ior
import com.flutter.infra.kafka.converter.Converter
import com.flutter.infra.kafka.streams.kafka.KafkaMessageValidationFailures
import com.flutter.pcsa.rd.subscription.notifications.contract.proto.RDSubscriptionNotificationInstruction
import com.flutter.pcsa.rd.subscription.notifications.contract.proto.RDSubscriptionNotificationInstruction.Notification
import com.flutter.subscriptions.actor.domain.state.Subscription

class RDMarketTypeSubscriptionNotification2Instruction[A <: Notification] extends Converter[(Set[Subscription], A), RDSubscriptionNotificationInstruction] {

  val SCHEMA_VERSION = "0.0.1"

  override def convert(inbound: (Set[Subscription], A)): Ior[KafkaMessageValidationFailures, RDSubscriptionNotificationInstruction] = {
    val instruction = RDSubscriptionNotificationInstruction(
      schemaVersion = SCHEMA_VERSION,
      subscribers = inbound._1.map(_.identifier).toList,
      notification = inbound._2
    )
    Ior.right(instruction)
  }
}
