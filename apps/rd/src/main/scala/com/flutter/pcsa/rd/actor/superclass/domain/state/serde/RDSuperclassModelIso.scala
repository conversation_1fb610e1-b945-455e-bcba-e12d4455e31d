package com.flutter.pcsa.rd.actor.superclass.domain.state.serde

import cats.implicits.catsSyntaxOptionId
import com.flutter.pcsa.common.datatypes
import com.flutter.pcsa.common.serde.EnumerationsIso.entityIdentifiersIso
import com.flutter.pcsa.common.state.model.datatypes.OverridableFeedField
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.common.state.model.serde.OverridableFeedFieldIso.OverridableFeedFieldStringIso
import com.flutter.pcsa.rd.actor.superclass.domain.state.RDSuperclassModel
import com.flutter.pcsa.rd.actor.superclass.domain.state.proto.{RDSuperclassEntityMetadataModelProto, RDSuperclassModelProto}
import monocle.Iso

trait RDSuperclassModelIso {

  val rdSuperclassEntityMetadataModelIso: Iso[EntityMetadata[RDSuperclassModel], RDSuperclassEntityMetadataModelProto] = Iso[EntityMetadata[RDSuperclassModel], RDSuperclassEntityMetadataModelProto] { im =>
    RDSuperclassEntityMetadataModelProto(
      model = rdSuperclassModelIso.get(im.model).some,
      feedCreated = im.feedCreated,
      hierarchyCreated = im.hierarchyCreated
    )
  } { pb =>
    EntityMetadata(
      model = pb.model.map(rdSuperclassModelIso.reverseGet).getOrElse(RDSuperclassModel.empty),
      feedCreated = pb.feedCreated,
      hierarchyCreated = pb.hierarchyCreated
    )
  }

  val rdSuperclassModelIso: Iso[RDSuperclassModel, RDSuperclassModelProto] =
    Iso[RDSuperclassModel, RDSuperclassModelProto] { im =>
      RDSuperclassModelProto(
        identifiers = entityIdentifiersIso.get(im.entityIdentifiers.identifiers),
        name = Some(OverridableFeedFieldStringIso.get(im.name))
      )
    } { pb =>
      RDSuperclassModel(
        entityIdentifiers = datatypes.EntityIdentifiers(entityIdentifiersIso.reverseGet(Map.from(pb.identifiers))),
        name = pb.name.map(OverridableFeedFieldStringIso.reverseGet).getOrElse(OverridableFeedField.empty[String])
      )
    }
}
