// $COVERAGE-OFF$
package com.flutter.pcsa.rd.common.datatypes

import com.flutter.pcsa.rd.subscription.notifications.contract.common.proto.BoolField.OptionalBool
import com.flutter.pcsa.rd.subscription.notifications.contract.common.proto.{<PERSON><PERSON><PERSON><PERSON>, <PERSON>Field, IntField, LongField, StringField}
import com.flutter.pcsa.rd.subscription.notifications.contract.common.proto.DoubleField.OptionalDouble
import com.flutter.pcsa.rd.subscription.notifications.contract.common.proto.IntField.OptionalInt
import com.flutter.pcsa.rd.subscription.notifications.contract.common.proto.LongField.OptionalLong
import com.flutter.pcsa.rd.subscription.notifications.contract.common.proto.StringField.OptionalString

import scala.language.implicitConversions

object NotificationDataTypesConversion {

  implicit def convertOptionalIntToOption(value: OptionalInt): Option[Int] = {
    value.value
  }

  implicit def convertOptionalLongToOption(value: OptionalLong): Option[Long] = {
    value.value
  }

  implicit def convertOptionalDoubleToOption(value: OptionalDouble): Option[Double] = {
    value.value
  }

  implicit def convertOptionalStringToOption(value: OptionalString): Option[String] = {
    value.value
  }

  implicit def convertOptionalBoolToOption(value: OptionalBool): Option[Boolean] = {
    value.value
  }

  implicit def convertMapOptionalIntToOption(value: Option[IntField]): Option[Map[String, Option[Int]]] = {
    value.map(_.data.map(kv => kv._1 -> kv._2))
  }

  implicit def convertMapOptionalLongToOption(value: Option[LongField]): Option[Map[String, Option[Long]]] = {
    value.map(_.data.map(kv => kv._1 -> kv._2))
  }

  implicit def convertMapOptionalDoubleToOption(value: Option[DoubleField]): Option[Map[String, Option[Double]]] = {
    value.map(_.data.map(kv => kv._1 -> kv._2))
  }

  implicit def convertMapOptionalStringToOption(value: Option[StringField]): Option[Map[String, Option[String]]] = {
    value.map(_.data.map(kv => kv._1 -> kv._2))
  }

  implicit def convertMapOptionalBoolToOption(value: Option[BoolField]): Option[Map[String, Option[Boolean]]] = {
    value.map(_.data.map(kv => kv._1 -> kv._2))
  }
}
// $COVERAGE-ON$
