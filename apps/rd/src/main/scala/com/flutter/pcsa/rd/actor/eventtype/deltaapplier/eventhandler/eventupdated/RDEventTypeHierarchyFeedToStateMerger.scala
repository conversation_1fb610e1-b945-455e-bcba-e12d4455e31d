package com.flutter.pcsa.rd.actor.eventtype.deltaapplier.eventhandler.eventupdated

import com.flutter.pcsa.rd.actor.eventtype.domain.dto.RDEventTypeHierarchyDTO
import com.flutter.pcsa.rd.actor.eventtype.domain.state.RDEventTypeHierarchyModel
import com.flutter.pcsa.rd.common.deltaapplier.eventhandler.eventupdated.HierarchyLinkMetadataFeedToStateMerger

trait RDEventTypeHierarchyFeedToStateMerger extends HierarchyLinkMetadataFeedToStateMerger {

  def applyFeedUpdate(state: RDEventTypeHierarchyModel, update: RDEventTypeHierarchyDTO): RDEventTypeHierarchyModel = {
    state.copy(
      subclass = applyFeedUpdate(state.subclass, update.subclassIdentifier)
    )
  }

}
