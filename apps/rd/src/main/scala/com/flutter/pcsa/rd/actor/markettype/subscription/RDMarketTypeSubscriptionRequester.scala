package com.flutter.pcsa.rd.actor.markettype.subscription

import com.flutter.infra.kafka.producer.GenericKafkaProducer
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.rd.actor.markettype.RDMarketTypeActor.{RDFlushActorMarketType, RDMarketTypeActorEvent, RDMarketTypeLinkFeedCommandApplied, RDPurgeMeEvent}
import com.flutter.pcsa.rd.actor.markettype.domain.dto.RDMarketTypeLinkDTO
import com.flutter.pcsa.rd.actor.markettype.domain.state.{RDMarketTypeActorState, RDMarketTypeLinkModel, RDMarketTypeState}
import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import com.flutter.pcsa.rd.subscriptions.contract.proto.RDSubscriptionInstruction
import com.flutter.pcsa.rd.subscriptions.contract.proto.RDSubscriptionInstruction.Command.{SubscribeEventTypeFromMarketTypeLink, UnsubscribeEventTypeFromMarketTypeLink}
import com.flutter.pcsa.rd.subscriptions.contract.proto.RDSubscriptionInstruction.{RDSubscribeEventTypeFromMarketType, RDUnsubscribeEventTypeFromMarketType}
import com.flutter.sharedplatforms.LazyLogging
import com.flutter.subscriptions.requester.SubscriptionRequester

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class RDMarketTypeSubscriptionRequester(val genericKafkaProducer: GenericKafkaProducer[String, RDSubscriptionInstruction])
    extends SubscriptionRequester[RDMarketTypeActorState, RDMarketTypeActorEvent] with LazyLogging {

  private val SchemaVersion = "0.0.1"

  implicit class ValidAction(field: RDFeedActionDTO.Value){
    def isValid: Boolean = {
      Set(RDFeedActionDTO.CREATE, RDFeedActionDTO.REFRESH).contains(field)
    }
  }

  override def requestSubscriptions(previousState: RDMarketTypeActorState, state: RDMarketTypeActorState, actorEvent: RDMarketTypeActorEvent): Future[Unit] = {
    (state, actorEvent) match {
      case (currentState: RDMarketTypeState, purgeMeEvent: RDPurgeMeEvent) =>
        handlePurgeEvent(currentState, purgeMeEvent)
      case (currentState: RDMarketTypeState, refreshActorEvent: RDFlushActorMarketType) =>
        handleRefreshEvent(currentState, refreshActorEvent)
      case (currentState: RDMarketTypeState, marketTypeLinkFeedCommand: RDMarketTypeLinkFeedCommandApplied) if(marketTypeLinkFeedCommand.action.isValid) =>
        handleMarketTypeLinkFeedCommand(currentState, marketTypeLinkFeedCommand)
      case _ => Future.successful(())
    }
  }

  private def handlePurgeEvent(currentState: RDMarketTypeState, event: RDPurgeMeEvent): Future[Unit] = {
    Future.sequence(currentState.marketTypeAggregationModel.marketTypeLinks.map {
      case (cmdMarketIdentifier, marketTypeLink) => unsubscribeEventType(currentState, event, toEntityIdsMap(marketTypeLink.model))
    }.toList).map(_ => ())
  }

  private def handleMarketTypeLinkFeedCommand(currentState: RDMarketTypeState, marketTypeLinkFeedCommand: RDMarketTypeLinkFeedCommandApplied): Future[Unit] = {
    marketTypeLinkFeedCommand.action match {
      case RDFeedActionDTO.REFRESH => {
        unsubscribeEventType(currentState, marketTypeLinkFeedCommand, toEntityIdsMap(marketTypeLinkFeedCommand.marketTypeLink))
        subscribeEventType(currentState, marketTypeLinkFeedCommand, toEntityIdsMap(marketTypeLinkFeedCommand.marketTypeLink))
      }
      case RDFeedActionDTO.CREATE => subscribeEventType(currentState, marketTypeLinkFeedCommand, toEntityIdsMap(marketTypeLinkFeedCommand.marketTypeLink))
    }
  }

  private def handleRefreshEvent(currentState: RDMarketTypeState, refreshMKTypeActorEvent: RDFlushActorMarketType): Future[Unit] = {
    Future.sequence(currentState.marketTypeAggregationModel.marketTypeLinks.map {
      case (cmdMarketIdentifier, marketTypeLink) =>
        val entitiesIdMap = toEntityIdsMap(marketTypeLink.model)
        unsubscribeEventType(currentState, refreshMKTypeActorEvent, entitiesIdMap)
        subscribeEventType(currentState, refreshMKTypeActorEvent, entitiesIdMap)
    }.toList).map(_ => ())
  }


  private def subscribeEventType(currentState: RDMarketTypeState, event: RDMarketTypeActorEvent, entityIdsMap: Map[String, GbpId]): Future[Unit] = {
    val eventTypeId = entityIdsMap("eventTypeId")
    val marketTypeId = entityIdsMap("marketTypeId").asLong
    val marketTypeLinkId = entityIdsMap("marketTypeLinkId").asLong

    val subscription = generateMarketTypeLinkSubscriptionRequest(eventTypeId.asLong, marketTypeId, marketTypeLinkId)

    produceMessage(
      eventTypeId.asInfraKey,
      subscription,
      event.headers,
      exception => {
        var logBuilder = logger.atWarn()
          .setCause(exception)
        if (logger.isDebugEnabled) {
          logBuilder = logBuilder
            .addKeyValue("currentState", currentState)
            .addKeyValue("actorEvent", event)
        } else {
          logBuilder = logBuilder
            .addKeyValue("cause", exception.getMessage)
        }
        logBuilder
          .addKeyValue("marketTypeId", marketTypeId)
          .log("operation=subscribeEventType, msg='An error occurred during publishing event type subscription'")
      })
  }

  private def unsubscribeEventType(currentState: RDMarketTypeState, event: RDMarketTypeActorEvent, entityIdsMap: Map[String, GbpId]): Future[Unit] = {
    val eventTypeId = entityIdsMap("eventTypeId")
    val marketTypeId = entityIdsMap("marketTypeId").asLong
    val marketTypeLinkId = entityIdsMap("marketTypeLinkId").asLong

    val subscription = generateMarketTypeLinkUnsubscriptionRequest(eventTypeId.asLong, marketTypeId, marketTypeLinkId)

    produceMessage(eventTypeId.asInfraKey,
      subscription,
      event.headers,
      exception => {
        var logBuilder = logger.atWarn()
          .setCause(exception)
        if (logger.isDebugEnabled) {
          logBuilder = logBuilder
            .addKeyValue("currentState", currentState)
            .addKeyValue("actorEvent", event)
        } else {
          logBuilder = logBuilder
            .addKeyValue("cause", exception.getMessage)
        }
        logBuilder
          .addKeyValue("marketTypeId", marketTypeId)
          .log("operation=unsubscribeEventType, msg='An error occurred during publishing event type unsubscription'")
      })
  }

  private def generateMarketTypeLinkSubscriptionRequest(eventTypeId: String, marketTypeId: String, marketTypeLinkId: String) = {
    val rdSubscribeEventType: RDSubscribeEventTypeFromMarketType = RDSubscribeEventTypeFromMarketType(eventTypeId = eventTypeId, marketTypeId = marketTypeId, marketTypeLinkId = marketTypeLinkId)
    RDSubscriptionInstruction(schemaVersion = SchemaVersion, command = SubscribeEventTypeFromMarketTypeLink(rdSubscribeEventType))
  }

  private def generateMarketTypeLinkUnsubscriptionRequest(eventTypeId: String, marketTypeId: String, marketTypeLinkId: String) = {
    val rdUnsubscribeEventType: RDUnsubscribeEventTypeFromMarketType = RDUnsubscribeEventTypeFromMarketType(eventTypeId = eventTypeId, marketTypeId = marketTypeId, marketTypeLinkId = marketTypeLinkId)
    RDSubscriptionInstruction(schemaVersion = SchemaVersion, command = UnsubscribeEventTypeFromMarketTypeLink(rdUnsubscribeEventType))
  }

  private def produceMessage(key: String, subscriptionInstruction: RDSubscriptionInstruction, headers: Map[String, String], onError: Throwable => Unit): Future[Unit] = {
    genericKafkaProducer.send(GbpId(key).asInfraKey, subscriptionInstruction, headers, onError = onError).map(_ => ())
  }

  private def toEntityIdsMap(link: RDMarketTypeLinkModel): Map[String, GbpId] = Map( "eventTypeId" -> link.eventTypeId, "marketTypeId" -> link.marketTypeId, "marketTypeLinkId" -> link.identifier)

  private def toEntityIdsMap(link: RDMarketTypeLinkDTO): Map[String, GbpId] = Map( "eventTypeId" -> link.eventTypeId, "marketTypeId" -> link.marketTypeId, "marketTypeLinkId" -> link.identifier)

}
