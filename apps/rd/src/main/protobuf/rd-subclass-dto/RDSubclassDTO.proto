syntax = "proto3";

package com.flutter.pcsa.rd.subclass.proto;

import "rd-common/RDFeedActions.proto";
import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  flat_package: true
};

message RDSubclassDTOProto {
  string identifier = 1;
  string superclassId = 2;
  google.protobuf.Int32Value minForecastStakeLimit = 3;
  google.protobuf.Int32Value minTricastStakeLimit = 4;
  google.protobuf.DoubleValue forecastStakeFactor = 5;
  google.protobuf.DoubleValue tricastStakeFactor = 6;
  // GBPE-9044 : PLEASE RESERVE THIS PROPERTY, DO NOT DELETE OR REUSE
  google.protobuf.StringValue name = 7;
}
