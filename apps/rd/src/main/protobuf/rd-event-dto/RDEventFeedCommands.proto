syntax = "proto3";

package com.flutter.pcsa.rd.actor.event.domain.dto.proto;

import "rd-common/RDFeedActions.proto";
import "rd-event-dto/RDMarketDTO.proto";
import "rd-event-dto/RDSelectionDTO.proto";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  flat_package: true
};

message RDMarketFeedCommandProto {
  com.flutter.pcsa.rd.actor.common.domain.dto.proto.FeedActionProto action = 1;
  optional string marketTypeId = 2;
  RDMarketDTOProto market = 3;
  repeated RDSelectionFeedCommandProto selections = 4;
}

message RDSelectionFeedCommandProto {
  com.flutter.pcsa.rd.actor.common.domain.dto.proto.FeedActionProto action = 1;
  RDSelectionDTOProto selection = 2;
}

message RDEventHierarchyDTOProto {
  optional string eventTypeIdentifier = 1;
  optional string subclassIdentifier = 2;
  optional string superclassIdentifier = 3;
}
