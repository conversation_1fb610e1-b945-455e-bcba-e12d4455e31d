syntax = "proto3";

import "KafkaMetadata.proto";
import "rd-subscription-notifications-contract/RDSuperclassUpdateNotification.proto";

package com.flutter.pcsa.rd.actor.subclass.commands.proto;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  flat_package: true
};

message RDSubscribeSubclassProto {
  map<string, string> headers = 1;
  string eventTypeId = 2;
  string subclassId = 3;
  com.flutter.pcsa.actor.common.metadata.proto.KafkaMetadataProto metadata = 4;
}

message RDUnsubscribeSubclassProto {
  map<string, string> headers = 1;
  string eventTypeId = 2;
  string subclassId = 3;
  com.flutter.pcsa.actor.common.metadata.proto.KafkaMetadataProto metadata = 4;
}

message RDInheritanceUpdateSubclassProto {
  map<string, string> headers = 1;
  string identifier = 2;
  com.flutter.pcsa.rd.subscription.notifications.contract.proto.RDSuperclassUpdateNotification notification = 3;
  com.flutter.pcsa.actor.common.metadata.proto.KafkaMetadataProto metadata = 4;
}