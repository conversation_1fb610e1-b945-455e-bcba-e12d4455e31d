syntax = "proto3";

package com.flutter.pcsa.rd.actor.event.domain.state.proto;

import "common-state-model/Int32ModelTypes.proto";
import "common-state-model/Int64ModelTypes.proto";
import "common-state-model/DoubleModelTypes.proto";
import "common-state-model/StringModelTypes.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  flat_package: true
};

message RDEventModelProto {
  map<string, string> identifiers = 1;
  map<string, string> eventTypeMappingIds = 2;
  com.flutter.pcsa.common.state.model.proto.OverridableFeedFieldDoubleProto leastMaxBet = 9;
  com.flutter.pcsa.common.state.model.proto.OverridableFeedFieldDoubleProto mostMaxBet = 10;
  com.flutter.pcsa.common.state.model.proto.OverridableFeedFieldDoubleProto layToLose = 11;
  com.flutter.pcsa.common.state.model.proto.OverridableAndInheritableFeedFieldInt32Proto inPlayBettingDelay = 12;
  com.flutter.pcsa.common.state.model.proto.OverridableFeedFieldStringProto sort = 13;
  com.flutter.pcsa.common.state.model.proto.FeedFieldDoubleProto liabilityLimit = 14;
  // GBPE-9044 : PLEASE RESERVE THIS PROPERTY (15, 16, 17), DO NOT DELETE OR REUSE
  com.flutter.pcsa.common.state.model.proto.OverridableFeedFieldStringProto name = 15;
  com.flutter.pcsa.common.state.model.proto.OverridableFeedFieldStringProto status = 16;
  com.flutter.pcsa.common.state.model.proto.OverridableFeedFieldInt64Proto startTime = 17;
}
