package com.flutter.pcsa.rd.actor.event.domain.state.generators

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.state.model.model.HierarchyLinkMetadata
import com.flutter.pcsa.common.state.model.model.generators.GenHierarchyLinkMetadata
import com.flutter.pcsa.rd.actor.event.domain.state.RDEventHierarchyModel
import org.scalacheck.Gen

trait GenRDEventHierarchyModel extends GenHierarchyLinkMetadata {
  lazy val genRDEventHierarchyModel: Gen[RDEventHierarchyModel] = for {
    eventType <- genHierarchyLinkMetadata
    subclass <- genHierarchyLinkMetadata
    superclass <- genHierarchyLinkMetadata
  } yield RDEventHierarchyModel(
    eventType = eventType,
    subclass = subclass,
    superclass = superclass
  )


  lazy val genRDEventHierarchyModelFeedCreated: Gen[RDEventHierarchyModel] = for {
    eventType <- genHierarchyLinkMetadataFeedCreated
    subclass <- genHierarchyLinkMetadataFeedCreated
    superclass <- genHierarchyLinkMetadataFeedCreated
  } yield RDEventHierarchyModel(
    eventType = eventType,
    subclass = subclass,
    superclass = superclass
  )


  lazy val emptyRDEventHierarchyModel: RDEventHierarchyModel = RDEventHierarchyModel(
    eventType = HierarchyLinkMetadata(GbpId.empty, feedCreated = false),
    subclass = HierarchyLinkMetadata(GbpId.empty, feedCreated = false),
    superclass = HierarchyLinkMetadata(GbpId.empty, feedCreated = false)
  )
}
