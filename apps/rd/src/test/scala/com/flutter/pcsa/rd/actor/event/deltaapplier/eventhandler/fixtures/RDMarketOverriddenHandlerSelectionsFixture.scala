package com.flutter.pcsa.rd.actor.event.deltaapplier.eventhandler.fixtures

import com.flutter.pcsa.common.datatypes.EntityIdentifiers
import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.state.model.datatypes.syntax.setters.implicits.setOverrideSyntax.OverridableOps
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.rd.actor.event.RDEventActor.RDMarketOverridden
import com.flutter.pcsa.rd.actor.event.deltaapplier.eventhandler.RDMarketOverriddenHandler
import com.flutter.pcsa.rd.actor.event.domain.dto.{RDMarketOverrideDTO, RDSelectionOverrideDTO}
import com.flutter.pcsa.rd.actor.event.domain.state._
import com.flutter.pcsa.rd.actor.event.domain.state.generators.{GenRDEventState, GenRDMarketOverridden}
import com.flutter.pcsa.rd.common.actor.domain.dto.state.RDGBPIdentifierModel

trait RDMarketOverriddenHandlerSelectionsFixture extends RDMarketOverriddenHandlerFixture with GenRDMarketOverridden with GenRDEventState {
  final val victim: RDMarketOverriddenHandler.type = RDMarketOverriddenHandler

  private def getBaseExpectedStateForSelection(
      rdEventState: RDEventState,
      rdMarketOverrideDTO: RDMarketOverrideDTO,
      fn: RDSelectionModel => RDSelectionModel
    ) = {

    val baseExpectedStateMarket = getBaseExpectedStateForMarket(rdEventState, model => {
      mapMarketOverrideDTOToMarketModel(model, rdMarketOverrideDTO)
    })

    val marketGBPId = RDGBPIdentifierModel(getEntityIdentifiersForMarketDTO.getGbpId.get)
    val selectionGBPId = RDGBPIdentifierModel(getEntityIdentifiersForSelectionDTO.getGbpId.get)
    val markets = baseExpectedStateMarket.aggregateRiskEvent.markets
    val marketAggregationModel = markets.getOrElse(marketGBPId, RDMarketAggregationModel.empty)
    val selectionModel =
      marketAggregationModel.selections.getOrElse(selectionGBPId, EntityMetadata(model = RDSelectionModel.empty, feedCreated = false, hierarchyCreated = false))

    baseExpectedStateMarket.copy(
      aggregateRiskEvent = baseExpectedStateMarket.aggregateRiskEvent.copy(
        markets = markets ++ Map(
          marketGBPId -> marketAggregationModel.copy(
            selections = marketAggregationModel.selections ++ Map(
              selectionGBPId -> selectionModel.applyUpdate(model => {
                val newModel = model.copy(
                  entityIdentifiers = getEntityIdentifiersForSelectionDTO
                )

                fn(newModel)
              })
            )
          )
        )
      )
    )
  }

  def expectedStateSelectionMultipleKey: RDEventActorState => (RDEventState, RDEventState, RDMarketOverrideDTO, RDSelectionOverrideDTO) = actorState => {
    val overridesDTO = getOverridesDTOString
    val rdMarketOverrideDTO = getRDMarketOverrideDTO
    val rdSelectionOverrideDTO = getRDSelectionOverrideDTO.copy(multipleKey = overridesDTO)
    val previousState = getPreviousState(actorState)
    val newState = getBaseExpectedStateForSelection(previousState, rdMarketOverrideDTO, { model =>
      model.copy(
        multipleKey = model.multipleKey.applyOverrideDTO(overridesDTO)
      )
    })

    (previousState, newState, rdMarketOverrideDTO, rdSelectionOverrideDTO)
  }

  def expectedStateSelectionLpMaxBet: RDEventActorState => (RDEventState, RDEventState, RDMarketOverrideDTO, RDSelectionOverrideDTO) = actorState => {
    val overridesDTO = getOverridesDTODouble
    val rdMarketOverrideDTO = getRDMarketOverrideDTO
    val rdSelectionOverrideDTO = getRDSelectionOverrideDTO.copy(lpMaxBet = overridesDTO)
    val previousState = getPreviousState(actorState)
    val newState = getBaseExpectedStateForSelection(previousState, rdMarketOverrideDTO, { model =>
      model.copy(
        lpMaxBet = model.lpMaxBet.applyOverrideDTO(overridesDTO)
      )
    })

    (previousState, newState, rdMarketOverrideDTO, rdSelectionOverrideDTO)
  }

  def expectedStateSelectionForecastStakeLimit: RDEventActorState => (RDEventState, RDEventState, RDMarketOverrideDTO, RDSelectionOverrideDTO) =
    actorState => {
      val overridesDTO = getOverridesDTOInt
      val rdMarketOverrideDTO = getRDMarketOverrideDTO
      val rdSelectionOverrideDTO = getRDSelectionOverrideDTO.copy(forecastStakeLimit = overridesDTO)
      val previousState = getPreviousState(actorState)
      val newState = getBaseExpectedStateForSelection(previousState, rdMarketOverrideDTO, { model =>
        model.copy(
          forecastStakeLimit = model.forecastStakeLimit.applyOverrideDTO(overridesDTO)
        )
      })

      (previousState, newState, rdMarketOverrideDTO, rdSelectionOverrideDTO)
    }

  def expectedStateSelectionRiskInfo: RDEventActorState => (RDEventState, RDEventState, RDMarketOverrideDTO, RDSelectionOverrideDTO) = actorState => {
    val overridesDTO = getOverridesDTOString
    val rdMarketOverrideDTO = getRDMarketOverrideDTO
    val rdSelectionOverrideDTO = getRDSelectionOverrideDTO.copy(riskInfo = overridesDTO)
    val previousState = getPreviousState(actorState)
    val newState = getBaseExpectedStateForSelection(previousState, rdMarketOverrideDTO, { model =>
      model.copy(
        riskInfo = model.riskInfo.applyOverrideDTO(overridesDTO)
      )
    })

    (previousState, newState, rdMarketOverrideDTO, rdSelectionOverrideDTO)
  }

  def getEntityIdentifiersForSelectionDTO: EntityIdentifiers = EntityIdentifiers.createGbpId("urn:sbk:pc:s:gpd:gbpIDSelection")

  def getRDMarketOverrideDTO: RDMarketOverrideDTO = genRDMarketOverrideDTO.sample.get.copy(
    id = getEntityIdentifiersForMarketDTO,
    eventId = getEntityIdentifiersForMarketDTO
  )

  def getRDSelectionOverrideDTO: RDSelectionOverrideDTO = RDSelectionOverrideDTO.empty.copy(
    id = getEntityIdentifiersForSelectionDTO
  )

  def getRDMarketOverridden(
      rdMarketOverrideDTO: RDMarketOverrideDTO = RDMarketOverrideDTO.empty,
      rdSelectionOverrideDTO: RDSelectionOverrideDTO = RDSelectionOverrideDTO.empty
    ): RDMarketOverridden = {
    val rdMarketOverridden = genRDMarketOverridden.sample.get

    rdMarketOverridden.copy(
      marketAgg = rdMarketOverridden.marketAgg.copy(
        market = rdMarketOverrideDTO.copy(
          id = getEntityIdentifiersForMarketDTO
        ),
        selections = if (rdSelectionOverrideDTO.id.getGbpId.isEmpty) {
          Map.empty
        } else {
          Map(GbpId(getEntityIdentifiersForSelectionDTO.getGbpId.get) -> rdSelectionOverrideDTO)
        },
        marketTypeLinkIdentifier = GbpId.empty
      )
    )
  }

  def populateState(actorState: RDEventActorState, rdMarketOverridden: RDMarketOverridden): (RDEventState, RDEventState) = {
    populateState(actorState, rdMarketOverridden, validateSelection = true)
  }
}
