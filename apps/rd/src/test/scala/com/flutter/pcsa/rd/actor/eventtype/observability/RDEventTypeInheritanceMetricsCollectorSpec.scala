package com.flutter.pcsa.rd.actor.eventtype.observability

import com.flutter.observability.profile.{MESSAGE_INHERITANCE_PROFILE, TechnicalMessageProfile}
import com.flutter.pcsa.common.GenCommon
import com.flutter.pcsa.common.state.model.datatypes.OverridableFeedField
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.RDFlushActorEventType
import com.flutter.pcsa.rd.actor.eventtype.domain.state.RDEventTypeState
import com.flutter.pcsa.rd.actor.eventtype.domain.state.generators.{GenRDEventTypeOverridden, GenRDEventTypeState, GenRDMarketTypeLinkOverridden}
import com.flutter.pcsa.rd.actor.eventtype.generators.GenEvents
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper
import org.scalatest.prop.TableDrivenPropertyChecks

class RDEventTypeInheritanceMetricsCollectorSpec extends AnyFlatSpecLike with TableDrivenPropertyChecks {
  trait RDEventTypeInheritanceMetricsCollectorSpecFixture extends RDEventTypeInheritanceMetricsCollector with GenCommon with GenRDEventTypeState with GenEvents with GenRDEventTypeOverridden with GenRDMarketTypeLinkOverridden {
    val eventTypeFeedCommandApplied: RDEventTypeActor.RDEventTypeFeedCommandApplied = genRDEventTypeFeedCommandApplied.sample.get

    val overriddenEventType: RDEventTypeActor.RDEventTypeOverridden = genRDEventTypeOverridden.sample.get

    val overriddenMarketTypeLink: RDEventTypeActor.RDMarketTypeLinkOverridden = genRDMarketTypeLinkOverridden.sample.get

    val anyOtherEvent: RDFlushActorEventType = new RDFlushActorEventType {}

    val state: RDEventTypeState = genRDEventTypeState.sample.get

    val previousStateWithNoChangesInMarketType: Option[RDEventTypeState] = Some(state.copy(eventTypeAggregation =
      state.eventTypeAggregation.copy(eventType = state.eventTypeAggregation.eventType.copy(model =
        state.eventTypeAggregation.eventType.model.copy(inPlayBettingDelay = OverridableFeedField.empty[Int])))))

    val previousStateWithNoChangesInEventType: Option[RDEventTypeState] = Some(state.copy(eventTypeAggregation =
      state.eventTypeAggregation.copy(marketTypeLinks = Map.empty)))

    val previousEmptyState: Option[RDEventTypeState] = None
  }

  "RDEventTypeInheritanceMetricsCollector" should "return all inheritance possible profiles by event if there is no previous state and all settings exist" in new RDEventTypeInheritanceMetricsCollectorSpecFixture {
    val testCases = Table(
      ("event", "expectedProfiles"),
      (
        eventTypeFeedCommandApplied,
        Map(MESSAGE_INHERITANCE_PROFILE -> "%s, %s".format(TechnicalMessageProfile.INHERITANCE_CHANGE_EVENT_TYPE.toString, TechnicalMessageProfile.INHERITANCE_CHANGE_MARKET_TYPE_LINK.toString))
      ),
      (
        overriddenEventType,
        Map(MESSAGE_INHERITANCE_PROFILE -> TechnicalMessageProfile.INHERITANCE_CHANGE_EVENT_TYPE.toString)
      ),
      (
        overriddenMarketTypeLink,
        Map(MESSAGE_INHERITANCE_PROFILE -> TechnicalMessageProfile.INHERITANCE_CHANGE_MARKET_TYPE_LINK.toString)
      ),
      (
        //only with stakeFactor setting in overriddenMarketTypeLink
        overriddenMarketTypeLink.copy(marketTypeLink = overriddenMarketTypeLink.marketTypeLink.copy(inPlayBettingDelay = None)),
        Map(MESSAGE_INHERITANCE_PROFILE -> TechnicalMessageProfile.INHERITANCE_CHANGE_MARKET_TYPE_LINK.toString)
      ),
      (
        //only with birDelay setting in overriddenMarketTypeLink
        overriddenMarketTypeLink.copy(marketTypeLink = overriddenMarketTypeLink.marketTypeLink.copy(stakeFactor = None)),
        Map(MESSAGE_INHERITANCE_PROFILE -> TechnicalMessageProfile.INHERITANCE_CHANGE_MARKET_TYPE_LINK.toString)
      )
    )

    forAll(testCases) { (event, expectedProfiles) =>
      val result = getProfileInheritanceMetricsHeaders(event, state, previousEmptyState)
      result shouldBe expectedProfiles
    }
  }

  it should "not have a INHERITANCE_CHANGE_EVENT_TYPE/INHERITANCE_CHANGE_MARKET_TYPE_LINK if there is no changes in birDelay or stakeFactor caused by RDEventTypeFeedCommandApplied" in new RDEventTypeInheritanceMetricsCollectorSpecFixture {
    val testCases = Table(
      ("event", "previousState", "expectedProfiles"),
      (
        eventTypeFeedCommandApplied,
        previousStateWithNoChangesInMarketType,
        Map(MESSAGE_INHERITANCE_PROFILE -> TechnicalMessageProfile.INHERITANCE_CHANGE_EVENT_TYPE.toString)
      ),
      (
        eventTypeFeedCommandApplied,
        previousStateWithNoChangesInEventType,
        Map(MESSAGE_INHERITANCE_PROFILE -> TechnicalMessageProfile.INHERITANCE_CHANGE_MARKET_TYPE_LINK.toString)
      ),
      (
        eventTypeFeedCommandApplied,
        Some(state),
        Map(MESSAGE_INHERITANCE_PROFILE -> "")
      )
    )

    forAll(testCases) { (event, previousState, expectedProfiles) =>
      val result = getProfileInheritanceMetricsHeaders(event, state, previousState)
      result shouldBe expectedProfiles
    }
  }

  it should "not have a INHERITANCE_CHANGE_EVENT_TYPE/INHERITANCE_CHANGE_MARKET_TYPE_LINK if there is no relevant settings in RDEventTypeOverridden/RDMarketTypeLinkOverridden" in new RDEventTypeInheritanceMetricsCollectorSpecFixture {
    val testCases = Table(
      ("event", "previousState", "expectedProfiles"),
      (
        overriddenEventType.copy(eventType = overriddenEventType.eventType.copy(inPlayBettingDelay = None)),
        Some(state),
        Map(MESSAGE_INHERITANCE_PROFILE -> "")
      ),
      (
        overriddenMarketTypeLink.copy(marketTypeLink = overriddenMarketTypeLink.marketTypeLink.copy(inPlayBettingDelay = None, stakeFactor = None)),
        Some(state),
        Map(MESSAGE_INHERITANCE_PROFILE -> "")
      )
    )

    forAll(testCases) { (event, previousState, expectedProfiles) =>
      val result = getProfileInheritanceMetricsHeaders(event, state, previousState)
      result shouldBe expectedProfiles
    }
  }

  it should "return the correct profiles for anyOtherEvent" in new RDEventTypeInheritanceMetricsCollectorSpecFixture {
    val result = getProfileInheritanceMetricsHeaders(anyOtherEvent, state, previousEmptyState)
    result shouldBe Map(MESSAGE_INHERITANCE_PROFILE -> "")
  }
}
