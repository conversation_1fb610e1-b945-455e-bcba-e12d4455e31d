package com.flutter.pcsa.rd.actor.eventtype.deltaapplier.postprocessor.inheritance.cascadeoncreate

import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.RDEventTypeActorEvent
import com.flutter.pcsa.rd.actor.eventtype.domain.state.RDEventTypeModel
import com.flutter.pcsa.rd.actor.eventtype.domain.state.generators.GenRDMarketTypeLinkModel
import org.mockito.MockitoSugar.mock
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

class RDEventTypeToMarketTypeLinkCascadeOnCreateProcessorSpec extends AnyFlatSpecLike with GenRDMarketTypeLinkModel {
  it should "update MarketTypeLinkC based on EventType - CascadeOnCreate" in {
    val rdEventTypeModel = mock[RDEventTypeModel]
    val rdMarketTypeLinkModel = genRDMarketTypeLinkModel.sample.get
    val actorEvent =  mock[RDEventTypeActorEvent]
    val victim = new RDEventTypeToMarketTypeLinkCascadeOnCreateProcessor
    val result = victim.apply(rdEventTypeModel, rdMarketTypeLinkModel, actorEvent)

    result shouldBe rdMarketTypeLinkModel
  }
}
