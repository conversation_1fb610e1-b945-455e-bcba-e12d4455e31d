package com.flutter.pcsa.rd.actor.subclass.generators.databuilders

import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.rd.actor.subclass.domain.state.{RDSubclassModel, RDSubclassState}
import com.flutter.pcsa.rd.actor.subclass.state.generators.GenRDSubclassState

trait SubclassOnlyDataBuilder extends RDSubclassStateOnlyBuilder {
  lazy val invalidEmptySubclass: EntityMetadata[RDSubclassModel] = invalidEntityMetadata(RDSubclassModel.empty)
  lazy val invalidSubclass: EntityMetadata[RDSubclassModel] = invalidEntityMetadata(genRDSubclassModel.sample.get)

  lazy val validSubclass: EntityMetadata[RDSubclassModel] = invalidSubclass.copy(feedCreated = true, hierarchyCreated = true)

  lazy val invalidStateEmptySubclass: RDSubclassState = getNewState(invalidEmptySubclass)
  lazy val invalidStateSubclass: RDSubclassState = getNewState(invalidSubclass)
  lazy val validStateSubclass: RDSubclassState = getNewState(validSubclass)

  def createSameSubclassManipulated(eventType: EntityMetadata[RDSubclassModel]): EntityMetadata[RDSubclassModel] =
    eventType.applyUpdate(
      _ =>
        genRDSubclassModel.sample.get.copy(
          entityIdentifiers = eventType.model.entityIdentifiers
        )
    )
}

trait RDSubclassStateOnlyBuilder extends GenRDSubclassState {

  def getNewState(
                   subclass: EntityMetadata[RDSubclassModel]
                 ): RDSubclassState = {
    genRDSubclassState(
      subclassAggregation = genRDSubclassAggregationModel.sample.get.copy(
        subclass = subclass
      )
    ).sample.get
  }
}