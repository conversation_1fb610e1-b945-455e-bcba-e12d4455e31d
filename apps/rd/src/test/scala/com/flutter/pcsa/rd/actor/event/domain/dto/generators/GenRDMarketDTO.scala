package com.flutter.pcsa.rd.actor.event.domain.dto.generators

import com.flutter.pcsa.common.datatypes.{EntityIdentifiers, GenPlatform, Platform}
import com.flutter.pcsa.rd.actor.event.domain.dto.RDMarketDTO
import org.scalacheck.Gen

trait GenRDMarketDTO extends GenPlatform {
  lazy val genRDMarketDTO: Gen[RDMarketDTO] = for {
    identifier <- genGbpIdLongFormatWithHL("m")
    entityIdentifiers <- genEntityIdentifiers
    accMin <- genSomeInt
    accMax <- genSomeInt
    forecastStakeFactor <- genSomeDouble
    tricastStakeFactor <- genSomeDouble
    winLP <- genSomeDouble
    placeLP <- genSomeDouble
    leastMaxBet <- genSomeDouble
    mostMaxBet <- genSomeDouble
    guaranteedMinimumLayToLose <- genSomeDouble
    inPlayIgnoreTimeConfig <- genSomeBoolean
    inPlay <- genOptionalBoolean
    inPlayBettingDelay <- genSomeInt
    minForecastStakeLimit <- genSomeInt
    minTricastStakeLimit <- genSomeInt
    stakeFactor <- genSomeDouble
    resulted <- genSomeBoolean
    name <- genSomeString
  } yield RDMarketDTO(
    identifier = identifier,
    EntityIdentifiers(entityIdentifiers.identifiers + (Platform.Gbp -> identifier.asLong)),
    accMin,
    accMax,
    forecastStakeFactor,
    tricastStakeFactor,
    winLP,
    placeLP,
    leastMaxBet,
    mostMaxBet,
    guaranteedMinimumLayToLose,
    inPlayIgnoreTimeConfig,
    inPlay,
    inPlayBettingDelay,
    minForecastStakeLimit,
    minTricastStakeLimit,
    stakeFactor,
    resulted,
    name = name
  )

  lazy val genRDMarketDTOWithOptionalFields: Gen[RDMarketDTO] = for {
    identifier <- genGbpIdLongFormat
    entityIdentifiers <- genEntityIdentifiers
    accMin <- genOptionalInt
    accMax <- genOptionalInt
    forecastStakeFactor <- genOptionalDouble
    tricastStakeFactor <- genOptionalDouble
    winLP <- genOptionalDouble
    placeLP <- genOptionalDouble
    leastMaxBet <- genOptionalDouble
    mostMaxBet <- genOptionalDouble
    guaranteedMinimumLayToLose <- genOptionalDouble
    inPlayIgnoreTimeConfig <- genOptionalBoolean
    inPlay <- genOptionalBoolean
    inPlayBettingDelay <- genOptionalInt
    rule4Deductions <- genOptionalInt
    minForecastStakeLimit <- genOptionalInt
    minTricastStakeLimit <- genOptionalInt
    stakeFactor <- genOptionalDouble
    resulted <- genOptionalBoolean
  } yield RDMarketDTO(
    identifier = identifier,
    entityIdentifiers,
    accMin,
    accMax,
    forecastStakeFactor,
    tricastStakeFactor,
    winLP,
    placeLP,
    leastMaxBet,
    mostMaxBet,
    guaranteedMinimumLayToLose,
    inPlayIgnoreTimeConfig,
    inPlay,
    inPlayBettingDelay,
    minForecastStakeLimit,
    minTricastStakeLimit,
    stakeFactor,
    resulted
  )

}
