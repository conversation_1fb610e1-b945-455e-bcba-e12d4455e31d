package com.flutter.pcsa.rd.actor.event.domain.state.generators

import com.flutter.pcsa.common.GenCommon
import com.flutter.pcsa.rd.actor.event.domain.state.RDMarketTypeLinkContext
import org.scalacheck.Gen

trait GenRDMarketTypeLinkContext extends GenCommon {

  lazy val genRDMarketTypeLinkContext: Gen[RDMarketTypeLinkContext] = genRDMarketTypeLinkContext()

  def genRDMarketTypeLinkContext(
                        genLayToLosePerc: Gen[Map[String, Option[Int]]] = genMapOfN(2, genString, genSomeInt),
                        genLeastMaxBetPerc: Gen[Map[String, Option[Int]]] = genMapOfN(2, genString, genSomeInt),
                        genMostMaxBetPerc: Gen[Map[String, Option[Int]]] = genMapOfN(2, genString, genSomeInt)
                      ): Gen[RDMarketTypeLinkContext] =
    for {
      layToLosePerc <- genLayToLosePerc
      leastMaxBetPerc <- genLeastMaxBetPerc
      mostMaxBetPerc <- genMostMaxBetPerc
    } yield RDMarketTypeLinkContext(
      layToLosePerc = layToLosePerc,
      leastMaxBetPerc = leastMaxBetPerc,
      mostMaxBetPerc = mostMaxBetPerc
    )

  def genRDMarketTypeLinkContextWithOptional(
                                  genLayToLosePerc: Gen[Map[String, Option[Int]]] = genMapOfN(2, genString, genOptionalInt),
                                  genLeastMaxBetPerc: Gen[Map[String, Option[Int]]] = genMapOfN(2, genString, genOptionalInt),
                                  genMostMaxBetPerc: Gen[Map[String, Option[Int]]] = genMapOfN(2, genString, genOptionalInt)
                                ): Gen[RDMarketTypeLinkContext] =
    for {
      layToLosePerc <- genLayToLosePerc
      leastMaxBetPerc <- genLeastMaxBetPerc
      mostMaxBetPerc <- genMostMaxBetPerc
    } yield RDMarketTypeLinkContext(
      layToLosePerc = layToLosePerc,
      leastMaxBetPerc = leastMaxBetPerc,
      mostMaxBetPerc = mostMaxBetPerc
    )

}
