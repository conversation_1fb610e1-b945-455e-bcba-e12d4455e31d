package com.flutter.pcsa.rd.actor.eventtype.domain.dto.serde

import com.flutter.pcsa.rd.actor.eventtype.domain.dto.generators.GenRDEventTypeDTO
import com.flutter.pcsa.rd.actor.eventtype.serde.ProtocolsIso.rdEventTypeDTOIso
import org.scalatest.matchers.should.Matchers
import org.scalatest.propspec.AnyPropSpec
import org.scalatestplus.scalacheck.ScalaCheckDrivenPropertyChecks

class RDEventTypeDTOIsoSpec extends AnyPropSpec with ScalaCheckDrivenPropertyChecks with Matchers with GenRDEventTypeDTO {


  property("Event type data should be isomorphic") {
    forAll(genRDEventTypeDTO) { givenModel =>
      val pb = rdEventTypeDTOIso.get(givenModel)
      val result = rdEventTypeDTOIso.reverseGet(pb)

      givenModel shouldBe result
    }
  }
}
