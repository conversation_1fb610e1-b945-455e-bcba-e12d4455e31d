package com.flutter.pcsa.rd.services.demux

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.kafka.ConsumerMessage.{CommittableMessage, CommittableOffset, CommittableOffsetBatch}
import org.apache.pekko.kafka.scaladsl.Consumer.Control
import org.apache.pekko.kafka.testkit.ConsumerResultFactory.committableOffset
import org.apache.pekko.kafka.{ConsumerMessage, ProducerMessage}
import org.apache.pekko.pattern.AskTimeoutException
import org.apache.pekko.stream.scaladsl.{Flow, Sink, Source}
import org.apache.pekko.{Done, NotUsed}
import cats.data.Ior
import com.flutter.infra.kafka.consumer.StreamKillSwitch
import com.flutter.infra.kafka.streams.kafka.{KafkaMessageInterpreterException, KafkaMessageValidationFailures}
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.common.serialization.StringDeserializer
import org.apache.kafka.common.{Metric, MetricName}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util.concurrent.atomic.AtomicBoolean
import scala.concurrent.Future
import scala.util.control.NoStackTrace

class DemuxStreamSpec extends AnyFlatSpecLike with Matchers {
  "DemuxStream" should "handle messages from demux result" in new StreamFixture  {
    val happyScenario = Seq(message1("success1")(0), message1("success2")(1))
    val (_, control) = probe(happyScenario)

    val msg = control.request(1).expectNext
    msg.records.size shouldBe 1
    msg.records.head.value() shouldBe "success1"
    val msg1 = control.request(1).expectNext
    msg1.records.size shouldBe 1
    msg1.records.head.value() shouldBe "success2"
  }

  it should "handle partial success messages from demux result" in new StreamFixture  {
    val happyScenario = Seq(message1("PartialMessage")(0), message1("success")(1))
    val (_, control) = probe(happyScenario)

    val msg = control.request(1).expectNext
    msg.records.size shouldBe 1
    msg.records.head.value() shouldBe "PartialMessageSuccess"
    val msg1 = control.request(1).expectNext
    msg1.records.size shouldBe 1
    msg1.records.head.value() shouldBe "success"
  }

  it should "restart if an unexpected exception happens" in new StreamFixture {
    val timeoutScenario = Seq(message1("TimeoutException")(0))
    val (_, control) = probe(timeoutScenario)

    control.request(1)
    control.expectError()
    control.expectSubscription()

    val msg1 = control.request(1).expectNext()

    msg1.records.size shouldBe 1
    msg1.records.head.value() shouldBe "TimeoutExceptionRecovered"
  }

  it should "create a stream" in new StreamFixture {

    val victim = new DemuxStream(keyDeserializer, valueDeserializer, mockDemux)(system, config)
    val originalStream = victim.startStreamConsumer()
    originalStream shouldBe a[StreamKillSwitch]
  }

  it should "handle invalid messages from demux result" in new StreamFixture {
    val invalidMessageScenario = Seq(message1("validMessage1")(0),message1("invalidMessage")(1), message1("validMessage2")(2))
    val (_, control) = probe(invalidMessageScenario)
    val validMessage1 = control.request(1).expectNext
    validMessage1.records.size shouldBe 1
    validMessage1.records.head.value() shouldBe "validMessage1"
    val msg = control.request(1).expectNext
    msg.records.size shouldBe 0
    val validMessage2 = control.request(1).expectNext
    validMessage2.records.size shouldBe 1
    validMessage2.records.head.value() shouldBe "validMessage2"

  }

  it should "not consumer if stream is stopped" in new StreamFixture {
    val invalidMessageScenario = Seq(message1("validMessage1")(0),message1("validMessage2")(1), message1("validMessage3")(2))
    val (kill, control) = probe(invalidMessageScenario)
    val validMessage1 = control.request(1).expectNext
    validMessage1.records.size shouldBe 1
    validMessage1.records.head.value() shouldBe "validMessage1"
    val validMessage2 = control.request(1).expectNext
    validMessage2.records.size shouldBe 1
    validMessage2.records.head.value() shouldBe "validMessage2"

    kill.shutdown()
    control.request(1).expectComplete

    control.request(1).expectNoMessage
  }
}
trait DemuxStreamFixture {
  val alreadyFailed = new AtomicBoolean(false)
  private val askTimeoutException = new AskTimeoutException("expected timeout") with NoStackTrace
  val keyDeserializer: StringDeserializer = new StringDeserializer()
  val valueDeserializer: StringDeserializer = new StringDeserializer()
  val mockDemux: ConsumerRecord[String, String] => Ior[KafkaMessageValidationFailures, List[(String, String)]] = {
    case record if record.value().equals("PartialMessage") =>
      Ior.both(KafkaMessageInterpreterException(List("PartialErrorMessage")), List(("49", s"${record.value()}Success")))
    case record if record.value().equals("invalidMessage") => Ior.left(KafkaMessageInterpreterException(List("invalidMessage")))
    case record if record.value().equals("TimeoutException") & !alreadyFailed.get() =>
      alreadyFailed.set(true)
      throw askTimeoutException
    case record if record.value().equals("TimeoutException") & alreadyFailed.get() =>
      alreadyFailed.set(false)
      Ior.right(List(("49", s"${record.value()}Recovered")))
    case record => Ior.right(List(("49", record.value())))
  }

  implicit val config: Config = ConfigFactory.load().getConfig("demux-pbc-service")

  implicit val system = ActorSystem(
    "DemuxStreamActorSystem",
    config
  )

}

trait StreamFixture extends DemuxStreamFixture with PBCDeMultiplexerFixture {
  type Scenario = Seq[CommittableMessage[String, String]]
  type MultiMessage = ProducerMessage.MultiMessage[String, String, ConsumerMessage.CommittableOffset]
  type ScenarioControl = (StreamKillSwitch, RestartableProbe[MultiMessage])

  var topic= "topic"
  var groupId= "groupId"
  var metadata= "metadata"
  var partition = 0
  var offset = 0
  var key1 = "K"
  var value1 = buildEventCase(noMarketTypes("CREATE"))

  // kafka message fixture
  var record: String => ConsumerRecord[String, String] = value => new ConsumerRecord(topic, partition, offset, key1, value)

  // mock CommittableOffset
  var co: Long => ConsumerMessage.CommittableOffset = offset =>
    committableOffset(groupId, topic, partition, offset, metadata)

  var message1: String => Long => CommittableMessage[String, String] = value => offset =>
    CommittableMessage(record(value), co(offset))

  // mock control
  val mockControl = () => new Control {
    override def stop(): Future[Done] = Future.successful(Done)

    override def shutdown(): Future[Done] = Future.successful(Done)

    override def isShutdown: Future[Done] = Future.successful(Done)

    override def metrics: Future[Map[MetricName, Metric]] = Future.successful(Map.empty)
  }

  // mock source
  val source: Scenario => Source[CommittableMessage[String, String], Control] =
    seq => Source(seq).mapMaterializedValue({case _ => mockControl()})

  // build the graph
  def probe(scenario: Scenario): ScenarioControl = {

    val inProbe: RestartableProbe[MultiMessage] = new RestartableProbe[MultiMessage]()

    val victim = new DemuxStream(keyDeserializer, valueDeserializer, mockDemux)(system, config) {
      override protected def sourceConsumerFactory(): Source[CommittableMessage[String, String], Control] = {
        source(scenario)
      }

      override protected def publishFLow: Flow[MultiMessage, CommittableOffset, NotUsed] =
        Flow[ProducerMessage.MultiMessage[String, String, CommittableOffset]]
          .alsoToAll(Sink.fromSubscriber[MultiMessage](inProbe))
          .map(_.passThrough)

      override def batchCommitterFlowSupervised: Flow[CommittableOffset, CommittableOffsetBatch, NotUsed] =
        Flow[ConsumerMessage.CommittableOffset]
          .map(CommittableOffsetBatch.apply)
    }


    val streamKillSwitch = victim.startStreamConsumer()

    (streamKillSwitch, inProbe)
  }



}
