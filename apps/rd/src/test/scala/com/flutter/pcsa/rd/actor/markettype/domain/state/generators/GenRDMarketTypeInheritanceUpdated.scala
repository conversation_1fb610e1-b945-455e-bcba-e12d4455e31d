package com.flutter.pcsa.rd.actor.markettype.domain.state.generators

import com.flutter.baseactor.GenActorState
import com.flutter.pcsa.common.datatypes.gbpid.{GbpIdHierarchyLevel, GenGbpId}
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.RDEventTypeInheritanceUpdated
import com.flutter.pcsa.rd.actor.markettype.RDMarketTypeActor.RDMTLInheritanceUpdated
import com.flutter.pcsa.rd.actor.subclass.domain.subscription.GenRDSubclassUpdateNotification
import com.flutter.pcsa.rd.adapter.gth.MarketGen.genRDEventTypeUpdateMTLNotification
import com.flutter.retryableactor.GenActorMetadata
import org.scalacheck.Gen

trait GenRDMarketTypeInheritanceUpdated extends GenActorState with GenRDSubclassUpdateNotification with GenActorMetadata with GenGbpId{
  lazy val genRDMarketTypeInheritanceUpdated: Gen[RDMTLInheritanceUpdated] = for {
    metadata <- genKafkaMetadata
    identifier <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    headers <- genMap(genString, genString)
    notification <- genRDEventTypeUpdateMTLNotification
    mtlIds <- genNonEmptyList[String](genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink).sample.get.asLong)
  } yield RDMTLInheritanceUpdated(
    metadata = metadata,
    identifier = identifier.asLong,
    mtlIds = mtlIds,
    headers = headers,
    notification = notification
  )
}
