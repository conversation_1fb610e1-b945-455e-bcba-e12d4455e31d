package com.flutter.pcsa.rd.actor.event.domain.dto

import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

class RDMarketFeedCommandSpec extends AnyFlatSpecLike with Matchers {
  "RDMarketAggregationDTO" should "provide an empty entity" in {
    val result = RDMarketFeedCommand.empty

    result.selections shouldBe Map.empty
    result.marketTypeId shouldBe None
    result.market shouldBe RDMarketDTO.empty
    result.action shouldBe RDFeedActionDTO.UNDEFINED

  }
}
