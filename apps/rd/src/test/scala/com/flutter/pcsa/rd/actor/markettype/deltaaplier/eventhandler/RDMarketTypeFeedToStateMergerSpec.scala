package com.flutter.pcsa.rd.actor.markettype.deltaaplier.eventhandler

import com.flutter.pcsa.common.state.model.datatypes.syntax.setters.implicits.setFeedValueSyntax.SetFeedValue
import com.flutter.pcsa.common.state.model.datatypes.{<PERSON>ed<PERSON>ield, OverridableAndInheritableFeedField, OverridableFeedField, OverridableField}
import com.flutter.pcsa.rd.actor.markettype.deltaapplier.eventhandler.eventupdated.RDMarketTypeFeedToStateMerger
import com.flutter.pcsa.rd.actor.markettype.domain.dto.RDMarketTypeDTO
import com.flutter.pcsa.rd.actor.markettype.domain.dto.generators.GenRDMarketTypeDTO
import com.flutter.pcsa.rd.actor.markettype.domain.state.RDMarketTypeModel
import com.flutter.pcsa.rd.actor.markettype.domain.state.generators.GenRDMarketTypeModel
import org.scalatest.GivenWhenThen
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks

class RDMarketTypeFeedToStateMergerSpec
    extends AnyFlatSpecLike with GivenWhenThen with GenRDMarketTypeModel with GenRDMarketTypeDTO with Matchers with TableDrivenPropertyChecks {

 "RDMarketTypeFeedToStateMerger" should "apply all Feed updates to a state" in new RDMarketTypeFeedToStateMerger {

    val randomState: RDMarketTypeModel = genRDMarketTypeModel.sample.get

    val randomEvent = genRDMarketTypeDTO.sample.get

    val table = Table(
      ("description", "state", "update", "expectation"),
      (
        "Should populate an empty state with the new update values",
        RDMarketTypeModel.empty,
        randomEvent,
        RDMarketTypeModel.empty.copy(
          entityIdentifiers = randomEvent.entityIdentifiers,
          marketTypeName = OverridableFeedField.empty[String].updateFeedValue(randomEvent.marketTypeName),
          subclassId = OverridableFeedField.empty[Int].updateFeedValue(randomEvent.subclassId),
          marketSort = OverridableFeedField.empty[String].updateFeedValue(randomEvent.marketSort),
          birIndexStart = OverridableFeedField.empty[Int].updateFeedValue(randomEvent.birIndexStart),
          displayOrder = OverridableFeedField.empty[Int].updateFeedValue(randomEvent.displayOrder),
          layToLosePercentage = FeedField.empty[Int].updateFeedValue(randomEvent.layToLosePercentage),
          leastMaxBetPercentage = FeedField.empty[Int].updateFeedValue(randomEvent.leastMaxBetPercentage),
          mostMaxBetPercentage = FeedField.empty[Int].updateFeedValue(randomEvent.mostMaxBetPercentage),
          liabilityLimitPercentage = FeedField.empty[Int].updateFeedValue(randomEvent.liabilityLimitPercentage),
          name = OverridableFeedField.empty[String].updateFeedValue(randomEvent.name)
        )
      ),
      (
        "Should populate a random state with the new update values",
        randomState,
        randomEvent,
        RDMarketTypeModel.empty.copy(
          entityIdentifiers = merge(randomState.entityIdentifiers, randomEvent.entityIdentifiers),
          marketTypeName = randomState.marketTypeName.updateFeedValue(randomEvent.marketTypeName),
          subclassId = randomState.subclassId.updateFeedValue(randomEvent.subclassId),
          marketSort = randomState.marketSort.updateFeedValue(randomEvent.marketSort),
          birIndexStart = randomState.birIndexStart.updateFeedValue(randomEvent.birIndexStart),
          displayOrder = randomState.displayOrder.updateFeedValue(randomEvent.displayOrder),
          layToLosePercentage = randomState.layToLosePercentage.updateFeedValue(randomEvent.layToLosePercentage),
          leastMaxBetPercentage = randomState.leastMaxBetPercentage.updateFeedValue(randomEvent.leastMaxBetPercentage),
          mostMaxBetPercentage = randomState.mostMaxBetPercentage.updateFeedValue(randomEvent.mostMaxBetPercentage),
          liabilityLimitPercentage = randomState.liabilityLimitPercentage.updateFeedValue(randomEvent.liabilityLimitPercentage),
          name = randomState.name.updateFeedValue(randomEvent.name)
        )
      ),
      (
        "Should maintain values from a random state with an empty update",
        randomState,
        RDMarketTypeDTO.empty,
        randomState
      ),
      (
        "Should maintain an empty state with an empty update",
        RDMarketTypeModel.empty,
        RDMarketTypeDTO.empty,
        RDMarketTypeModel.empty
      )
    )

    forAll(table) { (description, state, update, expectation) =>
      val result = applyFeedUpdate(state, update)
      Then(description)
      result shouldBe expectation
    }
  }

}
