package com.flutter.pcsa.rd.actor.subclass.domain.state.generators

import com.flutter.baseactor.GenActorState
import com.flutter.pcsa.rd.actor.subclass.RDSubclassActor.RDSubclassInheritanceUpdated
import com.flutter.pcsa.rd.actor.superclass.domain.subscription.GenRDSuperclassUpdateNotification
import com.flutter.retryableactor.GenActorMetadata
import org.scalacheck.Gen

trait GenRDSubclassInheritanceUpdated extends GenActorState with GenRDSuperclassUpdateNotification with GenActorMetadata {
  lazy val genRDSubclassInheritanceUpdated: Gen[RDSubclassInheritanceUpdated] = for {
    metadata <- genKafkaMetadata
    identifier <- genString
    headers <- genMap(genString, genString)
    notification <- genRDSuperclassUpdateNotification
  } yield RDSubclassInheritanceUpdated(
    metadata = metadata,
    identifier = identifier,
    headers = headers,
    notification = notification
  )
}
