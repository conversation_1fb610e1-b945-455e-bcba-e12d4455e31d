package com.flutter.pcsa.rd.actor.eventtype.domain.dto.generators

import com.flutter.pcsa.common.datatypes.GenPlatform
import com.flutter.pcsa.rd.actor.eventtype.domain.dto.{RDMarketTypeLinkDTO, RDMarketTypeLinkFeedCommand}
import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import com.flutter.pcsa.rd.common.domain.dto.generators.GenRDFeedActionDTO
import org.scalacheck.Gen

trait GenRDMarketTypeLinkFeedCommand extends GenPlatform with GenRDMarketTypeLinkDTO with GenRDFeedActionDTO {
  lazy val genRDMarketTypeLinkFeedCommand: Gen[RDMarketTypeLinkFeedCommand] = genRDMarketTypeLinkFeedCommand()

  def genRDMarketTypeLinkFeedCommand(
      genRDFeedActionDTO: Gen[RDFeedActionDTO.Value] = genRDFeedActionDTO,
      genRDMarketTypeLinkDTO: Gen[RDMarketTypeLinkDTO] = genRDMarketTypeLinkDTO
    ): Gen[RDMarketTypeLinkFeedCommand] =
    for {
      action <- genRDFeedActionDTO
      marketTypeLink <- genRDMarketTypeLinkDTO
    } yield RDMarketTypeLinkFeedCommand(action = action, marketTypeLink = marketTypeLink)
}
