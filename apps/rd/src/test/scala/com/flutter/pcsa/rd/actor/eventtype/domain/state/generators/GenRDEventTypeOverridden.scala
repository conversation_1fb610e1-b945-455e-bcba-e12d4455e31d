package com.flutter.pcsa.rd.actor.eventtype.domain.state.generators

import com.flutter.baseactor.GenActorState
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.RDEventTypeOverridden
import com.flutter.pcsa.rd.actor.eventtype.domain.dto.generators.GenRDEventTypeOverrideDTO
import com.flutter.pcsa.rd.adapter.gma.GenOverrideDTO
import com.flutter.retryableactor.GenActorMetadata
import org.scalacheck.Gen

trait GenRDEventTypeOverridden extends GenActorState with GenOverrideDTO with GenRDEventTypeOverrideDTO with GenActorMetadata{
  lazy val genRDEventTypeOverridden: Gen[RDEventTypeOverridden] = for {
    metadata <- genKafkaMetadata
    identifier <- genString
    headers <- genMap(genString, genString)
    eventType <- genRDEventTypeOverrideDTO
  } yield RDEventTypeOverridden(
    metadata = metadata,
    identifier = identifier,
    headers = headers,
    eventType = eventType
  )

  lazy val genRDEventTypeOverriddenWithoutOverrides: Gen[RDEventTypeOverridden] = for {
    metadata <- genKafkaMetadata
    identifier <- genString
    headers <- genMap(genString, genString)
    eventType <- genRDEventTypeOverrideWithoutInPlayBettingDelayOverridesDTO
  } yield RDEventTypeOverridden(
    metadata = metadata,
    identifier = identifier,
    headers = headers,
    eventType = eventType
  )
}
