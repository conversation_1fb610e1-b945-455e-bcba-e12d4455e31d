package com.flutter.pcsa.rd.actor.markettype.domain.dto.generators

import com.flutter.pcsa.common.datatypes.GenPlatform
import com.flutter.pcsa.common.datatypes.gbpid.GbpIdHierarchyLevel
import com.flutter.pcsa.rd.actor.markettype.domain.dto.RDMarketTypeLinkDTO
import org.scalacheck.Gen

trait GenRDMarketTypeLinkDTO extends GenPlatform {

  val genRDMarketTypeLinkDTO: Gen[RDMarketTypeLinkDTO] = for {
    identifier <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    entityIdentifiers <- genEntityIdentifiers
    inPlayBettingDelay <- genSomeInt
    stakeFactor <- genSomeDouble
    eventLayToLose <- genSomeInt
    eventLeastMaxBet <- genSomeInt
    eventMostMaxBet <- genSomeInt
    liabilityLimitPerc <- genSomeInt
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    marketTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketType)
  } yield RDMarketTypeLinkDTO(
    identifier = identifier,
    entityIdentifiers = entityIdentifiers,
    stakeFactor = stakeFactor,
    layToLosePerc = eventLayToLose,
    leastMaxBetPerc = eventLeastMaxBet,
    mostMaxBetPerc = eventMostMaxBet,
    inPlayBettingDelay = inPlayBettingDelay,
    liabilityLimitPerc = liabilityLimitPerc,
    eventTypeId = eventTypeId,
    marketTypeId = marketTypeId
  )

  val genRDMarketTypeLinkDTOWithOptionalFields: Gen[RDMarketTypeLinkDTO] = for {
    identifier <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    entityIdentifiers <- genEntityIdentifiers
    inPlayBettingDelay <- genOptionalInt
    stakeFactor <- genOptionalDouble
    eventLayToLose <- genOptionalInt
    eventLeastMaxBet <- genOptionalInt
    eventMostMaxBet <- genOptionalInt
    liabilityLimitPerc <- genOptionalInt
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    marketTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketType)
  } yield RDMarketTypeLinkDTO(
    identifier = identifier,
    entityIdentifiers = entityIdentifiers,
    stakeFactor = stakeFactor,
    layToLosePerc = eventLayToLose,
    leastMaxBetPerc = eventLeastMaxBet,
    mostMaxBetPerc = eventMostMaxBet,
    inPlayBettingDelay = inPlayBettingDelay,
    liabilityLimitPerc = liabilityLimitPerc,
    eventTypeId = eventTypeId,
    marketTypeId = marketTypeId
  )
}
