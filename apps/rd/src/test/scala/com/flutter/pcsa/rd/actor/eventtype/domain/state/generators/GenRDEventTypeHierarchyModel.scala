package com.flutter.pcsa.rd.actor.eventtype.domain.state.generators

import com.flutter.pcsa.common.datatypes.gbpid.GbpId
import com.flutter.pcsa.common.datatypes.GenPlatform
import com.flutter.pcsa.common.state.model.model.HierarchyLinkMetadata
import com.flutter.pcsa.common.state.model.model.generators.GenHierarchyLinkMetadata
import com.flutter.pcsa.rd.actor.eventtype.domain.state.RDEventTypeHierarchyModel
import org.scalacheck.Gen

trait GenRDEventTypeHierarchyModel extends GenPlatform with GenHierarchyLinkMetadata {

  lazy val emptyRDEventTypeHierarchyModel: RDEventTypeHierarchyModel = RDEventTypeHierarchyModel(
    subclass = HierarchyLinkMetadata(GbpId.empty, feedCreated = false),
    superclass = HierarchyLinkMetadata(GbpId.empty, feedCreated = false)
  )

  lazy val genRDEventTypeHierarchyModel: Gen[RDEventTypeHierarchyModel] = for {
    subclass <- genHierarchyLinkMetadata
    superclass <- genHierarchyLinkMetadata
  } yield RDEventTypeHierarchyModel(
    subclass = subclass,
    superclass = superclass
  )

}
