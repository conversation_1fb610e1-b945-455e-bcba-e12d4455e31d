package com.flutter.pcsa.rd.actor.event.domain.dto.generators

import com.flutter.pcsa.rd.actor.event.domain.dto.{RDMarketDTO, RDMarketFeedCommand, RDSelectionFeedCommand}
import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import com.flutter.pcsa.rd.common.domain.dto.generators.GenRDFeedActionDTO
import org.scalacheck.Gen

trait GenRDMarketFeedCommand extends GenRDMarketDTO with GenRDSelectionFeedCommand with GenRDFeedActionDTO {
  lazy val genRDMarketFeedCommand: Gen[RDMarketFeedCommand] = genRDMarketFeedCommand()

  def genRDMarketFeedCommand(
      genRDFeedActionDTO: Gen[RDFeedActionDTO.Value] = genRDFeedActionDTO,
      genMarketTypeId: Gen[Option[String]] = genSomeString,
      genRDMarketDTO: Gen[RDMarketDTO] = genRDMarketDTO,
      genSelections: Gen[List[RDSelectionFeedCommand]] = genNonEmptyListOfN(Gen.choose[Int](1, 3).sample.get, genRDSelectionFeedCommand)
    ) =
    for {
      action <- genRDFeedActionDTO
      marketTypeId <- genMarketTypeId
      market <- genRDMarketDTO
      selections <- genSelections
    } yield RDMarketFeedCommand(action, marketTypeId, market, selections.map(selection => selection.selection.identifier -> selection).toMap)
}
