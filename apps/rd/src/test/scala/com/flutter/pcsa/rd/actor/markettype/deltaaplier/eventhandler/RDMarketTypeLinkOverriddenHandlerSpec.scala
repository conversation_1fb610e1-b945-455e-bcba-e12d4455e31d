package com.flutter.pcsa.rd.actor.markettype.deltaaplier.eventhandler

import com.flutter.pcsa.rd.actor.markettype.deltaaplier.eventhandler.fixture.RDMarketTypeLinkOverriddenHandlerFixture
import com.flutter.pcsa.rd.actor.markettype.domain.state._
import com.flutter.pcsa.rd.actor.markettype.generators.GenRDMarketTypeState
import com.flutter.pcsa.rd.common.deltaapplier.eventhandler.fixtures.OverridableFixture
import org.scalatest.GivenWhenThen
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper
import org.scalatest.prop.TableDrivenPropertyChecks

class RDMarketTypeLinkOverriddenHandlerSpec
    extends AnyFlatSpecLike with GivenWhenThen with TableDrivenPropertyChecks with GenRDMarketTypeState with OverridableFixture
    with RDMarketTypeLinkOverriddenHandlerFixture {
  private val stateCases = Table(
    "state",
    RDMarketTypeNoState,
    genRDMarketTypeState.sample.get
  )

  "RDEventTypeOverriddenHandler" should "test every overridable fields from RDEventTypeModel" in {
    val cases = Table(
      ("field", "expected"),
      (
        "eventLayToLose",
        expectedStateEventLayToLose
      ),
      (
        "eventLeastMaxBet",
        expectedStateEventLeastMaxBet
      ),
      (
        "eventMostMaxBet",
        expectedStateEventMostMaxBet
      ),
      (
        "inPlayBettingDelay",
        expectedStateInPlayBettingDelay
      )
    )

    forAll(cases) { (field, expectedFunction) =>
      forAll(stateCases) { stateType =>
        val (previousState, expectedState, overrideDTO) = expectedFunction.apply(stateType)

        // scalastyle:off
        Given(s"a previous state ${stateType.getClass.getSimpleName}")
        When(s"a RDMarketTypeOverrideDTO with $field with LOCK")
        Then(s"result state contain $field from override $field with LOCK")

        val rdMarketTypeOverridden = getRDMarketTypeLinkOverridden(overrideDTO)

        val result = victim.handle().apply(previousState,rdMarketTypeOverridden).asInstanceOf[RDMarketTypeState]

        result shouldBe expectedState
        // scalastyle:on
      }
    }
  }

  it should "apply the RDMarketOverrideDTO to RDMarketModel to every overridable field and retrieve a state with all fields populated (including not populated fields)" in {
    forAll(stateCases) { stateType =>
      Given(s"a previous state ${stateType.getClass.getSimpleName}")
      When("RDMarketOverrideDTO with all overridable fields filled")
      val rdMarketTypeLinkOverrideDTO = genRDMarketTypeLinkOverrideDTO.sample.get
      val rdMarketTypeLinkOverridden = getRDMarketTypeLinkOverridden(rdMarketTypeLinkOverrideDTO)
      val (previousState, expectedState) = populateState(stateType, rdMarketTypeLinkOverridden)

      Then("give a state with all overridable fields with overrides loaded from RDMarketOverrideDTO")
      val result = victim.handle().apply(previousState, rdMarketTypeLinkOverridden).asInstanceOf[RDMarketTypeState]

      result shouldBe expectedState
    }
  }
}
