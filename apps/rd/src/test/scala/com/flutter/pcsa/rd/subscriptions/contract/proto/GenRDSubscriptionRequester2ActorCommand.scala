package com.flutter.pcsa.rd.subscriptions.contract.proto

import com.flutter.pcsa.common.GenCommon
import com.flutter.pcsa.common.datatypes.gbpid.{GbpIdHierarchyLevel, GenGbpId}
import com.flutter.pcsa.rd.subscriptions.contract.proto.RDSubscriptionInstruction.Command.{SubscribeEventType, SubscribeEventTypeFromMarketTypeLink, SubscribeMarketTypeLink, SubscribeMarketTypeLinkFromMarket, SubscribeSubclass, SubscribeSuperclass, UnsubscribeEventType, UnsubscribeEventTypeFromMarketTypeLink, UnsubscribeMarketTypeLink, UnsubscribeMarketTypeLinkFromMarket, UnsubscribeSubclass, UnsubscribeSuperclass}
import org.scalacheck.Gen

trait GenRDSubscriptionRequester2ActorCommand extends GenCommon with GenGbpId {
  lazy val genRDSubscribeEventTypeProto: Gen[RDSubscriptionInstruction.RDSubscribeEventType] = for {
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    eventId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Event)
  } yield RDSubscriptionInstruction.RDSubscribeEventType(
    eventTypeId = eventTypeId.asLong,
    eventId = eventId.asLong
  )

  lazy val genSubscribeEventType: Gen[SubscribeEventType] = for {
    value <- genRDSubscribeEventTypeProto
  } yield SubscribeEventType(value)

  lazy val genRDUnsubscribeEventTypeProto: Gen[RDSubscriptionInstruction.RDUnsubscribeEventType] = for {
    eventTypeId <- genString
    eventId <- genString
  } yield RDSubscriptionInstruction.RDUnsubscribeEventType(
    eventTypeId = eventTypeId,
    eventId = eventId
  )

  lazy val genUnsubscribeEventType: Gen[UnsubscribeEventType] = for {
    value <- genRDUnsubscribeEventTypeProto
  } yield UnsubscribeEventType(value)

  lazy val genRDSubscribeMarketTypeLinkProto: Gen[RDSubscriptionInstruction.RDSubscribeMarketTypeLink] = for {
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    marketId <- genGbpUrnLongFormatWithHL("m")
    eventTypeId <- genGbpUrnLongFormatWithHL("et")
    eventId <- genGbpUrnLongFormatWithHL("e")
  } yield RDSubscriptionInstruction.RDSubscribeMarketTypeLink(
    marketTypeLinkId = marketTypeLinkId.asLong,
    marketId = marketId,
    eventTypeId = eventTypeId,
    eventId = eventId
  )

  lazy val genSubscribeEventTypeFromMarketTypeLink: Gen[SubscribeEventTypeFromMarketTypeLink] = for {
    value <- genRDSubscribeEventTypeFromMarketTypeLinkProto
  } yield SubscribeEventTypeFromMarketTypeLink(value)

  lazy val genRDSubscribeEventTypeFromMarketTypeLinkProto: Gen[RDSubscriptionInstruction.RDSubscribeEventTypeFromMarketType] = for {
    eventTypeId <- genGbpUrnLongFormatWithHL("EventType")
    marketTypeId <- genGbpUrnLongFormatWithHL("MarketType")
    marketTypeLinkId <- genGbpUrnLongFormatWithHL("MarketTypeLink")
  } yield RDSubscriptionInstruction.RDSubscribeEventTypeFromMarketType(
    eventTypeId = eventTypeId,
    marketTypeId = marketTypeId,
    marketTypeLinkId = marketTypeLinkId
  )

  lazy val genUnsubscribeEventTypeFromMarketTypeLink: Gen[UnsubscribeEventTypeFromMarketTypeLink] = for {
    value <- genRDUnsubscribeEventTypeFromMarketTypeLinkProto
  } yield UnsubscribeEventTypeFromMarketTypeLink(value)

  lazy val genRDUnsubscribeEventTypeFromMarketTypeLinkProto: Gen[RDSubscriptionInstruction.RDUnsubscribeEventTypeFromMarketType] = for {
    eventTypeId <- genGbpIdLongFormat
    marketTypeId <- genGbpIdLongFormat
    marketTypeLinkId <- genGbpIdLongFormat
  } yield RDSubscriptionInstruction.RDUnsubscribeEventTypeFromMarketType(
    eventTypeId = eventTypeId.asLong,
    marketTypeId = marketTypeId.asLong,
    marketTypeLinkId = marketTypeLinkId.asLong
  )

  lazy val genSubscribeMarketTypeLink: Gen[SubscribeMarketTypeLink] = for {
    value <- genRDSubscribeMarketTypeLinkProto
  } yield SubscribeMarketTypeLink(value)

  lazy val genRDUnsubscribeMarketTypeLinkProto: Gen[RDSubscriptionInstruction.RDUnsubscribeMarketTypeLink] = for {
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    marketId <- genString
    eventTypeId <- genString
    eventId <- genString
  } yield RDSubscriptionInstruction.RDUnsubscribeMarketTypeLink(
    marketTypeLinkId = marketTypeLinkId.asLong,
    marketId = marketId,
    eventTypeId = eventTypeId,
    eventId = eventId
  )

  lazy val genUnsubscribeMarketTypeLink: Gen[UnsubscribeMarketTypeLink] = for {
    value <- genRDUnsubscribeMarketTypeLinkProto
  } yield UnsubscribeMarketTypeLink(value)

  lazy val genRDSubscribeMarketTypeLinkFromMarketProto: Gen[RDSubscriptionInstruction.RDSubscribeMarketTypeLinkFromMarket] = for {
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    marketId <- genGbpUrnLongFormatWithHL("m")
    marketTypeId <- genGbpUrnLongFormatWithHL("mt")
    eventId <- genGbpUrnLongFormatWithHL("e")
  } yield RDSubscriptionInstruction.RDSubscribeMarketTypeLinkFromMarket(
    marketTypeLinkId = marketTypeLinkId.asLong,
    marketId = marketId,
    marketTypeId = marketTypeId,
    eventId = eventId
  )

  lazy val genSubscribeMarketTypeLinkFromMarket: Gen[SubscribeMarketTypeLinkFromMarket] = for {
    value <- genRDSubscribeMarketTypeLinkFromMarketProto
  } yield SubscribeMarketTypeLinkFromMarket(value)

  lazy val genRDUnsubscribeMarketTypeLinkFromMarketProto: Gen[RDSubscriptionInstruction.RDUnsubscribeMarketTypeLinkFromMarket] = for {
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    marketId <- genGbpUrnLongFormatWithHL("m")
    marketTypeId <- genGbpUrnLongFormatWithHL("mt")
    eventId <- genGbpUrnLongFormatWithHL("e")
  } yield RDSubscriptionInstruction.RDUnsubscribeMarketTypeLinkFromMarket(
    marketTypeLinkId = marketTypeLinkId.asLong,
    marketId = marketId,
    marketTypeId = marketTypeId,
    eventId = eventId
  )

  lazy val genUnsubscribeMarketTypeLinkFromMarket: Gen[UnsubscribeMarketTypeLinkFromMarket] = for {
    value <- genRDUnsubscribeMarketTypeLinkFromMarketProto
  } yield UnsubscribeMarketTypeLinkFromMarket(value)

  lazy val genRDSubscribeSubclassProto: Gen[RDSubscriptionInstruction.RDSubscribeSubclass] = for {
    eventTypeId <- genString
    subclassId <- genString
  } yield RDSubscriptionInstruction.RDSubscribeSubclass(
    eventTypeId = eventTypeId,
    subclassId = subclassId
  )

  lazy val genSubscribeSubclass: Gen[SubscribeSubclass] = for {
    value <- genRDSubscribeSubclassProto
  } yield SubscribeSubclass(value)

  lazy val genRDUnsubscribeSubclassProto: Gen[RDSubscriptionInstruction.RDUnsubscribeSubclass] = for {
    eventTypeId <- genString
    subclassId <- genString
  } yield RDSubscriptionInstruction.RDUnsubscribeSubclass(
    eventTypeId = eventTypeId,
    subclassId = subclassId
  )

  lazy val genUnsubscribeSubclass: Gen[UnsubscribeSubclass] = for {
    value <- genRDUnsubscribeSubclassProto
  } yield UnsubscribeSubclass(value)

  lazy val genRDSubscribeSuperclassProto: Gen[RDSubscriptionInstruction.RDSubscribeSuperclass] = for {
    superclassId <- genString
    subclassId <- genString
  } yield RDSubscriptionInstruction.RDSubscribeSuperclass(
    superclassId = superclassId,
    subclassId = subclassId
  )

  lazy val genSubscribeSuperclass: Gen[SubscribeSuperclass] = for {
    value <- genRDSubscribeSuperclassProto
  } yield SubscribeSuperclass(value)

  lazy val genRDUnsubscribeSuperclassProto: Gen[RDSubscriptionInstruction.RDUnsubscribeSuperclass] = for {
    superclassId <- genString
    subclassId <- genString
  } yield RDSubscriptionInstruction.RDUnsubscribeSuperclass(
    superclassId = superclassId,
    subclassId = subclassId
  )

  lazy val genUnsubscribeSuperclass: Gen[UnsubscribeSuperclass] = for {
    value <- genRDUnsubscribeSuperclassProto
  } yield UnsubscribeSuperclass(value)

}
