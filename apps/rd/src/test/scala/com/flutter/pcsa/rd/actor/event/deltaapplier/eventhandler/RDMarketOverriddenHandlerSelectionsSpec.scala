package com.flutter.pcsa.rd.actor.event.deltaapplier.eventhandler

import com.flutter.pcsa.rd.actor.event.deltaapplier.eventhandler.fixtures.RDMarketOverriddenHandlerSelectionsFixture
import com.flutter.pcsa.rd.actor.event.domain.dto.RDSelectionOverrideDTO
import com.flutter.pcsa.rd.actor.event.domain.state._
import com.flutter.pcsa.rd.actor.event.domain.state.generators.GenRDMarketOverridden
import org.scalatest.GivenWhenThen
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper
import org.scalatest.prop.TableDrivenPropertyChecks

class RDMarketOverriddenHandlerSelectionsSpec
    extends AnyFlatSpecLike with GivenWhenThen with TableDrivenPropertyChecks with RDMarketOverriddenHandlerSelectionsFixture {

  private val stateCases = Table(
    "state",
    RDEventNoState,
    genRDEventState.sample.get
  )

  "RDMarketOverriddenHandler" should "test every overridable fields from RDSelectionModel" in {
    val cases = Table(
      ("field", "expected"),
      (
        "multipleKey",
        expectedStateSelectionMultipleKey
      ),
      (
        "lpMaxBet",
        expectedStateSelectionLpMaxBet
      ),
      (
        "forecastStakeLimit",
        expectedStateSelectionForecastStakeLimit
      ),
      (
        "riskInfo",
        expectedStateSelectionRiskInfo
      )
    )

    forAll(cases) { (field, expectedFunction) =>
      forAll(stateCases) { stateType =>
        Given(s"a previous state ${stateType.getClass.getSimpleName}")
        When(s"a RDMarketOverrideDTO with $field with LOCK")
        Then(s"result state contain $field from override $field with LOCK")

        val (previousState, expectedState, rdMarketOverrideDTO, rdSelectionOverrideDTO) = expectedFunction.apply(stateType)
        val rdMarketOverridden = getRDMarketOverridden(rdSelectionOverrideDTO = rdSelectionOverrideDTO, rdMarketOverrideDTO = rdMarketOverrideDTO)

        val result = victim.handle().apply(previousState, rdMarketOverridden).asInstanceOf[RDEventState]

        result shouldBe expectedState
      }
    }
  }

  it should "apply the RDSelectionOverrideDTO to RDSelectionModel to every overridable field and retrieve a state with all fields populated (including not populated fields)" in new GenRDMarketOverridden {
    forAll(stateCases) { stateType =>
      Given(s"a previous state ${stateType.getClass.getSimpleName}")
      When("RDMarketOverrideDTO with all overridable fields filled")
      val rdSelectionOverrideDTO: RDSelectionOverrideDTO = genRDSelectionOverrideDTO.sample.get
      val rdMarketOverridden = getRDMarketOverridden(rdSelectionOverrideDTO = rdSelectionOverrideDTO)
      val (previousState, expectedState) = populateState(stateType, rdMarketOverridden)

      Then("give a state with all overridable fields with overrides loaded from RDMarketOverrideDTO")
      val result = victim.handle().apply(previousState, rdMarketOverridden).asInstanceOf[RDEventState]

      result shouldBe expectedState
    }

  }
}
