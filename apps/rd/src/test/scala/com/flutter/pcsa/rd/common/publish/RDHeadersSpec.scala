package com.flutter.pcsa.rd.common.publish

import com.flutter.HOST_NAME
import com.flutter.baseactor.BaseActor.ActorEvent
import com.flutter.headers.{ACTION, ACTOR_EVENT, VERSION}
import com.flutter.pcsa.common.Global.GLOBAL
import com.flutter.pcsa.rd.RD
import com.flutter.product.catalogue.common.Flag.NONE
import com.flutter.product.catalogue.common.IntField
import com.flutter.product.catalogue.common.IntField.IntFieldValue
import com.flutter.product.catalogue.global.risk.GlobalRiskInstruction
import com.flutter.product.catalogue.global.risk.GlobalRiskInstruction.Action.{CLOSE, CREATE, FLUSH, REFRESH, UPDATE}
import com.flutter.publish.Headers
import com.ppb.feeds.event.model.Instruction.Instruction
import org.mockito.MockitoSugar
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import scalapb.UnknownFieldSet

class RDHeadersSpec extends AnyFlatSpec with Matchers with MockitoSugar {

  trait RDHeadersSpecFixture {

    val actorEvent: ActorEvent = mock[ActorEvent]
    val actorHeader: Map[String, AnyRef] = Map.empty
    val sequenceNumber: Int = 2
    val internalSequenceNumber: Int = 3
    val payload: GlobalRiskInstruction = GlobalRiskInstruction(
      action = UPDATE,
      entity = GlobalRiskInstruction.Entity.Event(
        com.flutter.product.catalogue.global.risk.Event(
          id = "gbpId",
          mappedIds = Map.empty,
          inPlayBettingDelay = Some(IntField(Map.empty, Map(GLOBAL -> IntFieldValue(Some(666), NONE, UnknownFieldSet(Map())))))
        )
      )
    )

    val createPayload: GlobalRiskInstruction = payload.copy(action = CREATE)

    val refreshPayload: GlobalRiskInstruction = payload.copy(action = REFRESH)

    val closePayload: GlobalRiskInstruction = payload.copy(action = CLOSE)

    val flushPayload: GlobalRiskInstruction = payload.copy(action = FLUSH)
  }

  val victim = {
    new {} with RDHeaders {
      override def getCurrentTime(): java.lang.Long = 10L
    }
  }

  "RDHeaders" should "extract the payload to a map" in new RDHeadersSpecFixture {
    val result = victim.extractHeaders(actorHeader, sequenceNumber, internalSequenceNumber, payload, actorEvent)

    result.get(Headers.SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(sequenceNumber))
    result.get(Headers.PCSA_INTERNAL_SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(internalSequenceNumber))
    result.get(Headers.EVENT_ID) shouldBe Some("gbpId")
    result.get(Headers.SEQUENCER_HOST) shouldBe Some(HOST_NAME)
    result.get(ACTION(RD.ServiceName)) shouldBe Some(payload.action.toString)
    result.get(ACTOR_EVENT(RD.ServiceName)) shouldBe Some(actorEvent.getClass.getSimpleName)
    result.get(Headers.SERVICE_OUT) shouldBe Some(10)
    result.get(VERSION(RD.ServiceName)) shouldBe Some(RD.DomainContractVersion)
    result.get(Headers.DOMAIN) shouldBe Some("RISK")
    result.contains(Headers.LAST_REFRESHED_DATE) shouldBe false
    result.contains(Headers.LAST_UPDATED_DATE) shouldBe true
    result.contains(Headers.CREATED_DATE) shouldBe false
    result.contains(Headers.CLOSED_DATE) shouldBe false
    result.contains(Headers.RESULTED_DATE) shouldBe false
  }

  it should "extract a payload with a CREATE action to a map" in new RDHeadersSpecFixture {
    val result = victim.extractHeaders(actorHeader, sequenceNumber, internalSequenceNumber, createPayload, actorEvent)

    result.get(Headers.SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(sequenceNumber))
    result.get(Headers.PCSA_INTERNAL_SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(internalSequenceNumber))
    result.get(Headers.EVENT_ID) shouldBe Some("gbpId")
    result.get(Headers.SEQUENCER_HOST) shouldBe Some(HOST_NAME)
    result.get(ACTION(RD.ServiceName)) shouldBe Some(createPayload.action.toString)
    result.get(ACTOR_EVENT(RD.ServiceName)) shouldBe Some(actorEvent.getClass.getSimpleName)
    result.get(Headers.SERVICE_OUT) shouldBe Some(10)
    result.get(VERSION(RD.ServiceName)) shouldBe Some(RD.DomainContractVersion)
    result.get(Headers.DOMAIN) shouldBe Some("RISK")
    result.contains(Headers.LAST_REFRESHED_DATE) shouldBe false
    result.contains(Headers.LAST_UPDATED_DATE) shouldBe false
    result.contains(Headers.CREATED_DATE) shouldBe true
    result.contains(Headers.CLOSED_DATE) shouldBe false
    result.contains(Headers.RESULTED_DATE) shouldBe false
  }

  it should "extract a payload with a CLOSE action to a map" in new RDHeadersSpecFixture {
    val result = victim.extractHeaders(actorHeader, sequenceNumber, internalSequenceNumber, closePayload, actorEvent)

    result.get(Headers.SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(sequenceNumber))
    result.get(Headers.PCSA_INTERNAL_SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(internalSequenceNumber))
    result.get(Headers.EVENT_ID) shouldBe Some("gbpId")
    result.get(Headers.SEQUENCER_HOST) shouldBe Some(HOST_NAME)
    result.get(ACTION(RD.ServiceName)) shouldBe Some(closePayload.action.toString)
    result.get(ACTOR_EVENT(RD.ServiceName)) shouldBe Some(actorEvent.getClass.getSimpleName)
    result.get(Headers.SERVICE_OUT) shouldBe Some(10)
    result.get(VERSION(RD.ServiceName)) shouldBe Some(RD.DomainContractVersion)
    result.get(Headers.DOMAIN) shouldBe Some("RISK")
    result.contains(Headers.LAST_REFRESHED_DATE) shouldBe false
    result.contains(Headers.LAST_UPDATED_DATE) shouldBe false
    result.contains(Headers.CREATED_DATE) shouldBe false
    result.contains(Headers.CLOSED_DATE) shouldBe true
    result.contains(Headers.RESULTED_DATE) shouldBe true
  }

  it should "extract a payload with a REFRESH action to a map" in new RDHeadersSpecFixture {
    val result = victim.extractHeaders(actorHeader, sequenceNumber, internalSequenceNumber, refreshPayload, actorEvent)

    result.get(Headers.SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(sequenceNumber))
    result.get(Headers.PCSA_INTERNAL_SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(internalSequenceNumber))
    result.get(Headers.EVENT_ID) shouldBe Some("gbpId")
    result.get(Headers.SEQUENCER_HOST) shouldBe Some(HOST_NAME)
    result.get(ACTION(RD.ServiceName)) shouldBe Some(refreshPayload.action.toString)
    result.get(ACTOR_EVENT(RD.ServiceName)) shouldBe Some(actorEvent.getClass.getSimpleName)
    result.get(Headers.SERVICE_OUT) shouldBe Some(10)
    result.get(VERSION(RD.ServiceName)) shouldBe Some(RD.DomainContractVersion)
    result.get(Headers.DOMAIN) shouldBe Some("RISK")
    result.contains(Headers.LAST_REFRESHED_DATE) shouldBe true
    result.contains(Headers.LAST_UPDATED_DATE) shouldBe false
    result.contains(Headers.CREATED_DATE) shouldBe false
    result.contains(Headers.CLOSED_DATE) shouldBe false
    result.contains(Headers.RESULTED_DATE) shouldBe false
  }

  it should "extract a payload with a FLUSH action to a map" in new RDHeadersSpecFixture {
    val result = victim.extractHeaders(actorHeader, sequenceNumber, internalSequenceNumber, flushPayload, actorEvent)

    result.get(Headers.SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(sequenceNumber))
    result.get(Headers.PCSA_INTERNAL_SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(internalSequenceNumber))
    result.get(Headers.EVENT_ID) shouldBe Some("gbpId")
    result.get(Headers.SEQUENCER_HOST) shouldBe Some(HOST_NAME)
    result.get(ACTION(RD.ServiceName)) shouldBe Some(flushPayload.action.toString)
    result.get(ACTOR_EVENT(RD.ServiceName)) shouldBe Some(actorEvent.getClass.getSimpleName)
    result.get(Headers.SERVICE_OUT) shouldBe Some(10)
    result.get(VERSION(RD.ServiceName)) shouldBe Some(RD.DomainContractVersion)
    result.get(Headers.DOMAIN) shouldBe Some("RISK")
    result.contains(Headers.LAST_REFRESHED_DATE) shouldBe true
    result.contains(Headers.LAST_UPDATED_DATE) shouldBe false
    result.contains(Headers.CREATED_DATE) shouldBe false
    result.contains(Headers.CLOSED_DATE) shouldBe false
    result.contains(Headers.RESULTED_DATE) shouldBe false
  }

  it should "extract kafka headers correctly" in new RDHeadersSpecFixture {
    val sepAppKey = "sepAppKey"
    val overrideType = Instruction.Action.OVERRIDE
    val sepPayload = Instruction(action = overrideType)
    val result = victim.extractSepHeaders(sequenceNumber, internalSequenceNumber, sepPayload, actorEvent,sepAppKey)

    result.get(Headers.SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(sequenceNumber).toString)
    result.get(Headers.PCSA_INTERNAL_SEQUENCE_NUMBER) shouldBe Some(java.lang.Integer.valueOf(internalSequenceNumber).toString)
    result.get(Headers.SEP_APP_KEY) shouldBe Some(sepAppKey)
    result.get(Headers.SEP_OVERRIDE_TYPE) shouldBe Some("FIELDS")

    result.get(Headers.SEQUENCER_HOST) shouldBe Some(HOST_NAME)
    result.get(ACTION(RD.ServiceName)) shouldBe Some(overrideType.toString)
    result.get(ACTOR_EVENT(RD.ServiceName)) shouldBe Some(actorEvent.getClass.getSimpleName)
    result.get(Headers.SERVICE_OUT) shouldBe Some("10")
    result.get(VERSION(RD.ServiceName)) shouldBe Some(RD.DomainContractVersion)
    result.contains(Headers.LAST_REFRESHED_DATE) shouldBe false
    result.contains(Headers.LAST_UPDATED_DATE) shouldBe false
    result.contains(Headers.CREATED_DATE) shouldBe false
    result.contains(Headers.CLOSED_DATE) shouldBe false
    result.contains(Headers.RESULTED_DATE) shouldBe false


  }
}
