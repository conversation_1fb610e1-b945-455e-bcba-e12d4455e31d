package com.flutter.pcsa.rd.serialization

import com.flutter.pcsa.rd.actor.event.domain.state.generators.{GenRDEventInheritanceUpdated, GenRDEventOverridden, GenRDEventState, GenRDMarketInheritanceUpdated, GenRDMarketOverridden}
import com.flutter.pcsa.rd.actor.event.generators.{GenCommands, GenEventActorBatchBehaviour, GenEvents}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.scalacheck.ScalaCheckPropertyChecks

class RDEventProtobufSerializerSpec extends AnyFlatSpec with Matchers with ScalaCheckPropertyChecks {

  "RDEventProtobufSerializer" should "serialize and deserialize for RDEventFeedCommandApplied" in new RDEventProtobufSerializerFixture {
    forAll(genRDEventFeedCommandApplied) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDEventFeedCommand" in new RDEventProtobufSerializerFixture {
    forAll(genRDEventFeedCommand) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDEventState" in new RDEventProtobufSerializerFixture {
    forAll(genRDEventState) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDBatchInheritanceUpdateEvent" in new RDEventProtobufSerializerFixture {
    forAll(genRDBatchInheritanceUpdateEvent) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDInheritanceUpdateEvent" in new RDEventProtobufSerializerFixture {
    forAll(genRDInheritanceUpdateEvent) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDBatchInheritanceUpdateMarket" in new RDEventProtobufSerializerFixture {
    forAll(genRDBatchInheritanceUpdateMarket) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDInheritanceUpdateMarket" in new RDEventProtobufSerializerFixture {
    forAll(genRDInheritanceUpdateMarket) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDBatchInheritanceUpdateMarketFromMTL" in new RDEventProtobufSerializerFixture {
    forAll(genRDBatchInheritanceUpdateMarketFromMTL) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDInheritanceUpdateMarketFromMTL" in new RDEventProtobufSerializerFixture {
    forAll(genRDInheritanceUpdateMarketFromMTL) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDEventInheritanceUpdated" in new RDEventProtobufSerializerFixture {
    forAll(genRDEventInheritanceUpdated) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDMarketInheritanceUpdated" in new RDEventProtobufSerializerFixture {
    forAll(genRDMarketInheritanceUpdated) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDMarketInheritanceUpdatedFromMTL" in new RDEventProtobufSerializerFixture {
    forAll(genRDMarketInheritanceUpdatedFromMTL) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDEventOverridden" in new RDEventProtobufSerializerFixture {
    forAll(genRDEventOverridden) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }

  it should "serialize and deserialize for RDMarketOverridden" in new RDEventProtobufSerializerFixture {
    forAll(genRDMarketOverridden) { payload =>
      val bytes = serializer.toBinary(payload)

      serializer.fromBinary(bytes, serializer.manifest(payload)) shouldBe payload
    }
  }
}

trait RDEventProtobufSerializerFixture extends GenEvents with GenCommands with GenRDEventState with GenEventActorBatchBehaviour
  with GenRDEventInheritanceUpdated with GenRDMarketInheritanceUpdated with GenRDEventOverridden with GenRDMarketOverridden {
  val serializer = new RDEventProtobufSerializer()
}
