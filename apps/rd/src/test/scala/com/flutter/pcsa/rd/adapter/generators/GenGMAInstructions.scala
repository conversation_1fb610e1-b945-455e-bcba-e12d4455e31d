package com.flutter.pcsa.rd.adapter.generators

import cats.implicits.catsSyntaxOptionId
import com.flutter.pcsa.common.GenCommon
import com.flutter.pcsa.common.datatypes.gbpid.{GbpIdHierarchyLevel, GenGbpId}
import com.flutter.product.catalogue.overrides.Market.{EachwayPlaceTermsOverride, EachwayTerms, Rule4, Rule4ListOverride}
import com.flutter.product.catalogue.overrides.OverrideInstruction.Entity
import com.flutter.product.catalogue.overrides.common.{Action, BoolOverride, DoubleOverride, FractionalPrice, IntOverride, LongOverride, StringOverride}
import com.flutter.product.catalogue.overrides.{Event, EventType, Market, MarketTypeLink, OverrideInstruction, Selection}
import org.scalacheck.Gen

trait GenGMAInstructions extends GenCommon with GenGbpId {

  lazy val genGMAEventTypeInstructionOverride: Gen[OverrideInstruction] = for {
    entity <- genGMAEntityEventTypeOverride
  } yield OverrideInstruction(entity = entity)

  lazy val genGMAEventInstructionOverride: Gen[OverrideInstruction] = for {
    entity <- genGMAEntityEventOverride
  } yield OverrideInstruction(entity = entity)

  lazy val genGMAMarketInstructionOverride: Gen[OverrideInstruction] = for {
    entity <- genGMAEntityMarketOverride
  } yield OverrideInstruction(entity = entity)

  lazy val genGMAMarketTypeLinkInstructionOverride: Gen[OverrideInstruction] = for {
    entity <- genGMAEntityMarketTypeLinkOverride
  } yield OverrideInstruction(entity = entity)

  lazy val genGMAEntityEventTypeOverride: Gen[Entity.EventType] = for {
    eventType <- genGMAEventTypeOverride
  } yield Entity.EventType(eventType)

  lazy val genGMAEntityEventOverride: Gen[Entity.Event] = for {
    event <- genGMAEventOverride
  } yield Entity.Event(event)

  lazy val genGMAEntityMarketOverride: Gen[Entity.Market] = for {
    market <- genGMAMarketOverride
  } yield Entity.Market(market)

  lazy val genGMAEntityMarketTypeLinkOverride: Gen[Entity.MarketTypeLink] = for {
    eventType <- genGMAMarketTypeLinkOverride
  } yield Entity.MarketTypeLink(eventType)

  lazy val genGMAEventTypeOverride: Gen[EventType] = for {
    id <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    subclassId <- genString
    superclassId <- genString
    inPlayBettingDelay <- genNonEmptyMap(genInstance, genGMAIntOverride)
    maxPayout <- genNonEmptyMap(genInstance, genGMALongOverride)
  } yield new EventType(
      id = id.asLong,
      subclassId = subclassId,
      superclassId = superclassId,
      inPlayBettingDelay = inPlayBettingDelay,
      maxPayout = maxPayout
  )

  lazy val genGMAEventOverride: Gen[Event] = for {
    id <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Event)
    inPlayBettingDelay <- genMap(genInstance, genGMAIntOverride)
  } yield new Event(id.asLong, inPlayBettingDelay)

  lazy val genGMAMarketOverride: Gen[Market] = for {
    id <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Market)
    eventId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Event)
    inPlayBettingDelay <- genMap(genInstance, genGMAIntOverride)
    eachwayTerms <- genSomeGMAEachwayTermsOverride
    rule4S <- genMap(genString, genGMARule4ListOverride)
    selections <- Gen.listOfN(2, genGMASelectionOverride)
  } yield new Market(id = id.asLong,
    eventId = eventId.asLong,
    inPlayBettingDelay = inPlayBettingDelay,
    eachwayTerms = eachwayTerms,
    rule4S = rule4S,
    selections = selections)

  lazy val genGMAMarketTypeLinkOverride: Gen[MarketTypeLink] = for {
    id <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    subclassId <- genString
    superclassId <- genString
    inPlayBettingDelay <- genNonEmptyMap(genInstance, genGMAIntOverride)
  } yield new MarketTypeLink(
    id = id.asLong,
    eventTypeId = eventTypeId.asLong,
    subclassId = subclassId,
    superclassId = superclassId,
    inPlayBettingDelay = inPlayBettingDelay
  )

  lazy val genGMASelectionOverride: Gen[Selection] = for {
    id <- genGbpUrnLongFormatWithHL("s")
    name <- genMap(genString, genGMAStringOverride)
  } yield new Selection(id, name)

  lazy val genGMARule4ListOverride: Gen[Rule4ListOverride] = for {
    action <- genGMAOverrideAction
    value <- Gen.listOfN(2, genGMARule4Override)
  } yield new Rule4ListOverride(action, value)

  lazy val genGMARule4Override: Gen[Rule4] = for {
    deduction <- genDouble
    startDate <- genLong
    endDate <- genLong
  } yield new Rule4(deduction, startDate, endDate)

  lazy val genSomeGMAEachwayTermsOverride: Gen[Option[EachwayTerms]] = for {
    withBet <- genMap(genString, genGMABoolOverride)
    available <- genMap(genString, genGMABoolOverride)
    placeTerms <- genMap(genString, genGMAEachwayPlaceTermsOverride)
  } yield new EachwayTerms(withBet, available, placeTerms).some

  lazy val genGMAEachwayPlaceTermsOverride: Gen[EachwayPlaceTermsOverride] = for {
    action <- genGMAOverrideAction
    eachwayFactor <- genSomeGMAFractionalPriceOverride
    ewPlaces <- genInt
  } yield new EachwayPlaceTermsOverride(action, eachwayFactor, ewPlaces)

  lazy val genSomeGMAFractionalPriceOverride: Gen[Option[FractionalPrice]] = for {
    numerator <- genInt
    denominator <- genInt
  } yield new FractionalPrice(numerator, denominator).some

  lazy val genGMABoolOverride: Gen[BoolOverride] = for {
    action <- genGMAOverrideAction
    value <- genSomeBoolean
  } yield new BoolOverride(action, value)

  lazy val genGMAIntOverride: Gen[IntOverride] = for {
    action <- genGMAOverrideAction
    value <- genOptionalInt
  } yield IntOverride(action, value)

  lazy val genGMALongOverride: Gen[LongOverride] = for {
    action <- genGMAOverrideAction
    value <- genOptionalLong
  } yield LongOverride(action, value)

  lazy val genGMAIntPercOverride: Gen[IntOverride] = for {
    action <- genGMAOverrideAction
    value <- genOptionalIntPerc
  } yield IntOverride(action, value)

  lazy val genGMADoubleOverride: Gen[DoubleOverride] = for {
    action <- genGMAOverrideAction
    value <- genOptionalDouble
  } yield DoubleOverride(action, value)

  lazy val genGMASomeDoubleOverride: Gen[DoubleOverride] = for {
    action <- genGMAOverrideAction
    value <- genSomeDouble
  } yield DoubleOverride(action, value)

  lazy val genGMAStringOverride: Gen[StringOverride] = for {
    action <- genGMAOverrideAction
    value <- genOptionString
  } yield StringOverride(action, value)

  // FIXME[JDCR#1] : only covering Happy Path here.
  lazy val genGMAOverrideAction: Gen[Action] = Gen.oneOf(Action.values.filterNot(_.isUnknown).filterNot(_.isUnrecognized))
}
