package com.flutter.pcsa.rd.actor.eventtype.subscriptions.processor

import org.apache.pekko.actor.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ActorRef}
import cats.implicits.catsSyntaxOptionId
import com.flutter.baseactor.behaviour.SubscribeCommands
import com.flutter.baseactor.domain.state.SequenceNumber
import com.flutter.pcsa.common.state.model.model.generators.GenEntityMetadata
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.{RDEventTypeActorEvent, RDEventTypeFeedCommandApplied, RDEventTypeSubscribeCommands, RDFlushActorEventType}
import com.flutter.pcsa.rd.actor.eventtype.domain.state.generators.{GenRDEventTypeModel, GenRDEventTypeState}
import com.flutter.pcsa.rd.actor.eventtype.domain.state.{RDEventTypeActorState, RDEventTypeAggregationModel, RDEventTypeModel, RDEventTypeNoState, RDEventTypeState}
import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import com.flutter.pcsa.rd.common.subscriptions.notifications.ImplicitInheritanceOps._
import com.flutter.pcsa.rd.subscription.notifications.contract.common.proto.IntField
import com.flutter.pcsa.rd.subscription.notifications.contract.proto.RDEventTypeUpdateNotification
import com.flutter.pcsa.rd.subscription.notifications.contract.proto.RDSubscriptionNotificationInstruction.Notification
import com.flutter.pcsa.rd.subscription.notifications.contract.proto.RDSubscriptionNotificationInstruction.Notification.EventTypeUpdateNotification
import com.flutter.pcsa.rd.subscriptions.contract.proto.GenRDSubscriptionNotification2ActorCommand
import com.flutter.subscriptions.actor.processor.ExtractedNotification
import com.typesafe.config.Config
import com.flutter.subscriptions.actor.notifier.SubscribersPublisher
import org.mockito.MockitoSugar
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.{TableDrivenPropertyChecks, TableFor2}

class RDEventTypeSubscriptionProcessorSpec
    extends AnyFlatSpecLike with MockitoSugar with Matchers with TableDrivenPropertyChecks with GenRDEventTypeModel with GenEntityMetadata
    with GenRDEventTypeState with GenRDSubscriptionNotification2ActorCommand {

  trait RDEventTypeSubscriptionProcessorBuilder {

    lazy val mockedActorContext = mock[ActorContext]
    lazy val mockedSubscriptionNotifierPublisher = mock[SubscribersPublisher[EventTypeUpdateNotification]]
    lazy val mockedSubscriptionShardActorConfig = mock[Config]
    lazy val mockedActorRef = mock[ActorRef]
    lazy val mockedActorPath = mock[ActorPath]
    lazy val mockedActorName = "I will be back"
    lazy val mockedValidCommand = mock[RDEventTypeSubscribeCommands]
    lazy val randomEventTypeModel: RDEventTypeModel = genRDEventTypeModel.sample.get
    lazy val mockedInvalidEventTypeAggregationModel: RDEventTypeAggregationModel = mock[RDEventTypeAggregationModel]
    lazy val mockedValidEventTypeAggregationModel: RDEventTypeAggregationModel = mock[RDEventTypeAggregationModel]
    lazy val stateWithInvalidEventType =
      RDEventTypeState(lastPublishedNo = SequenceNumber(0), inboundSeqNo = SequenceNumber(0), eventTypeAggregation = mockedInvalidEventTypeAggregationModel, metadata = Map.empty)
    lazy val stateWithValidEventType =
      RDEventTypeState(lastPublishedNo = SequenceNumber(0), inboundSeqNo = SequenceNumber(0), eventTypeAggregation = mockedValidEventTypeAggregationModel, metadata = Map.empty)
    lazy val previousStateWithInvalidEventType: RDEventTypeState = genRDEventTypeState(mockedInvalidEventTypeAggregationModel).sample.get
    lazy val previousStateWithValidEventType: RDEventTypeState = genRDEventTypeState(mockedValidEventTypeAggregationModel).sample.get

    when(mockedInvalidEventTypeAggregationModel.eventType).thenReturn(invalidEntityMetadata(randomEventTypeModel))
    when(mockedValidEventTypeAggregationModel.eventType).thenReturn(validEntityMetadata(randomEventTypeModel))

    when(mockedActorContext.self).thenReturn(mockedActorRef)
    when(mockedActorRef.path).thenReturn(mockedActorPath)
    when(mockedActorPath.name).thenReturn(mockedActorName)

    lazy val victim = new RDEventTypeSubscriptionProcessor(mockedActorContext, mockedSubscriptionNotifierPublisher, mockedSubscriptionShardActorConfig)
  }

  it should "validate if incoming commands are the expected commands" in new RDEventTypeSubscriptionProcessorBuilder {
    lazy val mockedInvalidCommand = mock[SubscribeCommands]
    victim.ensureItIsTheExpectedTypeOfSubscribeCommands() shouldBe false
    victim.ensureItIsTheExpectedTypeOfSubscribeCommands(mockedInvalidCommand) shouldBe false

    victim.ensureItIsTheExpectedTypeOfSubscribeCommands(mockedValidCommand) shouldBe true

    verify(mockedActorContext, times(1)).self
    verifyNoMoreInteractions(mockedActorContext, mockedInvalidCommand, mockedValidCommand)
  }

  it should "return actor name from actorContext on invocation" in new RDEventTypeSubscriptionProcessorBuilder {
    victim.actorName(mockedValidCommand) shouldBe mockedActorName

    verify(mockedActorContext, times(1)).self
    verifyNoMoreInteractions(mockedActorContext, mockedValidCommand)
  }

  it should "extract Subscription Data and return as an EventType Notification" in new RDEventTypeSubscriptionProcessorBuilder {

    val table: TableFor2[RDEventTypeActorState, Option[EventTypeUpdateNotification]] = Table(
      ("value", "expectations"),
      (
        stateWithValidEventType,
        EventTypeUpdateNotification(
          RDEventTypeUpdateNotification(
            inPlayBettingDelay = randomEventTypeModel.inPlayBettingDelay
          )
        ).some
      ),
      (
        stateWithInvalidEventType,
        None
      ),
      (
        RDEventTypeNoState,
        None
      )
    )

    forAll(table) { (value, expectation) =>
      victim.buildSubscriptionNotification(value, mockedValidCommand) shouldBe expectation
    }

    verify(mockedActorContext, times(1)).self
    verify(mockedInvalidEventTypeAggregationModel, times(1)).eventType
    verify(mockedValidEventTypeAggregationModel, times(1)).eventType
    verifyNoMoreInteractions(mockedActorContext, mockedValidCommand, mockedInvalidEventTypeAggregationModel, mockedValidEventTypeAggregationModel)

  }

  it should "extract notifications from an EventType delta state and event" in new RDEventTypeSubscriptionProcessorBuilder {
    lazy val mockedEventTypeCommandApplied: RDEventTypeFeedCommandApplied = mock[RDEventTypeFeedCommandApplied]
    lazy val mockedRefreshActorEvent: RDFlushActorEventType = mock[RDFlushActorEventType]
    lazy val mockedActorEvent: RDEventTypeActorEvent = mock[RDEventTypeActorEvent]
    lazy val mockedEventHeaders: Map[String, String] = Map("header" -> "someRealValue", "header2" -> "anotherRealValue")

    when(mockedActorEvent.headers).thenReturn(mockedEventHeaders)

    when(mockedEventTypeCommandApplied.headers).thenReturn(mockedEventHeaders)
    when(mockedEventTypeCommandApplied.action).thenReturn(RDFeedActionDTO.REFRESH)


    when(mockedRefreshActorEvent.headers).thenReturn(mockedEventHeaders)

    val table = Table(
      ("previousState", "state", "event", "expectation"),
      (
        previousStateWithInvalidEventType,
        stateWithValidEventType,
        mockedActorEvent,
        List(
          ExtractedNotification(
            mockedActorName,
            EventTypeUpdateNotification(
              RDEventTypeUpdateNotification(
                inPlayBettingDelay = randomEventTypeModel.inPlayBettingDelay
              )
            ),
            mockedEventHeaders
          )
        )
      ),
      (
        previousStateWithValidEventType,
        stateWithValidEventType,
        mockedActorEvent,
        List.empty
      ),
      (
        RDEventTypeNoState,
        stateWithValidEventType,
        mockedActorEvent,
        List(
          ExtractedNotification(
            mockedActorName,
            EventTypeUpdateNotification(
              RDEventTypeUpdateNotification(
                inPlayBettingDelay = randomEventTypeModel.inPlayBettingDelay
              )
            ),
            mockedEventHeaders
          )
        )
      ),
      (
        previousStateWithInvalidEventType,
        stateWithInvalidEventType,
        mockedActorEvent,
        List.empty
      ),
      (
        RDEventTypeNoState,
        stateWithInvalidEventType,
        mockedActorEvent,
        List.empty
      ),
      (
        RDEventTypeNoState,
        RDEventTypeNoState,
        mockedActorEvent,
        List.empty
      ),
      (
        RDEventTypeNoState,
        RDEventTypeNoState,
        mockedRefreshActorEvent,
        List.empty
      ),
      (
        RDEventTypeNoState,
        stateWithInvalidEventType,
        mockedRefreshActorEvent,
        List.empty
      ),
      (
        previousStateWithInvalidEventType,
        stateWithInvalidEventType,
        mockedRefreshActorEvent,
        List.empty
      ),
      (
        RDEventTypeNoState,
        stateWithValidEventType,
        mockedRefreshActorEvent,
        List(
          ExtractedNotification(
            mockedActorName,
            EventTypeUpdateNotification(
              RDEventTypeUpdateNotification(
                inPlayBettingDelay = randomEventTypeModel.inPlayBettingDelay
              )
            ),
            mockedEventHeaders
          )
        )
      ),
      (
        previousStateWithInvalidEventType,
        stateWithValidEventType,
        mockedRefreshActorEvent,
        List(
          ExtractedNotification(
            mockedActorName,
            EventTypeUpdateNotification(
              RDEventTypeUpdateNotification(
                inPlayBettingDelay = randomEventTypeModel.inPlayBettingDelay
              )
            ),
            mockedEventHeaders
          )
        )
      ),
      (
        RDEventTypeNoState,
        RDEventTypeNoState,
        mockedEventTypeCommandApplied,
        List.empty
      ),
      (
        RDEventTypeNoState,
        stateWithInvalidEventType,
        mockedEventTypeCommandApplied,
        List.empty
      ),
      (
        previousStateWithInvalidEventType,
        stateWithInvalidEventType,
        mockedEventTypeCommandApplied,
        List.empty
      ),
      (
        RDEventTypeNoState,
        stateWithValidEventType,
        mockedEventTypeCommandApplied,
        List(
          ExtractedNotification(
            mockedActorName,
            EventTypeUpdateNotification(
              RDEventTypeUpdateNotification(
                inPlayBettingDelay = randomEventTypeModel.inPlayBettingDelay
              )
            ),
            mockedEventHeaders
          )
        )
      ),
      (
        previousStateWithInvalidEventType,
        stateWithValidEventType,
        mockedEventTypeCommandApplied,
        List(
          ExtractedNotification(
            mockedActorName,
            EventTypeUpdateNotification(
              RDEventTypeUpdateNotification(
                inPlayBettingDelay = randomEventTypeModel.inPlayBettingDelay
              )
            ),
            mockedEventHeaders
          )
        )
      )
    )

    forAll(table) { (previousState, state, event, expectation) =>
      val result = victim.generateNotifications(previousState, state, event)
      result shouldBe expectation
    }

    verify(mockedActorContext, times(1)).self
    verify(mockedInvalidEventTypeAggregationModel, times(8)).eventType
    verify(mockedActorEvent, times(2)).headers
    verify(mockedValidEventTypeAggregationModel, times(8)).eventType
    verifyNoMoreInteractions(
      mockedActorContext,
      mockedValidCommand,
      mockedInvalidEventTypeAggregationModel,
      mockedActorEvent,
      mockedValidEventTypeAggregationModel
    )
  }

  it should "correctly generate a delta notification" in new RDEventTypeSubscriptionProcessorBuilder {
    val emptyNotification: EventTypeUpdateNotification = Notification.EventTypeUpdateNotification(RDEventTypeUpdateNotification())
    val someFilledNotification1: EventTypeUpdateNotification = genEventTypeUpdateNotification.sample.get
    val someFilledNotification2: EventTypeUpdateNotification = genEventTypeUpdateNotification.sample.get

    val someEmptyNotification: EventTypeUpdateNotification = genEventTypeUpdateNotification(
      genRDEventTypeUpdateNotification.sample.get.copy(
        inPlayBettingDelay = Some(IntField(Map.empty))
      )
    ).sample.get

    val table = Table(
      ("previousStateNotification", "newStateNotification", "expectation"),
      (emptyNotification, someFilledNotification1, Some(someFilledNotification1)),
      (someEmptyNotification, someFilledNotification1, Some(someFilledNotification1)),
      (someFilledNotification1, someFilledNotification2, Some(someFilledNotification2)),
      (someFilledNotification1, someEmptyNotification, Some(someEmptyNotification))
    )

    forAll(table) { (previousStateNotification, newStateNotification, expectation) =>
      {
        val result = victim.deltaNotification(previousStateNotification, newStateNotification)

        result shouldBe expectation
      }
    }

  }

}
