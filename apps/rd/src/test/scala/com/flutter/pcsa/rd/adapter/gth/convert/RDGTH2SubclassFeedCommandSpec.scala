package com.flutter.pcsa.rd.adapter.gth.convert

import cats.data.Ior
import com.flutter.adapter.InboundMetadata
import com.flutter.infra.kafka.streams.kafka.{KafkaMessageInterpreterException, KafkaMessageValidationFailures}
import com.flutter.pcsa.common.datatypes.gbpid.{GbpId, GbpIdHierarchyLevel}
import com.flutter.pcsa.rd.actor.subclass.RDSubclassActor.RDSubclassFeedCommand
import com.flutter.pcsa.rd.actor.subclass.domain.dto.RDSubclassDTO
import com.flutter.pcsa.rd.actor.subclass.domain.dto.generators.GenRDSubclassDTO
import com.flutter.pcsa.rd.adapter.generators.GenGTHInstructions
import com.flutter.pcsa.rd.adapter.gth.GTHMessage
import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import com.flutter.retryableactor.GenActorMetadata
import com.flutter.retryableactor.metadata.Metadata.KafkaMetadata
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.mockito.MockitoSugar.mock
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

class RDGTH2SubclassFeedCommandSpec extends AnyFlatSpecLike with Matchers {

  trait RDGTH2SubclassFeedCommandFixture extends RDGTH2FeedCommand with RDGTH2SubclassFeedCommand with GenActorMetadata with GenGTHInstructions with GenRDSubclassDTO {
    lazy val getProviderIdIorMock: InboundMetadata[GTHMessage] => Ior[KafkaMessageValidationFailures, Int] = mock[InboundMetadata[GTHMessage] => Ior[KafkaMessageValidationFailures, Int]]
    when(getProviderIdIorMock.apply(any[InboundMetadata[GTHMessage]])).thenReturn(Ior.right(1))
    lazy val toGBPIdIorMock: (Int, GbpIdHierarchyLevel.Level) => Ior[KafkaMessageValidationFailures, GbpId] = mock[(Int, GbpIdHierarchyLevel.Level) => Ior[KafkaMessageValidationFailures, GbpId]]
    when(toGBPIdIorMock.apply(any[Int], any[GbpIdHierarchyLevel.Level])).thenReturn(Ior.right(genGbpIdLongFormat.sample.get))
    lazy val getFeedActionDTOIorMock: InboundMetadata[GTHMessage] => Ior[KafkaMessageValidationFailures, RDFeedActionDTO.Value] = mock[InboundMetadata[GTHMessage] => Ior[KafkaMessageValidationFailures, RDFeedActionDTO.Value]]
    when(getFeedActionDTOIorMock.apply(any[InboundMetadata[GTHMessage]])).thenReturn(Ior.right(RDFeedActionDTO.UPDATE))
    lazy val toSubclassDTOMock: (String, GbpId, GTHSubclassInstruction) => Ior[KafkaMessageValidationFailures, RDSubclassDTO] = mock[(String, GbpId, GTHSubclassInstruction) => Ior[KafkaMessageValidationFailures, RDSubclassDTO]]
    when(toSubclassDTOMock.apply(any[String], any[GbpId], any[GTHSubclassInstruction])).thenReturn(Ior.right(genRDSubclassDTO.sample.get))

    override def getProviderIdIor(inbound: InboundMetadata[GTHMessage]): Ior[KafkaMessageValidationFailures, Int] = getProviderIdIorMock(inbound)

    override def toGBPIDIor(providerId: Int, entityContext: GbpIdHierarchyLevel.Level): Ior[KafkaMessageValidationFailures, GbpId] = toGBPIdIorMock(providerId, entityContext)

    override def getFeedActionDTOIor(inbound: InboundMetadata[GTHMessage]): Ior[KafkaMessageValidationFailures, RDFeedActionDTO.Value] = getFeedActionDTOIorMock(inbound)

    override def toSubclassDTO(providerName: String, gbpSubclassId: GbpId, gthSubclass: GTHSubclassInstruction): Ior[KafkaMessageValidationFailures, RDSubclassDTO] = toSubclassDTOMock(providerName, gbpSubclassId, gthSubclass)

    lazy val inboundMock = mock[InboundMetadata[GTHMessage]]
    lazy val gthMessageMock = mock[GTHMessage]
    lazy val gthSubclassInstruction = genGTHSubclassInstruction.sample.get
    when(gthMessageMock.payload).thenReturn(gthSubclassInstruction)
    when(gthMessageMock.headers).thenReturn(Map.empty)
    lazy val inboundMetadataMock = genInboundKafkaMetadata.sample.get
    when(inboundMock.metadata).thenReturn(inboundMetadataMock)
    when(inboundMock.instruction).thenReturn(gthMessageMock)

    val victim: (InboundMetadata[GTHMessage], GTHSubclassInstruction) => Ior[KafkaMessageValidationFailures, RDSubclassFeedCommand] = innerSubclassConverter
  }

  it should "fail when providerIdIor returns KafkaMessageValidationFailure and everything else works as intended" in new RDGTH2SubclassFeedCommandFixture {
    when(getProviderIdIorMock.apply(any[InboundMetadata[GTHMessage]])).thenReturn(Ior.left(KafkaMessageInterpreterException(List("ProviderId Missing"))))

    val result = victim(inboundMock, gthSubclassInstruction)

    result.isLeft shouldBe true
  }

  it should "fail when toGBPIdIor returns KafkaMessageValidationFailure and everything else works as intended" in new RDGTH2SubclassFeedCommandFixture {
    when(toGBPIdIorMock.apply(any[Int], any[GbpIdHierarchyLevel.Level])).thenReturn(Ior.left(KafkaMessageInterpreterException(List("error"))))
    val result = victim(inboundMock, gthSubclassInstruction)

    result.isLeft shouldBe true
  }

  it should "fail when getFeedActionDTOIor returns KafkaMessageValidationFailure and everything else works as intended" in new RDGTH2SubclassFeedCommandFixture {
    when(getFeedActionDTOIorMock.apply(any[InboundMetadata[GTHMessage]])).thenReturn(Ior.left(KafkaMessageInterpreterException(List("error"))))

    val result = victim(inboundMock, gthSubclassInstruction)

    result.isLeft shouldBe true
  }

  it should "fail when toSubclassDTO returns KafkaMessageValidationFailure and everything else works as intended" in new RDGTH2SubclassFeedCommandFixture {
    when(toSubclassDTOMock.apply(any[String], any[GbpId], any[GTHSubclassInstruction])).thenReturn(Ior.left(KafkaMessageInterpreterException(List("error"))))

    val result = victim(inboundMock, gthSubclassInstruction)

    result.isLeft shouldBe true
  }

  it should "correctly convert to ActorCommand when everything works correctly" in new RDGTH2SubclassFeedCommandFixture {
    val providerId = 1
    val gbpIdentifierString = s"urn:GBP:Subclass:GTH:$providerId"
    val gbpIdentifier = GbpId(gbpIdentifierString)
    val rdFeedActionDTO = RDFeedActionDTO.UPDATE
    val generatedDTO = genRDSubclassDTO.sample.get.copy(identifier = gbpIdentifier)

    when(getProviderIdIorMock.apply(inboundMock)).thenReturn(Ior.right(providerId))
    when(toGBPIdIorMock.apply(providerId, GbpIdHierarchyLevel.Subclass)).thenReturn(Ior.right(gbpIdentifier))
    when(getFeedActionDTOIorMock.apply(inboundMock)).thenReturn(Ior.right(rdFeedActionDTO))
    when(toSubclassDTOMock.apply(any[String], any[GbpId], any[GTHSubclassInstruction])).thenReturn(Ior.right(generatedDTO))

    val result = victim(inboundMock, gthSubclassInstruction)

    result.isRight shouldBe true
    val right = result.right.get
    right.subclass shouldBe generatedDTO
    right.action shouldBe rdFeedActionDTO
    right.metadata shouldBe KafkaMetadata(
      inboundMock.metadata.topic, inboundMock.metadata.partition, inboundMock.metadata.offset
    )
    right.headers shouldBe inboundMock.instruction.headers
  }
}