package com.flutter.pcsa.rd.actor.event.deltaapplier.postprocessor.inheritance.cascadeoncreate

import com.flutter.pcsa.rd.actor.event.RDEventActor.RDEventActorEvent
import com.flutter.pcsa.rd.actor.event.domain.state.RDEventModel
import com.flutter.pcsa.rd.actor.event.domain.state.generators.GenRDMarketModel
import org.mockito.MockitoSugar.mock
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

class RDEventToMarketCascadeOnCreateProcessorSpec extends AnyFlatSpecLike with GenRDMarketModel {
  it should "update Market based on Event - CascadeOnCreate" in {
    val rdEventModel = mock[RDEventModel]
    val rdMarketModel = genRDMarketModel.sample.get
    val actorEvent =  mock[RDEventActorEvent]
    val victim = new RDEventToMarketCascadeOnCreateProcessor
    val result = victim.apply(rdEventModel, rdMarketModel, actorEvent)

    result shouldBe rdMarketModel
  }
}
