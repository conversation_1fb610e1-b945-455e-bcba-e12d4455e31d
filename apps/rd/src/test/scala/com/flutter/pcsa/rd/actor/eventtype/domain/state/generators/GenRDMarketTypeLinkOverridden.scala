package com.flutter.pcsa.rd.actor.eventtype.domain.state.generators

import com.flutter.baseactor.GenActorState
import com.flutter.pcsa.common.datatypes.GenPlatform
import com.flutter.pcsa.overrides.generators.GenOverrideDTO
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.RDMarketTypeLinkOverridden
import com.flutter.pcsa.rd.actor.eventtype.domain.dto.generators.GenRDMarketTypeLinkOverrideDTO
import com.flutter.retryableactor.GenActorMetadata
import org.scalacheck.Gen

trait GenRDMarketTypeLinkOverridden extends GenActorState with GenOverrideDTO with GenPlatform with GenRDMarketTypeLinkOverrideDTO with GenActorMetadata{
  lazy val genRDMarketTypeLinkOverridden: Gen[RDMarketTypeLinkOverridden] = for {
    metadata <- genKafkaMetadata
    identifier <- genString
    headers <- genMap(genString, genString)
    marketTypeLink <- genRDMarketTypeLinkOverrideDTO
  } yield RDMarketTypeLinkOverridden(
    metadata = metadata,
    identifier = identifier,
    headers = headers,
    marketTypeLink = marketTypeLink
  )
}
