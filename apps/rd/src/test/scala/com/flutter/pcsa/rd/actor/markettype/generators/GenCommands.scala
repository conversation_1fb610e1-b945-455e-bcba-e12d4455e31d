package com.flutter.pcsa.rd.actor.markettype.generators

import com.flutter.headers.{ROOT_SOURCE_TIMESTAMP, SOURCE_TIMESTAMP}
import com.flutter.pcsa.common.GenCommon
import com.flutter.pcsa.common.datatypes.EntityIdentifiers
import com.flutter.pcsa.common.datatypes.gbpid.{GbpId, GbpIdHierarchyLevel}
import com.flutter.pcsa.rd.RD
import com.flutter.pcsa.rd.actor.event.domain.subscription.GenRDEventTypeUpdateMTLNotification
import com.flutter.pcsa.rd.actor.eventtype.RDEventTypeActor.{RDSubscribeEventTypeFromMarketType, RDSubscribeMarketTypeLink, RDUnsubscribeEventTypeFromMarketType}
import com.flutter.pcsa.rd.actor.markettype.RDMarketTypeActor.{RDBatchInheritanceUpdateMTL, RDInheritanceUpdateMTL, RDMTLInheritanceUpdated, RDMarketTypeFeedCommand, RDMarketTypeLinkFeedCommand, RDMarketTypeLinkOverride, RDMarketTypeOverride, RDSubscribeMarketTypeLinkFromMarket, RDUnsubscribeMarketTypeLinkFromMarket}
import com.flutter.pcsa.rd.actor.markettype.domain.dto.generators.{GenRDMarketTypeDTO, GenRDMarketTypeLinkDTO, GenRDMarketTypeLinkOverrideDTO}
import com.flutter.pcsa.rd.common.domain.dto.generators.GenRDFeedActionDTO
import com.flutter.pcsa.rd.subscription.notifications.contract.proto.RDEventTypeUpdateMTLNotification
import com.flutter.retryableactor.GenActorMetadata
import org.scalacheck.Gen


trait GenCommands extends GenCommon with GenRDFeedActionDTO with GenActorMetadata with GenRDMarketTypeDTO with GenRDMarketTypeLinkDTO with GenRDMarketTypeOverrideDTO with GenRDMarketTypeLinkOverrideDTO with GenRDEventTypeUpdateMTLNotification{

  val genRDMarketTypeFeedCommand: Gen[RDMarketTypeFeedCommand] = for {
    metadata <- genKafkaMetadata
    headers <- genMap(genString, genString)
    rootSourceTimestamp <- genLong
    sourceTimestamp <- genLong
    action <- genRDFeedActionDTO
    marketType <- genRDMarketTypeDTO
  } yield {
    RDMarketTypeFeedCommand(
      metadata = metadata,
      headers = headers ++
        Map(
          ROOT_SOURCE_TIMESTAMP -> rootSourceTimestamp.toString,
          SOURCE_TIMESTAMP(RD.ServiceName) -> sourceTimestamp.toString
        ),
      action = action,
      marketType = marketType
    )
  }

  val genRDMarketTypeOverride: Gen[RDMarketTypeOverride] = for {
    eventTypeId <- genString
    headers <- genMap(genString, genString)
    marketTypeDTO <- genRDMarketTypeOverrideDTO
    metadata <- genKafkaMetadata
  } yield {
    RDMarketTypeOverride(
      eventTypeId = eventTypeId,
      marketTypeOverrideDTO = marketTypeDTO,
      headers = headers,
      metadata = metadata
    )
  }

  val genRDMarketTypeLinkFeedCommand: Gen[RDMarketTypeLinkFeedCommand] = for {
    metadata <- genKafkaMetadata
    headers <- genMap(genString, genString)
    rootSourceTimestamp <- genLong
    sourceTimestamp <- genLong
    action <- genRDFeedActionDTO
    marketTypeLink <- genRDMarketTypeLinkDTO
  } yield {
    RDMarketTypeLinkFeedCommand(
      metadata = metadata,
      headers = headers ++
        Map(
          ROOT_SOURCE_TIMESTAMP -> rootSourceTimestamp.toString,
          SOURCE_TIMESTAMP(RD.ServiceName) -> sourceTimestamp.toString
        ),
      action = action,
      marketTypeLink = marketTypeLink
    )
  }

  val genRDMarketTypeLinkOverride: Gen[RDMarketTypeLinkOverride] = for {
    marketTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketType)
    headers <- genMap(genString, genString)
    marketTypeLinkOverrideDTO <- genRDMarketTypeLinkOverrideDTO
    metadata <- genKafkaMetadata
  } yield {
    RDMarketTypeLinkOverride(
      marketTypeId = marketTypeId,
      marketTypeLinkOverrideDTO = marketTypeLinkOverrideDTO,
      headers = headers,
      metadata = metadata
    )
  }

  val genRDInheritanceUpdateMTL: Gen[RDInheritanceUpdateMTL] = for {
    identifier <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketType)
    headers <- genMap(genString, genString)
    mtlIds <- genNonEmptyList[String](genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink).sample.get.asLong)
    notification <- genRDEventTypeUpdateMTLNotification
    metadata <- genKafkaMetadata
  } yield {
    RDInheritanceUpdateMTL(
      identifier = identifier.asLong,
      mtlIds = mtlIds,
      notification = notification,
      headers = headers,
      metadata = metadata
    )
  }

  lazy val genRDSubscribeEventTypeFromMarketType: Gen[RDSubscribeEventTypeFromMarketType] = for {
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    marketId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketType)
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    headers <- genMap(genString, genString)
    metadata <- genKafkaMetadata
  } yield {
    RDSubscribeEventTypeFromMarketType(
      eventTypeId = eventTypeId.asLong,
      marketTypeId = marketId.asLong,
      marketTypeLinkId = marketTypeLinkId.asLong,
      headers = headers,
      metadata = metadata
    )
  }

  lazy val genRDUnsubscribeEventTypeFromMarketType: Gen[RDUnsubscribeEventTypeFromMarketType] = for {
    eventTypeId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.EventType)
    marketId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketType)
    marketTypeLinkId <- genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink)
    headers <- genMap(genString, genString)
    metadata <- genKafkaMetadata
  } yield {
    RDUnsubscribeEventTypeFromMarketType(
      eventTypeId = eventTypeId.asLong,
      marketTypeId = marketId.asLong,
      marketTypeLinkId = marketTypeLinkId.asLong,
      headers = headers,
      metadata = metadata
    )
  }

  lazy val genRDBatchInheritanceUpdateMTL: Gen[RDBatchInheritanceUpdateMTL] = for {
    identifiers <- genNonEmptyList[String](genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.MarketTypeLink).sample.get.asLong)
    headers <- genMap(genString, genString)
    notification <- genRDEventTypeUpdateMTLNotification
    metadata <- genKafkaMetadata
  } yield {
    RDBatchInheritanceUpdateMTL(
      metadata = metadata,
      identifiers = identifiers,
      notification = notification,
      headers = headers
    )
  }

  lazy val genRDMTLInheritanceUpdated: Gen[RDMTLInheritanceUpdated] = for {
      identifier <- genString
      mtlIds <- genNonEmptyList[String](genString)
      headers <- genMap(genString, genString)
      notification <- genRDEventTypeUpdateMTLNotification
      metadata <- genKafkaMetadata
  } yield {
      RDMTLInheritanceUpdated(
        metadata = metadata,
        identifier = identifier,
        mtlIds = mtlIds,
        headers = headers,
        notification = notification
      )
  }

  lazy val genRDSubscribeMarketTypeLinkFromMarket: Gen[RDSubscribeMarketTypeLinkFromMarket] = for {
    marketTypeId <- genGbpIdLongFormatWithHL("mt")
    eventId <- genGbpIdLongFormatWithHL("e")
    marketId <- genGbpIdLongFormatWithHL("m")
    marketTypeLinkId <- genString
    headers <- genMap(genString, genString)
    metadata <- genKafkaMetadata
  } yield {
    RDSubscribeMarketTypeLinkFromMarket(
      marketTypeId = marketTypeId.asLong,
      eventId = eventId.asLong,
      marketId = marketId.asLong,
      marketTypeLinkId = marketTypeLinkId,
      headers = headers,
      metadata = metadata
    )
  }

  lazy val genRDUnsubscribeMarketTypeLinkFromMarket: Gen[RDUnsubscribeMarketTypeLinkFromMarket] = for {
    marketTypeId <- genGbpIdLongFormatWithHL("mt")
    eventId <- genGbpIdLongFormatWithHL("e")
    marketId <- genGbpIdLongFormatWithHL("m")
    marketTypeLinkId <- genString
    headers <- genMap(genString, genString)
    metadata <- genKafkaMetadata
  } yield {
    RDUnsubscribeMarketTypeLinkFromMarket(
      marketTypeId = marketTypeId.asLong,
      eventId = eventId.asLong,
      marketId = marketId.asLong,
      marketTypeLinkId = marketTypeLinkId,
      headers = headers,
      metadata = metadata
    )
  }
}
