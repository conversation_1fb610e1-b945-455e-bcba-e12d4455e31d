package com.flutter.pcsa.rd.adapter.pbc.convert

import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

class RDPBC2FeedActionDTOSpec extends AnyFlatSpecLike with Matchers {

  trait RDPBC2FeedActionDTOBuilder {
    lazy val victim = new RDPBC2FeedActionDTO {}
  }

  it should "return a correct mapping of action String to RDFeedActionDTO" in new RDPBC2FeedActionDTOBuilder {
    victim.toFeedActionDTO("CREATE") shouldBe RDFeedActionDTO.CREATE
    victim.toFeedActionDTO("UPDATE") shouldBe RDFeedActionDTO.UPDATE
    victim.toFeedActionDTO("REFRESH") shouldBe RDFeedActionDTO.REFRESH
    victim.toFeedActionDTO("LINK") shouldBe RDFeedActionDTO.CREATE
  }

  it should "return a default mapping of unmatched case of action String to RDFeedActionDTO" in new RDPBC2FeedActionDTOBuilder {
    victim.toFeedActionDTO("Random") shouldBe RDFeedActionDTO.UPDATE
  }

}
