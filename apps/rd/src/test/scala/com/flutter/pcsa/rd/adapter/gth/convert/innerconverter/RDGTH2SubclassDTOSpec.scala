package com.flutter.pcsa.rd.adapter.gth.convert.innerconverter

import cats.data.Ior
import com.flutter.infra.kafka.streams.kafka.KafkaMessageValidationFailures
import com.flutter.pcsa.common.datatypes.gbpid.{GbpId, GbpIdHierarchyLevel}
import com.flutter.pcsa.rd.actor.subclass.domain.dto.RDSubclassDTO
import com.flutter.pcsa.rd.actor.subclass.domain.dto.generators.GenRDSubclassDTO
import com.flutter.pcsa.rd.adapter.generators.GenGTHInstructions
import com.flutter.pcsa.rd.adapter.gth.convert.GTHSubclassInstruction
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

class RDGTH2SubclassDTOSpec extends AnyFlatSpecLike with Matchers {

  trait RDGTH2SubclassDTOFixture extends RDGTH2SubclassDTO with GenGTHInstructions with GenRDSubclassDTO {
    val victim: (String, GbpId, GTHSubclassInstruction) => Ior[KafkaMessageValidationFailures, RDSubclassDTO] = toSubclassDTO
  }

  it should "fail when SuperclassId is not present in SubclassInstruction" in new RDGTH2SubclassDTOFixture {
    private val instruction = genGTHSubclassInstruction.sample.get.copy(superclassId = None)
    private val result = victim("ANY", genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Subclass).sample.get, instruction)

    result.isLeft shouldBe true
  }

  it should "successfully convert to subclassDTO" in new RDGTH2SubclassDTOFixture {
    private val subclassGbpId = genGbpIdLongFormatWithHL(GbpIdHierarchyLevel.Subclass).sample.get
    private val instruction = genGTHSubclassInstruction.sample.get
    private val superclassGbpId =
      GbpId(s"urn:POWERS_FEED:${instruction.superclassId.get.toString}", GbpIdHierarchyLevel.Superclass)

    val expectation: Ior[KafkaMessageValidationFailures, RDSubclassDTO] =
      Ior.right(
        RDSubclassDTO.empty.copy(
          subclassGbpId,
          superclassGbpId,
          name = instruction.subclassName
        )
      )

    private val result = victim("POWERS_FEED", subclassGbpId, instruction)

    result shouldBe expectation
  }
}