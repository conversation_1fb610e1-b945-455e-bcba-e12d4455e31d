package com.flutter.pcsa.rd.actor.markettype.domain.state.generators

import com.flutter.pcsa.common.datatypes.GenPlatform
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.common.state.model.model.generators.GenEntityMetadata
import com.flutter.pcsa.rd.actor.event.domain.state.generators.GenRDGBPIdentifierModel
import com.flutter.pcsa.rd.actor.markettype.domain.state.{RDMarketTypeAggregationModel, RDMarketTypeModel}
import org.scalacheck.Gen

trait GenRDMarketTypeAggregationModel
    extends GenPlatform with GenRDMarketTypeModel with GenRDGBPIdentifierModel with GenEntityMetadata {

  lazy val genRDMarketTypeAggregationModel: Gen[RDMarketTypeAggregationModel] = genRDMarketTypeAggregationModel()

  lazy val emptyRDMarketTypeAggregationModel: RDMarketTypeAggregationModel = RDMarketTypeAggregationModel(
    marketType = invalidEntityMetadata(RDMarketTypeModel.empty),
    marketTypeLinks = Map.empty
  )

  def genRDMarketTypeAggregationModel(
      marketType: Gen[EntityMetadata[RDMarketTypeModel]] = invalidEntityMetadata(genRDMarketTypeModel)
    ): Gen[RDMarketTypeAggregationModel] =
    for {
      marketType <- marketType
    } yield RDMarketTypeAggregationModel(
      marketType = marketType,
      marketTypeLinks = Map.empty
    )
}
