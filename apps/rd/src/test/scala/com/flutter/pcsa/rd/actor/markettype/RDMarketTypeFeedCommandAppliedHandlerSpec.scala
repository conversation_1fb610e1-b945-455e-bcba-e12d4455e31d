package com.flutter.pcsa.rd.actor.markettype

import com.flutter.pcsa.common.datatypes.EntityIdentifiers
import com.flutter.pcsa.common.eventupdated.EntityIdentifiersMerger
import com.flutter.pcsa.common.state.model.model.EntityMetadata
import com.flutter.pcsa.rd.actor.markettype.RDMarketTypeActor.{RDMarketTypeFeedCommandApplied, RDMarketTypeLinkFeedCommandApplied}
import com.flutter.pcsa.rd.actor.markettype.deltaapplier.eventhandler.RDMarketTypeFeedCommandAppliedHandler
import com.flutter.pcsa.rd.actor.markettype.deltaapplier.eventhandler.RDMarketTypeFeedCommandAppliedHandler.applyFeedUpdate
import com.flutter.pcsa.rd.actor.markettype.domain.dto.{RDMarketTypeDTO, RDMarketTypeLinkDTO}
import com.flutter.pcsa.rd.actor.markettype.domain.dto.generators.GenRDMarketTypeDTO
import com.flutter.pcsa.rd.actor.markettype.domain.state.{RDMarketTypeActorState, RDMarketTypeAggregationModel, RDMarketTypeNoState, RDMarketTypeState}
import com.flutter.pcsa.rd.actor.markettype.generators.GenRDMarketTypeState
import com.flutter.pcsa.rd.actor.markettype.generators.GenEvents
import com.flutter.pcsa.rd.common.actor.domain.dto.RDFeedActionDTO
import com.flutter.pcsa.rd.common.domain.dto.generators.GenRDFeedActionDTO
import com.flutter.retryableactor.GenActorMetadata
import com.flutter.retryableactor.metadata.Metadata.KafkaMetadata
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks

class RDMarketTypeFeedCommandAppliedHandlerSpec
    extends AnyFlatSpecLike with Matchers with GenRDMarketTypeState with GenRDMarketTypeDTO with GenRDFeedActionDTO with GenEvents with GenActorMetadata
    with TableDrivenPropertyChecks {

  trait RDMarketTypeFeedCommandAppliedHandlerFixture extends EntityIdentifiersMerger {
    lazy val sampledState = genRDMarketTypeState.sample.get
    lazy val randomState = genRDMarketTypeState.sample.get

    lazy val randomMarketTypeDTO = genRDMarketTypeDTO.sample.get

    lazy val randomCreateEvent = genRDMarketTypeFeedCommandApplied.sample.get.copy(action = RDFeedActionDTO.CREATE)
    lazy val randomRefreshEvent = genRDMarketTypeFeedCommandApplied.sample.get.copy(action = RDFeedActionDTO.REFRESH)
    lazy val randomUpdateEvent = genRDMarketTypeFeedCommandApplied.sample.get.copy(action = RDFeedActionDTO.UPDATE)
    lazy val emptyEvent = RDMarketTypeFeedCommandApplied(
      action = RDFeedActionDTO.UPDATE,
      marketType = RDMarketTypeDTO.empty,
      metadata = KafkaMetadata.empty,
      headers = Map.empty
    )

    def expectedState(state: RDMarketTypeState, event: RDMarketTypeFeedCommandApplied, expectedFeedCreated: Boolean): RDMarketTypeActorState = {
      val updateEntityIdentifiers = {
        if (event.marketType.identifier.isEmpty) EntityIdentifiers.empty
        else EntityIdentifiers.createGbpId(event.marketType.identifier.asLong)
      }
      RDMarketTypeState(
        lastPublishedNo = state.lastPublishedNo,
        inboundSeqNo = state.inboundSeqNo + 1,
        marketTypeAggregationModel = RDMarketTypeAggregationModel.empty.copy(
          marketType = EntityMetadata(state.marketTypeAggregationModel.marketType.model, feedCreated = expectedFeedCreated, hierarchyCreated = false)
            .applyUpdate(applyFeedUpdate(_, event.marketType))
        )
      )
    }
  }

  "RDMarketTypeFeedCommandAppliedHandler" should "apply feed commands to a state" in new RDMarketTypeFeedCommandAppliedHandlerFixture {

    val cases = Table(
      ("description", "state", "update", "expectation"),
      (
        "maintain no state on an empty update",
        RDMarketTypeNoState,
        emptyEvent,
        expectedState(emptyRDMarketTypeState, emptyEvent, expectedFeedCreated = false)
      ),
      (
        "maintain a random state on an empty update",
        randomState,
        emptyEvent,
        expectedState(randomState, emptyEvent, expectedFeedCreated = false)
      ),
      (
        "update a random state on Update command",
        randomState,
        randomUpdateEvent,
        expectedState(randomState, randomUpdateEvent, expectedFeedCreated = false)
      ),
      (
        "update no state on CREATE command, feedCreated should be true",
        RDMarketTypeNoState,
        randomCreateEvent,
        expectedState(emptyRDMarketTypeState, randomCreateEvent, expectedFeedCreated = true)
      ),
      (
        "update empty state on CREATE command, feedCreated should be true",
        emptyRDMarketTypeState,
        randomCreateEvent,
        expectedState(emptyRDMarketTypeState, randomCreateEvent, expectedFeedCreated = true)
      ),
      (
        "update no state on REFRESH command, feedCreated should be true",
        RDMarketTypeNoState,
        randomRefreshEvent,
        expectedState(emptyRDMarketTypeState, randomRefreshEvent, expectedFeedCreated = true)
      ),
      (
        "update empty state on REFRESH command, feedCreated should be true",
        emptyRDMarketTypeState,
        randomRefreshEvent,
        expectedState(emptyRDMarketTypeState, randomRefreshEvent, expectedFeedCreated = true)
      ),
      (
        "update no state on UPDATE command, feedCreated should be false",
        RDMarketTypeNoState,
        randomUpdateEvent,
        expectedState(emptyRDMarketTypeState, randomUpdateEvent, expectedFeedCreated = false)
      ),
      (
        "update empty state on UPDATE command, feedCreated should be false",
        emptyRDMarketTypeState,
        randomUpdateEvent,
        expectedState(emptyRDMarketTypeState, randomUpdateEvent, expectedFeedCreated = false)
      ),
      (
        "update random state on CREATE command, feedCreated should be true",
        randomState,
        randomCreateEvent,
        expectedState(randomState, randomCreateEvent, expectedFeedCreated = true)
      ),
      (
        "update random state on REFRESH command, feedCreated should be true",
        randomState,
        randomRefreshEvent,
        expectedState(randomState, randomRefreshEvent, expectedFeedCreated = true)
      ),
      (
        "update random state on UPDATE command, feedCreated should be false",
        randomState,
        randomUpdateEvent,
        expectedState(randomState, randomUpdateEvent, expectedFeedCreated = false)
      )
    )

    val victim = RDMarketTypeFeedCommandAppliedHandler.handle()
    forAll(cases)((description, state, event, expectation) => {
      val result = victim(state, event)
      result shouldBe expectation
    })
  }

  it should "apply feed commands of MarketTypeLink" in new RDMarketTypeFeedCommandAppliedHandlerFixture {

    val victim = RDMarketTypeFeedCommandAppliedHandler.handle()
    val state = RDMarketTypeNoState
    val event = genRDMarketTypeLinkFeedCommandApplied.sample.get

    val result: RDMarketTypeActorState = victim(state,event)
    result.asInstanceOf[RDMarketTypeState].marketTypeAggregationModel.marketTypeLinks.size shouldBe 1
  }

  it should "apply feed commands of MarketTypeLink without substitution of exiting" in new RDMarketTypeFeedCommandAppliedHandlerFixture {

    val victim = RDMarketTypeFeedCommandAppliedHandler.handle()
    val state = genRDMarketTypeState.sample.get
    val event = genRDMarketTypeLinkFeedCommandApplied.sample.get.copy(marketTypeLink = RDMarketTypeLinkDTO.empty)

    val result: RDMarketTypeActorState = victim(state,event)
    result.asInstanceOf[RDMarketTypeState].marketTypeAggregationModel.marketTypeLinks.size shouldBe 0
  }



}
